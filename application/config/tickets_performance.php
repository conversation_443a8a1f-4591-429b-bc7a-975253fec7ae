<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
|--------------------------------------------------------------------------
| Tickets Performance Configuration
|--------------------------------------------------------------------------
|
| This file contains configuration options for optimizing ticket performance
| when dealing with large datasets (10,000+ tickets)
|
*/

/*
|--------------------------------------------------------------------------
| Use Optimized View
|--------------------------------------------------------------------------
|
| Set to TRUE to use the optimized DataTables-based view for better performance
| Set to FALSE to use the original view (not recommended for large datasets)
|
*/
$config['tickets_use_optimized_view'] = TRUE;

/*
|--------------------------------------------------------------------------
| DataTables Configuration
|--------------------------------------------------------------------------
|
| Configuration options for DataTables when using optimized view
|
*/
$config['tickets_datatable'] = [
    'default_page_length' => 25,
    'page_length_options' => [10, 25, 50, 100],
    'enable_state_save' => TRUE,
    'enable_responsive' => TRUE,
    'defer_render' => TRUE,
    'processing_message' => 'Loading tickets...'
];

/*
|--------------------------------------------------------------------------
| Performance Thresholds
|--------------------------------------------------------------------------
|
| Define thresholds for automatic optimization
|
*/
$config['tickets_performance'] = [
    // Automatically switch to optimized view when ticket count exceeds this
    'auto_optimize_threshold' => 1000,
    
    // Cache status counts for this many seconds
    'status_count_cache_duration' => 300, // 5 minutes
    
    // Maximum records to load without pagination
    'max_records_without_pagination' => 500
];

/*
|--------------------------------------------------------------------------
| Database Optimization
|--------------------------------------------------------------------------
|
| Settings for database query optimization
|
*/
$config['tickets_db_optimization'] = [
    // Use database aggregation for counts instead of PHP loops
    'use_db_aggregation' => TRUE,
    
    // Enable query caching
    'enable_query_cache' => TRUE,
    
    // Limit for search queries
    'search_result_limit' => 1000
];
