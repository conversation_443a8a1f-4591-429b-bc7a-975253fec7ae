<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Developer Aptitude Test</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.9.1/font/bootstrap-icons.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-image: linear-gradient(135deg, rgba(18, 98, 62, 0.05), rgba(36, 174, 97, 0.05));
            min-height: 100vh;
            font-size: 16px;
        }

        @media (max-width: 576px) {
            body {
                font-size: 14px;
            }
        }

        :root {
            --primary: #12623e;
            --secondary: #24ae61;
            --gradient-start: #12623e;
            --gradient-end: #24ae61;
        }

        .custom-container {
            max-width: 850px;
            margin: 1rem auto;
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 5px 30px rgba(18, 98, 62, 0.1);
        }

        @media (max-width: 576px) {
            .custom-container {
                padding: 1rem;
                margin: 0.5rem auto;
                border-radius: 8px;
            }
        }

        .gradient-heading {
            position: relative;
            padding-bottom: 15px;
            padding-top: 15px;
            font-weight: 600;
            font-size: 1.65rem;
        }

        @media (max-width: 576px) {
            .gradient-heading {
                font-size: 1.5rem;
                padding-bottom: 10px;
            }

            h2 {
                font-size: 1.3rem;
            }
        }

        .gradient-heading::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            height: 3px;
            width: 60px;
            background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
            border-radius: 3px;
        }

        .custom-btn {
            background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
            border: none;
            color: white;
            border-radius: 50px;
            padding: 0.7rem 1.8rem;
            font-weight: 500;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(18, 98, 62, 0.2);
            transition: all 0.3s;
            font-size: 0.95rem;
            width: auto;
        }

        @media (max-width: 576px) {
            .custom-btn {
                padding: 0.6rem 1.5rem;
                font-size: 0.9rem;
                width: 100%;
                margin-top: 1rem;
            }
        }

        .custom-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(18, 98, 62, 0.3);
            color: white;
        }

        .custom-btn-submit {
            background: linear-gradient(90deg, var(--gradient-end), var(--gradient-start));
        }

        .custom-btn-submit:hover {
            background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary);
            box-shadow: 0 0 0 0.25rem rgba(36, 174, 97, 0.25);
        }

        .form-control, .form-select {
            font-size: 0.95rem;
            padding: 0.5rem 0.75rem;
        }

        @media (max-width: 576px) {
            .form-control, .form-select {
                font-size: 0.95rem;
                padding: 0.5rem 0.75rem;
                height: auto;
            }

            .form-label {
                font-size: 0.9rem;
                margin-bottom: 0.25rem;
            }
        }

        .question-card {
            opacity: 0;
            transition: opacity 0.5s ease;
            border-radius: 10px;
            border: 1px solid rgba(18, 98, 62, 0.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.04);
            position: relative;
            padding: 1.25rem;
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        @media (max-width: 576px) {
            .question-card {
                padding: 1rem;
                margin-bottom: 1rem;
                border-radius: 8px;
            }
        }

        .question-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, var(--gradient-start), var(--gradient-end));
        }

        .question-title {
            color: var(--primary);
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        @media (max-width: 576px) {
            .question-title {
                font-size: 0.9rem;
                margin-bottom: 0.75rem;
                line-height:1.6em;
            }
        }

        .option-label {
            display: block;
            padding: 0.6rem 0.75rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            margin-bottom: 0.4rem;
            font-size: 0.95rem;
        }

        @media (max-width: 576px) {
            .option-label {
                padding: 0.5rem 0.6rem;
                font-size: 0.9rem;
            }
        }

        .option-label:hover {
            background-color: rgba(36, 174, 97, 0.05);
        }

        .form-check-input {
            margin-right: 10px;
            transform: scale(1.2);
        }

        @media (max-width: 576px) {
            .form-check-input {
                margin-right: 8px;
                transform: scale(1.1);
            }
        }

        .form-check-input:checked {
            background-color: var(--secondary);
            border-color: var(--secondary);
        }

        .timer-container {
            background: rgba(18, 98, 62, 0.05);
            padding: 0.6rem;
            border-radius: 50px;
            max-width: 250px;
            margin: 0 auto 1.25rem;
            font-weight: 600;
            color: var(--primary);
            box-shadow: 0 2px 10px rgba(18, 98, 62, 0.07);
            font-size: 0.95rem;
        }

        @media (max-width: 576px) {
            .timer-container {
                font-size: 0.9rem;
                padding: 0.5rem;
                max-width: 220px;
                margin: 0 auto 1rem;
            }
        }

        .spinner-border {
            margin-right: 8px;
            width: 1.25rem;
            height: 1.25rem;
        }

        @media (max-width: 576px) {
            .spinner-border {
                width: 1rem;
                height: 1rem;
                margin-right: 6px;
            }
        }

        code {
            background-color: #f8f9fa;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            color: var(--primary);
            font-size: 0.9rem;
        }

        pre {
            background-color: #f8f9fa;
            padding: 0.75rem;
            border-radius: 6px;
            border-left: 3px solid var(--secondary);
            font-size: 0.9rem;
            white-space: pre-wrap;
            word-break: break-word;
            overflow-x: auto;
        }

        @media (max-width: 576px) {
            pre {
                padding: 0.6rem;
                font-size: 0.8rem;
            }

            code {
                font-size: 0.8rem;
            }
        }

        .step {
            display: none;
        }

        .step.active {
            display: block;
        }

        textarea.form-control {
            min-height: 80px;
        }

        @media (max-width: 576px) {
            textarea.form-control {
                min-height: 70px;
            }
        }

        /* Improved mobile tap targets */
        @media (max-width: 576px) {
            .form-check {
                margin-bottom: 0.5rem;
            }

            input[type="radio"],
            input[type="checkbox"],
            button {
                min-height: 24px;
                min-width: 24px;
            }

            .progress {
                height: 6px !important;
            }

            /* Prevent zoom on input focus */
            input, select, textarea {
                font-size: 16px !important;
            }
        }

        /* Fixed position timer for mobile */
        @media (max-width: 576px) {
            .timer-fixed {
                position: fixed;
                bottom: 10px;
                right: 10px;
                background: rgba(255, 255, 255, 0.95);
                border: 1px solid rgba(18, 98, 62, 0.2);
                z-index: 1000;
                padding: 5px 12px;
                border-radius: 50px;
                font-size: 0.8rem;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                max-width: 150px;
            }
        }

        .test-instructions {
            background-color: rgba(18, 98, 62, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid var(--primary);
        }

        .test-instructions ul {
            margin-bottom: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container custom-container">
        <div class="text-center p-3">
            <img src="https://trogonmedia.com/assets/images/trogon_logo1%20-%20Copy.png" alt="Trogon Media Pvt Ltd" style="width:130px;margin:auto:">
        </div>
        <h1 class="text-center gradient-heading">PHP Developer Aptitude Test</h1>

        <div class="progress mb-4" style="height: 8px;">
            <div class="progress-bar bg-success" id="progress" role="progressbar" style="width: 0%"></div>
        </div>

        <div class="timer-container text-center" id="timer">
            <i class="bi bi-alarm"></i> Time Remaining: 12:00
        </div>

        <!-- Step 1: Registration Form -->
        <div class="step active" id="step1">
            <h2 class="mb-4">Personal Information</h2>
            <form id="registrationForm">
                <div class="mb-3">
                    <label for="fullname" class="form-label">Full Name*</label>
                    <input type="text" class="form-control" id="fullname" name="fullname" required>
                    <div class="invalid-feedback" id="fullname-error">Please enter your full name</div>
                </div>

                <div class="mb-3">
                    <label for="email" class="form-label">Email Address*</label>
                    <input type="email" class="form-control" id="email" name="email" required>
                    <div class="invalid-feedback" id="email-error">Please enter a valid email address</div>
                </div>

                <div class="mb-3">
                    <label for="phone" class="form-label">Phone Number* <br><small>(Without Country Code)</small></label>
                    <input type="number" class="form-control" id="phone" name="phone" required>
                    <div class="invalid-feedback" id="phone-error">Please enter a valid phone number</div>
                </div>

                <div class="mb-3">
                    <label for="district" class="form-label">District*</label>
                    <select class="form-select" id="district" name="district" required>
                        <option value="">Select a district</option>
                        <option value="Alappuzha">Alappuzha</option>
                        <option value="Ernakulam">Ernakulam</option>
                        <option value="Idukki">Idukki</option>
                        <option value="Kannur">Kannur</option>
                        <option value="Kasaragod">Kasaragod</option>
                        <option value="Kollam">Kollam</option>
                        <option value="Kottayam">Kottayam</option>
                        <option value="Kozhikode">Kozhikode</option>
                        <option value="Malappuram">Malappuram</option>
                        <option value="Palakkad">Palakkad</option>
                        <option value="Pathanamthitta">Pathanamthitta</option>
                        <option value="Thiruvananthapuram">Thiruvananthapuram</option>
                        <option value="Thrissur">Thrissur</option>
                        <option value="Wayanad">Wayanad</option>
                    </select>
                    <div class="invalid-feedback" id="district-error">Please select a district</div>
                </div>

                <div class="mb-3">
                    <label for="location" class="form-label">Location*</label>
                    <input type="text" class="form-control" id="location" name="location" required>
                    <div class="invalid-feedback" id="location-error">Please enter your location</div>
                </div>

                <button type="button" class="btn custom-btn" id="startTest">Start Test</button>
            </form>
        </div>

        <!-- Step 2: Test Questions -->
        <div class="step" id="step2">
            <h2 class="mb-4">Technical Aptitude Test</h2>

            <div class="test-instructions">
                <p><strong>Instructions:</strong></p>
                <ul>
                    <li>You have 12 minutes to complete this test.</li>
                    <li>Answer all 13 questions - 6 multiple-choice and 7 short answer.</li>
                    <li>Read each question carefully before answering.</li>
                    <li>For short answer questions, provide concise and relevant responses.</li>
                </ul>
            </div>

            <form id="testForm">
                <!-- Multiple Choice Questions -->
                <div class="question-card">
                    <h3 class="question-title">1. What's the difference between `#header { … }` and `.header { … }` in CSS?</h3>
                    <div class="options">
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q1_a" name="q1" value="a">
                                `#header` targets elements with <strong>id="header"</strong>; `.header` targets elements with <strong>class="header"</strong>
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q1_b" name="q1" value="b">
                                `#header` targets elements with class "header"; `.header` targets elements with id "header"
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q1_c" name="q1" value="c">
                                `#header` can apply to multiple elements; `.header` only to one
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q1_d" name="q1" value="d">
                                There is no difference
                            </label>
                        </div>
                    </div>
                </div>

                <div class="question-card">
                    <h3 class="question-title">2. Which method would you use to select the element with <strong>id="main"</strong>?</h3>
                    <div class="options">
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q2_a" name="q2" value="a">
                                `document.getElementById("main")`
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q2_b" name="q2" value="b">
                                `document.querySelectorAll("#main")`
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q2_c" name="q2" value="c">
                                `document.getElementsByClassName("main")`
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q2_d" name="q2" value="d">
                                `document.getElementsByTagName("main")`
                            </label>
                        </div>
                    </div>
                </div>

                <div class="question-card">
                    <h3 class="question-title">3. In a row of trees, every third tree is an apple tree, every fifth is an orange tree. Which position is the first tree to bear both fruits?</h3>
                    <div class="options">
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q3_a" name="q3" value="a">
                                15
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q3_b" name="q3" value="b">
                                30
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q3_c" name="q3" value="c">
                                10
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q3_d" name="q3" value="d">
                                5
                            </label>
                        </div>
                    </div>
                </div>

                <div class="question-card">
                    <h3 class="question-title">4. You're given a task with unclear requirements and a tight deadline. What's your first step?</h3>
                    <div class="options">
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q4_a" name="q4" value="a">
                                Start coding immediately
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q4_b" name="q4" value="b">
                                Ask clarifying questions and draft a quick spec
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q4_c" name="q4" value="c">
                                Delegate to teammates who already experienced
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q4_d" name="q4" value="d">
                                Propose extending the deadline
                            </label>
                        </div>
                    </div>
                </div>

                <div class="question-card">
                    <h3 class="question-title">5. What's the difference between `include` and `require` in PHP?</h3>
                    <div class="options">
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q5_a" name="q5" value="a">
                                `include` throws a fatal error on failure; `require` only a warning
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q5_b" name="q5" value="b">
                                `require` throws a fatal error on failure; `include` only a warning
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q5_c" name="q5" value="c">
                                No difference
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q5_d" name="q5" value="d">
                                `include` can only load files once
                            </label>
                        </div>
                    </div>
                </div>

                <div class="question-card">
                    <h3 class="question-title">6. What will this output?<br>
                    <pre><code>$a = 5; echo $a++ + ++$a;</code></pre></h3>
                    <div class="options">
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q6_a" name="q6" value="a">
                                11
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q6_b" name="q6" value="b">
                                12
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q6_c" name="q6" value="c">
                                10
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q6_d" name="q6" value="d">
                                Undefined behavior
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Short Answer Questions -->
                <div class="question-card">
                    <h3 class="question-title">7. In 2–3 sentences, explain what "API" means to someone without a tech background.</h3>
                    <div class="mb-3">
                        <textarea class="form-control" name="q7" id="q7" rows="3" placeholder="Type your answer here..."></textarea>
                    </div>
                </div>

                <div class="question-card">
                    <h3 class="question-title">8. Briefly describe a time you faced a setback (in school or college) and how you stayed positive to overcome it.</h3>
                    <div class="mb-3">
                        <textarea class="form-control" name="q8" id="q8" rows="3" placeholder="Type your answer here..."></textarea>
                    </div>
                </div>

                <div class="question-card">
                    <h3 class="question-title">9. Tell us about a new tool or technology you picked up on your own. How did you go about learning it?</h3>
                    <div class="mb-3">
                        <textarea class="form-control" name="q9" id="q9" rows="3" placeholder="Type your answer here..."></textarea>
                    </div>
                </div>

                <div class="question-card">
                    <h3 class="question-title">10. When processes or priorities change mid‑project, what do you do to adapt and keep things on track?</h3>
                    <div class="mb-3">
                        <textarea class="form-control" name="q10" id="q10" rows="3" placeholder="Type your answer here..."></textarea>
                    </div>
                </div>

                <div class="question-card">
                    <h3 class="question-title">11. What is database normalization? List the first three normal forms.</h3>
                    <div class="mb-3">
                        <textarea class="form-control" name="q11" id="q11" rows="4" placeholder="Type your answer here..."></textarea>
                    </div>
                </div>

                <div class="question-card">
                    <h3 class="question-title">12. Which of the following prompts is <strong>best</strong> for getting ChatGPT to produce a clear, step‑by‑step guide to implement user authentication in PHP using JSON Web Tokens (JWT)?</h3>
                    <div class="options">
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q12_a" name="q12" value="a">
                                "Tell me about PHP authentication."
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q12_b" name="q12" value="b">
                                "Explain JWT."
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q12_c" name="q12" value="c">
                                "Write a PHP script for authentication."
                            </label>
                        </div>
                        <div class="form-check">
                            <label class="option-label">
                                <input class="form-check-input" type="radio" id="q12_d" name="q12" value="d">
                                "Write a step‑by‑step tutorial in PHP that shows how to set up user registration, login, and protected routes using JSON Web Tokens, including code snippets and brief explanations for each step."
                            </label>
                        </div>
                    </div>
                </div>

                <div class="question-card">
                    <h3 class="question-title">13. Craft a single, well‑structured prompt you would give to ChatGPT to <strong>design a relational database schema</strong> for an e‑commerce application.<br>
                    <strong>Your prompt should</strong>:
                    <ul>
                        <li>Define the main entities (e.g., Users, Products, Orders, Reviews)</li>
                        <li>Ask for table names, field names, data types, primary/foreign keys</li>
                        <li>Specify relationships and normalization (up to 3NF)</li>
                        <li>Request brief explanations of each table's purpose</li>
                    </ul>
                    </h3>
                    <div class="mb-3">
                        <textarea class="form-control" name="q13" id="q13" rows="5" placeholder="Type your prompt here..."></textarea>
                    </div>
                </div>

                <button type="button" class="btn custom-btn custom-btn-submit" id="submitTest">
                    <span class="spinner-border spinner-border-sm d-none" id="submitSpinner" role="status" aria-hidden="true"></span>
                    Submit Test
                </button>
                <div style="padding:25px;"></div>
            </form>
        </div>

        <!-- Step 3: Thank You -->
        <div class="step" id="step3">
            <div class="text-center my-5 mt-4">
                <div class="mb-4">
                    <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                </div>
                <h2 class="text-success mb-4">Thank You!</h2>
                <p class="lead">Your responses have been successfully submitted.</p>
                <!-- <p>We will contact you soon with the results.</p> -->
            </div>
        </div>
    </div>

    <!-- jQuery (load before Bootstrap) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Bootstrap 5 Bundle with Popper -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
    $(document).ready(function() {
        // DOM Elements
        var step1 = $('#step1');
        var step2 = $('#step2');
        var step3 = $('#step3');
        var startTestBtn = $('#startTest');
        var submitTestBtn = $('#submitTest');
        var progressBar = $('#progress');
        var timerElement = $('#timer');
        var submitSpinner = $('#submitSpinner');

        // Adjust layout for mobile devices
        function adjustForMobile() {
            if(window.innerWidth <= 576) {
                // Make inputs larger for better tap targets
                $('.form-check-input').css('transform', 'scale(1.1)');

                // Adjust spacing between items
                $('.question-card').css('margin-bottom', '1rem');
            }
        }

        // Call mobile adjustments on load
        adjustForMobile();

        // Call again on resize
        $(window).resize(function() {
            adjustForMobile();
        });

        // Form validation
        function validateRegistrationForm() {
            var isValid = true;

            // Reset validation states
            $('.is-invalid').removeClass('is-invalid');

            // Validate Full Name
            var fullname = $('#fullname').val();
            if (!fullname || !fullname.trim()) {
                $('#fullname').addClass('is-invalid');
                isValid = false;
            }

            // Validate Email
            var email = $('#email').val();
            var emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!email || !emailPattern.test(email)) {
                $('#email').addClass('is-invalid');
                isValid = false;
            }

            // Validate Phone
            var phone = $('#phone').val();
            var phonePattern = /^[0-9]{10}$/;
            if (!phone || !phonePattern.test(phone)) {
                $('#phone').addClass('is-invalid');
                isValid = false;
            }

            // Validate District
            var district = $('#district').val();
            if (!district) {
                $('#district').addClass('is-invalid');
                isValid = false;
            }

            // Validate Location
            var location = $('#location').val();
            if (!location || !location.trim()) {
                $('#location').addClass('is-invalid');
                isValid = false;
            }

            return isValid;
        }

        // Timer functionality
        var timeLeft = 12 * 60; // 12 minutes in seconds
        var timerInterval;

        function startTimer() {
            timerInterval = setInterval(function() {
                timeLeft--;

                var minutes = Math.floor(timeLeft / 60);
                var seconds = timeLeft % 60;

                var minutesStr = minutes < 10 ? '0' + minutes : minutes.toString();
                var secondsStr = seconds < 10 ? '0' + seconds : seconds.toString();

                timerElement.text('Time Remaining: ' + minutesStr + ':' + secondsStr);

                if (timeLeft <= 180) { // 3 minutes remaining
                    timerElement.addClass('text-danger');
                }

                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    submitTest();
                }
            }, 1000);
        }

        // Submit test function with AJAX
        function submitTest() {
            // Show loading state
            submitTestBtn.prop('disabled', true);
            submitSpinner.removeClass('d-none');

            // Get form data manually for maximum compatibility
            // Collect answers in an array
            var answers = [];

            // Multiple choice questions (q1-q6, q12)
            for (var i = 1; i <= 6; i++) {
                var answer = $('input[name="q' + i + '"]:checked').val();
                if (answer) {
                    answers.push({
                        question_no: i,
                        answer: answer
                    });
                }
            }

            // Short answer questions (q7-q11, q13)
            for (var i = 7; i <= 11; i++) {
                var answer = $('#q' + i).val();
                if (answer) {
                    answers.push({
                        question_no: i,
                        answer: answer
                    });
                }
            }

            // Handle q12 (multiple choice) and q13 (short answer) separately
            var q12Answer = $('input[name="q12"]:checked').val();
            if (q12Answer) {
                answers.push({
                    question_no: 12,
                    answer: q12Answer
                });
            }

            var q13Answer = $('#q13').val();
            if (q13Answer) {
                answers.push({
                    question_no: 13,
                    answer: q13Answer
                });
            }

            // Create the user data object with the answers array
            var userData = {
                fullname: $('#fullname').val(),
                email: $('#email').val(),
                phone: $('#phone').val(),
                district: $('#district').val(),
                location: $('#location').val(),
                answers: JSON.stringify(answers)
            };

            console.log("Form data that would be sent via AJAX:", userData);

            // Simulate AJAX request with setTimeout
            setTimeout(function() {
                // Stop the timer
                clearInterval(timerInterval);

                // Add test timing information
                userData.test_start_time = sessionStorage.getItem('test_start_time');

                $.ajax({
                    url: '<?php echo base_url("aptitude/submit_test"); ?>',
                    type: 'POST',
                    data: userData,
                    dataType: 'json',
                    success: function(response) {
                        console.log("Response received:", response);

                        if (response.status === 'success') {
                            // Move to thank you step
                            step2.removeClass('active');
                            step3.addClass('active');

                            // Update progress bar
                            progressBar.css('width', '100%');
                        } else {
                            // Show error message
                            alert(response.message || "There was an error submitting your test. Please try again.");

                            // Reset button state
                            submitTestBtn.prop('disabled', false);
                            submitSpinner.addClass('d-none');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log("Error in submission:", xhr.responseText);

                        // Show error message
                        alert("There was an error submitting your test. Please try again.");

                        // Reset button state
                        submitTestBtn.prop('disabled', false);
                        submitSpinner.addClass('d-none');
                    }
                });
            }, 2000); // Simulate 2-second server processing time
        }

        // Event Listeners
        startTestBtn.on('click', function() {
            if (validateRegistrationForm()) {
                // Store test start time
                var currentTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
                sessionStorage.setItem('test_start_time', currentTime);

                step1.removeClass('active');
                step2.addClass('active');

                // Start the timer
                startTimer();

                // Update progress bar
                progressBar.css('width', '50%');

                // Add fixed position class to timer on mobile
                if(window.innerWidth <= 576) {
                    timerElement.addClass('timer-fixed');
                }

                // Fade in questions
                setTimeout(function() {
                    $('.question-card').css('opacity', '1');
                }, 300);
            }
        });

        submitTestBtn.on('click', function() {
            submitTest();
        });
    });
    </script>
</body>
</html>