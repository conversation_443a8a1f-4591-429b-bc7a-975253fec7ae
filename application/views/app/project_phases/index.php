<div class="container-fluid">
	<div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
		<a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
			<i class="fas fa-arrow-circle-left"></i> Go Back
		</a>
        <?php
            if (has_permission('project_phases/add')){
                ?>
                <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=project_phases/add', 'Add Project Phase')"
                        class="btn btn-primary btn-mini float-right">
                    <small><i class="fas fa-plus"></i></small> Add Project Phase
                </button>
                <?php
            }
        ?>
        <?php
        if (has_permission('project_phases/edit')){
            ?>
            <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=project_phases/sort', 'Sort Project Phases')"
                    class="btn btn-secondary btn-mini float-right mr-2">
                <small><i class="fas fa-sort"></i></small> Sort Phases
            </button>
            <?php
        }
        ?>
	</div>
	<div class="card card-primary row mt-3 shadow-pro" style="margin:14px;">
		<div class="card-header">
			<h3 class="card-title"><?= strtoupper($page_title ?? '')?></h3>
		</div>
		<!-- /.card-header -->
		<div class="card-body">
			<table id="example1" class="table table-bordered table-striped">
				<thead>
				<tr>
					<th>Order</th>
					<th>Title</th>
					<th>Description</th>
					<th>Deliverable</th>
					<th>Assigned Users</th>
                    <th style="width: 55px;">Action</th>
                </tr>
				</thead>
				<tbody>
				<?php
				if (isset($list_items) && isset($users)) {
					foreach ($list_items as $key => $item) {
                        $assigned_users = [];
                        $user_ids = json_decode($item['user_id'], true) ?: [];
                        foreach ($user_ids as $user_id) {
                            if (isset($users[$user_id])) {
                                $assigned_users[] = $users[$user_id];
                            }
                        }
						?>
						<tr>
							<td><?= $item['phase_order'] ?></td>
							<td>
                                <b><?= $item['title']?></b>
                            </td>
							<td><?= $item['short_description']?></td>
							<td>
                                <?php if($item['is_deliverable']): ?>
                                    <span class="badge badge-success">Yes</span>
                                <?php else: ?>
                                    <span class="badge badge-secondary">No</span>
                                <?php endif; ?>
                            </td>
                            <td><?= implode(', ', $assigned_users) ?></td>
                            <td>
                                <?php
                                if (has_permission('project_phases/edit')){
                                    ?>
                                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=project_phases/edit'); ?>', 'Edit Project Phase')"
                                            class="btn btn-info btn-sm">
                                        <small><i class="fas fa-pencil-alt"></i></small>
                                    </button>
                                    <?php
                                }
                                ?>

                                <?php
                                if (has_permission('project_phases/delete')){
                                    ?>
                                    <button onclick="confirm_modal('<?=base_url("app/project_phases/delete/{$item['id']}/");?>')"
                                            class="btn btn-outline-danger btn-sm">
                                        <small><i class="fas fa-trash"></i> </small>
                                    </button>
                                    <?php
                                }
                                ?>
                            </td>
						</tr>
						<?php
					}
				}
				?>
				</tbody>
			</table>
		</div>
		<!-- /.card-body -->
	</div>
</div>