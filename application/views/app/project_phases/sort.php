<?php
$phases = $this->project_phases_m->get(null, null, ['key' => 'phase_order', 'direction' => 'asc'])->result_array();
?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<div class="container">
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Drag and drop items to reorder project phases. Changes will be saved automatically.
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <ul class="list-group" id="sortable-phases">
                <?php foreach($phases as $phase): ?>
                <li class="list-group-item" data-id="<?= $phase['id'] ?>">
                    <div class="d-flex align-items-center">
                        <div class="mr-3">
                            <i class="fas fa-grip-vertical text-muted handle" style="cursor: move;"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1">
                                <?= $phase['title'] ?>
                                <?php if($phase['is_deliverable']): ?>
                                    <span class="badge badge-success">Deliverable</span>
                                <?php endif; ?>
                            </h5>
                            <?php if(!empty($phase['short_description'])): ?>
                                <p class="mb-0 text-muted"><?= $phase['short_description'] ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="ml-3 text-right">
                            <span class="badge badge-secondary">Order: <?= $phase['phase_order'] ?></span>
                        </div>
                    </div>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-12">
            <div id="status-message"></div>
        </div>
    </div>
</div>

<style>
.handle {
    cursor: move;
    padding: 10px;
}
#sortable-phases li {
    transition: background-color 0.3s;
}
#sortable-phases li:hover {
    background-color: #f8f9fa;
}
.ui-sortable-helper {
    box-shadow: 0 0 10px rgba(0,0,0,0.2);
    background-color: #fff;
}
</style>

<script type="text/javascript">
$(document).ready(function() {
    // Check if jQuery UI is now loaded
    if (typeof $.ui === 'undefined') {
        console.error("jQuery UI is still not available");
        $('#status-message').html('<div class="alert alert-danger">Error: jQuery UI is not available</div>');
        return;
    }
    
    console.log("jQuery UI version: " + $.ui.version);
    
    // Initialize sortable
    try {
        $("#sortable-phases").sortable({
            items: "li",
            handle: ".handle",
            placeholder: "list-group-item bg-light",
            update: function(event, ui) {
                saveNewOrder();
            }
        });
        $("#sortable-phases").disableSelection();
        console.log("Sortable successfully initialized");
    } catch (error) {
        console.error("Error initializing sortable:", error);
        $('#status-message').html('<div class="alert alert-danger">Error initializing sortable: ' + error.message + '</div>');
    }
});

function saveNewOrder() {
    // Get the new order
    let phases = [];
    $('#sortable-phases li').each(function(index) {
        phases.push({
            id: $(this).data('id'),
            order: index + 1
        });
    });
    
    // Show loading message
    $('#status-message').html('<div class="alert alert-warning">Saving changes...</div>');
    
    // Send the updated order to the server
    $.ajax({
        url: '<?= base_url("app/project_phases/update_order") ?>',
        type: 'POST',
        data: {
            phases: phases
        },
        success: function(response) {
            try {
                let data = JSON.parse(response);
                if(data.status === 'success') {
                    $('#status-message').html('<div class="alert alert-success">Order updated successfully!</div>');
                    
                    // Update the displayed order numbers
                    $('#sortable-phases li').each(function(index) {
                        $(this).find('.badge-secondary').text('Order: ' + (index + 1));
                    });
                    
                    // Hide message after 3 seconds
                    setTimeout(function() {
                        $('#status-message').html('');
                    }, 3000);
                } else {
                    $('#status-message').html('<div class="alert alert-danger">Error: ' + data.message + '</div>');
                }
            } catch(e) {
                console.error("Error parsing response:", e, response);
                $('#status-message').html('<div class="alert alert-danger">Error processing server response</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error("AJAX error:", status, error);
            $('#status-message').html('<div class="alert alert-danger">Error updating order. Please try again.</div>');
        }
    });
}
</script>