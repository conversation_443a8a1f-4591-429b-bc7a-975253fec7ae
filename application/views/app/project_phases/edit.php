<?php
$item_id = $param1;
$edit_data = $this->project_phases_m->get(['id' => $item_id])->row_array();
$users = $this->users_m->get(['employee_status' => 1, 'is_employee' => 1], ['id', 'name'])->result_array();
$assigned_users = json_decode($edit_data['user_id'], true) ?: [];
?>
<form class="form-horizontal" action="<?=base_url('app/project_phases/edit/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">
        <div class="form-group col-6 p-0">
            <label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="title" name="title" value="<?=$edit_data['title']?>" placeholder="Phase Title" required>
            </div>
        </div>
        <div class="form-group col-6 p-0">
			<label for="phase_order" class="col-sm-12 col-form-label text-muted">Phase Order</label>
			<div class="col-sm-12">
				<input type="number" class="form-control" id="phase_order" name="phase_order" value="<?=$edit_data['phase_order']?>" min="0">
			</div>
		</div>
        <div class="form-group col-12 p-0">
			<label for="short_description" class="col-sm-12 col-form-label text-muted">Description</label>
			<div class="col-sm-12">
				<textarea class="form-control" id="short_description" name="short_description" placeholder="Short Description"><?=$edit_data['short_description']?></textarea>
			</div>
		</div>
        <div class="form-group col-12 p-0">
			<label for="user_id" class="col-sm-12 col-form-label text-muted">Assign Users</label>
			<div class="col-sm-12">
                <select class="form-control select2" id="user_id" name="user_id[]" multiple>
                    <?php
                        foreach ($users as $user) {
                            $selected = in_array($user['id'], $assigned_users) ? 'selected' : '';
                            echo "<option value='{$user['id']}' {$selected}>{$user['name']}</option>";
                        }
                    ?>
                </select>
                <small class="text-muted">You can select multiple users</small>
			</div>
		</div>
        <div class="form-group col-12 p-0">
            <div class="col-sm-12">
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="is_deliverable" name="is_deliverable" value="1" <?=$edit_data['is_deliverable'] ? 'checked' : ''?>>
                    <label class="custom-control-label" for="is_deliverable">Is Deliverable</label>
                </div>
            </div>
        </div>
    </div>
	<div class="col-12">
		<button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
			<small><i class="fa fa-check"></i></small> Save
		</button>
	</div>
</form>

<script type="application/javascript">
	$('.select2').select2();
</script>