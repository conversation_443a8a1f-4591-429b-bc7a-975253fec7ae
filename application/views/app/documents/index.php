<div class="container-fluid ">
	<div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
		<a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
			<i class="fas fa-arrow-circle-left"></i> Go Back
		</a>
        <?php
            if (has_permission('documents/add')){
                ?>
                <button onclick="show_ajax_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=documents/add', '<?= get_phrase('add_document'); ?>')"
                        class="btn btn-primary btn-mini float-right btn-round">
                    <small><i class="fas fa-plus"></i></small> Add <?= $page_title ?? '' ?>
                </button>
                <?php
            }
        ?>

	</div>
    <div class="mt-3 p-2">
        <div class="">
            <div class="p-2">
                <div class="row text-center bg-white p-2" style="border-radius: 10px">
                    <?php
                    if (isset($status_count)){
                        ?>
                        <div class="col-6 col-lg-3 p-1 pb-0">
                            <div class="shadow_pro bg-white p-2 pb-0">
                                <div class="text-primary p-2" style="background-color: #eaf3fd;border-radius: 8px;">
                                    <span style="font-size: 28px; font-weight: bold"><?=count($list_items ?? [])?></span>
                                    <div class="mt-0 pt-0" style="margin-top: -5px!important;">
                                        <small>TOTAL</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 col-lg-3 p-1 pb-0">
                            <div class="shadow_pro bg-white p-2 pb-0">
                                <div class="text-danger p-2" style="background-color: #f6e5e6;border-radius: 8px;">
                                    <span style="font-size: 28px; font-weight: bold"><?=$status_count['pending_count']?></span>
                                    <div class="mt-0 pt-0" style="margin-top: -5px!important;">
                                        <small>PENDING</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 col-lg-3 p-1 pb-0">
                            <div class="shadow_pro bg-white p-2 pb-0">
                                <div class="text-warning p-2" style="background-color: #f8f4ec;border-radius: 8px;">
                                    <span style="font-size: 28px; font-weight: bold"><?=$status_count['assigned_count']?></span>
                                    <div class="mt-0 pt-0" style="margin-top: -5px!important;">
                                        <small>ASSIGNED</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 col-lg-3 p-1 pb-0">
                            <div class="shadow_pro bg-white p-2 pb-0">
                                <div class="text-success p-2" style="background-color: #e7faec;border-radius: 8px;">
                                    <span style="font-size: 28px; font-weight: bold"><?=$status_count['completed_count']?></span>
                                    <div class="mt-0 pt-0" style="margin-top: -5px!important;">
                                        <small>COMPLETED</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                    ?>
                    <div class="p-2 m-0 pb-2 pt-0 col-12">
                        <input type="text" class="form-control form-control-lg" id="ta_search_list" placeholder="Search Documents.." style="width: 100%!important; padding:25px;font-size:18px;border-radius: 40px;">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <?php
            if (isset($list_items) && isset($projects)) {
                foreach ($list_items as $key => $item) {
                    ?>
                    <div class="col-12 col-lg-4 p-2 ta_search_item">
                        <div class="bg-white p-4 pt-0 shadow-pro" style="border-radius: 10px;">
                            <h5 style="font-weight: bold; color: #434343;margin-bottom: 0px!important;"><?= $item['title']?></h5>
                            <small class="text-muted" style="font-size: 14px!important;">
                                <?= DateTime::createFromFormat('Y-m-d H:i:s', $item['updated_on'])->format('d-m-Y g:i A')?>
                            </small>
                            <div class="row">
                                <div class="col-12">
                                    <small class="text-muted">
                                        Projects:
                                        <b><?=$projects[$item['project_id']]?></b>
                                    </small>
                                </div>
                                <div class="col-12">
                                    <small class="text-muted">Created By: <b class="text-info">ADEEB C</b></small>
                                </div>
                            </div>

                            <hr>
                            <div>
                                <div class="row">
                                <?php
                                if (has_permission('documents/edit') && $item['created_by'] == get_user_id()){
                                    ?>
                                    
                                        <div class="col-6 p-2 pt-0 pb-0">
                                            <a href="<?=base_url('app/documents/edit_content/'.$item['id'].'/')?>"
                                                    class="btn btn-outline-success btn-sm w-100 btn-round">
                                                <small><i class="fas fa-pencil-alt"></i></small> Edit
                                            </a>
                                        </div>
                                        
                                    
                                    <?php
                                }
                                if(has_permission('documents/delete') && $item['created_by'] == get_user_id()){
                                    ?>
                                    <div class="col-6 p-2 pt-0 pb-0">
                                        <button onclick="confirm_modal('<?=base_url("app/users/delete/{$item['id']}/");?>')"
                                                class="btn btn-outline-danger btn-sm w-100 btn-round">
                                            <small><i class="fas fa-trash"></i> </small> Delete
                                        </button>
                                    </div>
                                    <?php
                                }
                                ?>
                                
                                
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            }
            ?>
		</div>
        <div id="ta_no_result" class="text-center p-2" style="display: none">
            <div class="p-4 shadow_pro bg-white mx-auto" style="">
                <img width="96" height="96" src="https://img.icons8.com/fluency/96/nothing-found.png" alt="nothing-found"/>
                <h4 class="text-danger mt-3">No Result Found!</h4>
            </div>
        </div>
	</div>
</div>

