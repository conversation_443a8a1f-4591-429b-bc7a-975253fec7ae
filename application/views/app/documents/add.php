<?php
$projects = array_column($this->projects_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
?>
<form class="form-horizontal" action="<?=base_url('app/documents/add/')?>" method="post" enctype="multipart/form-data">
	<div class="row" style="margin: 0!important;">
        <div class="form-group col-12">
            <label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="title" name="title" value="" placeholder="Title" style="font-weight: bold; font-size: 17px" required>
            </div>
        </div>
        <div class="form-group col-12 mb-4">
            <label for="project_id" class="col-sm-12 col-form-label text-muted">Projects <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="project_id" name="project_id" required>
                    <option value="">Choose Project</option>
                    <?php
                    foreach ($projects as $project_id => $project) {
                        echo "<option value='{$project_id}'>{$project}</option>";
                    }
                    ?>
                </select>
            </div>
        </div>

	</div>

	<div class="col-12" >
		<button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right btn-round" style="float: right!important;width: 120px;">
			<small><i class="fa fa-check"></i></small> Save
		</button>
	</div>
</form>

<script type="text/javascript">
    $('.ck_editor').each(function(){
        CKEDITOR.replace(this);
    });
    $('.select2').select2();
</script>
