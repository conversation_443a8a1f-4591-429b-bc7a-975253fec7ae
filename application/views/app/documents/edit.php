<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */
$item_id = $param1;
$projects = array_column($this->projects_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
$edit_data = $this->documents_m->get(['id' => $item_id])->row_array();
?>
<form class="form-horizontal" action="<?=base_url('app/documents/edit/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">
        <div class="form-group col-12">
            <label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="title" name="title" value="<?=$edit_data['title']?>" placeholder="Title" style="font-weight: bold; font-size: 17px" required>
            </div>
        </div>
        <div class="form-group col-12">
            <label for="project_id" class="col-sm-12 col-form-label text-muted">Projects <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="project_id" name="project_id" style="max-width: 300px" required>
                    <option value="">Choose Project</option>
                    <?php
                    foreach ($projects as $project_id => $project) {
                        $selected = $project_id == $edit_data['project_id'] ? ' selected="selected"' : '';
                        echo "<option value='{$project_id}' {$selected}>{$project}</option>";
                    }
                    ?>
                </select>
            </div>
        </div>
        <div class="col-12">
            <hr>
        </div>
        <div class="form-group col-12 p-0">
            <label for="content" class="col-sm-12 col-form-label text-muted">Content <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <textarea class="form-control ck_editor" id="content" name="content" placeholder="Content" style="resize: none; height: 300px;font-size:17px;"><?=$edit_data['content']?></textarea>
            </div>
        </div>

    </div>

    <div class="col-12" >
        <button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
            <small><i class="fa fa-check"></i></small> Save
        </button>
    </div>
</form>

<script type="text/javascript">
    $('.ck_editor').each(function(){
        CKEDITOR.replace(this);
    });
    $('.select2').select2();
</script>
