<!--lfj3nr7sn13rtolg505gqt6ut26sj4hofe3v7e5ic8nwyvcj-->

<div class="p-2 pt-0">
    <div class="shadow_pro bg-white">
        <form class="form-horizontal" action="" method="post" enctype="multipart/form-data">
            <div class="row" style="margin: 0!important;padding: 0!important;">
                <div class="form-group col-12 p-0">
                    <div class="col-sm-12 row">
                        <div class="col-sm-1 p-2">
                            <a href="<?=$_SERVER['HTTP_REFERER']?>" class="btn btn-secondary w-100 p-0" style="font-size: 28px;"><i class="bi bi-arrow-left-circle"></i></a>
                        </div>
                        <div class="col-sm-7 pt-2" style="margin-top:-4px!important">
                            <input class="form-control p-4" onchange="update_document()" id="edit_title" value="<?=$edit_data['title'] ?? ''?>" style="font-size: 20px;font-weight: bold; ">
                        </div>
                        <div class="col-sm-3 pt-2">
                            <select class="form-control select2" id="project_id" onchange="update_document()">
                                <option value="">Choose Project</option>
                                <?php
                                if (isset($projects)){
                                    foreach ($projects as $project_id => $project_title){
                                        $selected = isset($edit_data['project_id']) && $edit_data['project_id'] == $project_id ? 'selected' : '';
                                        echo "<option value='{$project_id}' {$selected}>{$project_title}</option>";
                                    }
                                }
                                ?>
                            </select>

                        </div>
                        <div class="col-sm-1 p-2 text-center">
                            <div id="editStatus">
                                <i style="font-size: 30px;" class="bi bi-check2-circle text-success"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-12 pt-2">
                        <textarea class="form-control" id="edit_content" name="description" placeholder="Description" style="resize: none; height: 200px;font-size:17px!important;"><?=$edit_data['content'] ?? ''?></textarea>

                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script type="application/javascript">
    tinymce.init({
        selector: '#edit_content',
        height: 750,
        content_css: '<?=base_url('assets/dist/css/ck_editor.css')?>',
        plugins: 'lists advlist pagebreak fullscreen',
        toolbar: 'bold italic underline | styleselect | bullist numlist | blocks | print | hr | pagebreak | fullscreen | removeformat',
        menubar: false,
        browser_spellcheck: true,
        setup: function(editor) {
            editor.on('keyup', function(e) {
                isContentEdited = true;
                showEditStatus();
            });
        },
        fontsize_formats: '18pt'
    });



    var isContentEdited = false;

    // Function to show edit status using jQuery
    function showEditStatus() {
        if (isContentEdited) {
            $('#editStatus').html(
                '<div class="spinner-border text-danger spinner-border-sm mt-1" style="width: 25px; height: 25px;" role="status">' +
                    '<span class="sr-only">Loading...</span>' +
                '</div>'
            );
        }
    }

    // Function to auto-save content using jQuery
    function autoSaveContent() {
        if (isContentEdited) {
            var content = tinymce.get('edit_content').getContent();
            save_content_ajax(content);
            isContentEdited = false;
        }
    }

    // Auto-save when the window is closed using jQuery
    $(window).on('beforeunload', autoSaveContent);

    // Auto-save when a specific modal is closed using jQuery
    $('#extra-large-modal').on('hidden.bs.modal', function() {
        autoSaveContent();
    });

    function update_document() {
        var content = tinymce.get('edit_content').getContent();
        save_content_ajax(content);
    }

    function save_content_ajax(content){
        var title = $('#edit_title').val();
        var project_id = $('#project_id').val();
        $.ajax({
            url: '<?=base_url('app/documents/ajax_save_content/')?>', // Replace with your server URL
            type: 'POST',
            data: {
                task_id: <?=$edit_data['id'] ?? 0?>,
                content: content,
                title: title,
                project_id: project_id,
            },
            success: function(response) {
                if (response){
                    // Handle success
                    $('#editStatus').html(
                        '<i style="font-size: 30px;" class="bi bi-check2-circle text-success"></i>'
                    );
                }else {
                    $('#editStatus').html(
                        '<div class="spinner-border text-danger spinner-border-sm mt-1" style="width: 25px; height: 25px;" role="status">' +
                        '<span class="sr-only">Loading...</span>' +
                        '</div>'
                    );
                }

            },
            error: function(xhr, status, error) {
                // Handle errors
                $('#editStatus').html(
                    '<span class="p-1" style="border-radius: 2px;color: #b90c0c!important;background-color: #fadbd7">' +
                    'Edit has not saved!' +
                    '</span>'
                );
            }
        });
    }

    setInterval(autoSaveContent, 6000);
</script> 






<style>
    .cke_editable{
        font-size:17px!important;
        height:300px!important;
    }
</style>