<?php
$project_id = $param1;
$project = $this->projects_m->get(['id' => $project_id])->row_array();
$project_name = isset($project['title']) ? $project['title'] : 'Project Schedule';

// Get only deliverable phases that are scheduled
$deliverable_phases = [];

// Get all phases ordered by phase_order
$phases = $this->project_phases_m->get(['is_deliverable' => 1], null, ['key' => 'phase_order', 'direction' => 'asc'])->result_array();

// Get scheduled phases for this project
$scheduled_phases = $this->project_schedule_m->get(['project_id' => $project_id])->result_array();

// Create a lookup map for quick access to scheduled phase data
$scheduled_map = [];
foreach ($scheduled_phases as $scheduled) {
    $scheduled_map[$scheduled['phase_id']] = $scheduled;
}

// Filter only deliverable phases that are scheduled
foreach ($phases as $phase) {
    if (isset($scheduled_map[$phase['id']])) {
        $schedule_data = $scheduled_map[$phase['id']];
        $deliverable = [
            'id' => $phase['id'],
            'title' => $phase['title'],
            'phase_order' => $phase['phase_order'],
            'date' => $schedule_data['date'],
            'status' => $schedule_data['status'],
            'remarks' => $schedule_data['remarks'] ?? ''
        ];
        $deliverable_phases[] = $deliverable;
    }
}

// Sort by date
usort($deliverable_phases, function($a, $b) {
    return strtotime($a['date']) - strtotime($b['date']);
});
?>

<div id="pdf-preview-container">
    <div class="text-center mb-4">
        <button id="download-pdf" class="btn btn-primary">
            <i class="fas fa-download"></i> Download PDF
        </button>
    </div>
    
    <div id="pdf-content" class="bg-white">
        <!-- Header Section -->
        <div class="pdf-header">
            <div class="company-info">
                
                <div class="company-details">
                <img src="<?= base_url('assets/logo/logo_primary.png') ?>" alt="Trogon Media Pvt Ltd" height="60">
                    <!-- <h1 style="font-size:14px;padding-top:9px;">TROGON MEDIA PVT LTD</h1> -->
                    <p>2nd Floor, UL Cyber Park, Nellikkode (PO), Kozhikode</p>
                </div>
            </div>
            <div class="document-info">
                <h2><?= strtoupper($project_name) ?></h2>
                <p>Date: <?= date('d F Y') ?></p>
            </div>
        </div>
        
        <!-- Deliverables Table -->
        <div class="deliverables-section">
            <h4 style="font-size:24px;padding:15px;padding-bottom:18px">PROJECT SCHEDULE</h4>
            <table class="deliverables-table">
                <thead>
                    <tr>
                        <th width="60">No.</th>
                        <th>Deliverable Item</th>
                        <th width="130">Status</th>
                        <th width="150">Delivery Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($deliverable_phases)): ?>
                    <tr>
                        <td colspan="4" class="text-center">No deliverables scheduled</td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($deliverable_phases as $index => $phase): ?>
                    <tr>
                        <td class="text-center"><?= $index + 1 ?></td>
                        <td>
                            <div class="deliverable-item">
                                <span class="title" style="font-weight:bold"><?= $phase['title'] ?></span>
                                <?php if (!empty($phase['remarks'])): ?>
                                <span class="remarks"><?= $phase['remarks'] ?></span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="text-center">
                            <?= strtoupper($phase['status']) ?>
                        </td>
                        <td class="text-center">
                            <?= date('d M, Y', strtotime($phase['date'])) ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Notes Section -->
        <div class="notes-section">
            <h4>NOTES</h4>
            <ul>
                <li>All deliverable dates are subject to timely client feedback and approvals.</li>
                <li>Changes to project scope may affect the delivery timeline.</li>
                <li>Project schedule will be reviewed and updated on a regular basis.</li>
            </ul>
        </div>
        
        <!-- Footer -->
        <div class="pdf-footer">
            <div class="footer-info">
                <p>© <?= date('Y') ?> Trogon Media Pvt Ltd. All Rights Reserved.</p>
                <p>Generated on <?= date('d F Y, h:i A') ?></p>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
<script>
$(document).ready(function() {
    $('#download-pdf').click(function() {
        // Change button text and add loading state
        var $btn = $(this);
        $btn.html('<i class="fas fa-spinner fa-spin"></i> Generating PDF...').prop('disabled', true);
        
        // PDF options
        var opt = {
            margin: [15, 15, 15, 15],
            filename: '<?= $project_name ?> - Schedule.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 2, useCORS: true },
            jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        };
        
        // Clone the content to avoid modifying the visible content
        var $content = $('#pdf-content').clone();
        
        // Get styled content
        var element = $content[0];
        
        // Generate PDF
        html2pdf().set(opt).from(element).save().then(function() {
            // Reset button
            $btn.html('<i class="fas fa-download"></i> Download PDF').prop('disabled', false);
        });
    });
});
</script>

<style>
#pdf-content {
    width: 100%;
    max-width: 210mm;
    margin: 0 auto;
    /* padding: 15mm 10mm; */
    /* box-shadow: 0 0 15px rgba(0, 0, 0, 0.15); */
    font-family: 'Segoe UI', Arial, sans-serif;
    color: #333;
    line-height: 1.5;
    position: relative;
    background-color: #fff;
}

/* Header Section */
.pdf-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    border-bottom: 2px solid #3fae61;
    padding-bottom: 20px;
}

.company-info {
    display: flex;
    align-items: center;
}

.company-details {
    /* margin-left: 10px; */
}

.company-details h1 {
    font-size: 18px;
    margin: 0;
    color: #20623d;
    font-weight: 600;
}

.company-details p {
    margin: 3px 0 0 0;
    font-size: 12px;
    color: #666;
}

.document-info {
    text-align: right;
}

.document-info h2 {
    font-size: 20px;
    margin: 0 0 8px 0;
    padding-top:10px;
    color: #20623d;
    font-weight: 600;
}

.document-info p {
    margin: 3px 0;
    font-size: 13px;
    color: #555;
}

/* Project Info */
.project-info {
    margin-bottom: 25px;
    text-align: center;
}

.project-info h3 {
    font-size: 22px;
    margin: 0 0 8px 0;
    color: #20623d;
}

.divider {
    height: 1px;
    background-color: #3fae61;
    margin: 10px auto;
    width: 50%;
}

/* Deliverables Section */
.deliverables-section {
    margin-bottom: 30px;
}

.deliverables-section h4 {
    font-size: 16px;
    margin: 0 0 15px 0;
    color: #20623d;
    font-weight: 600;
    text-align: center;
}

.deliverables-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.deliverables-table th {
    background-color: #20623d;
    color: white;
    text-align: left;
    padding: 10px;
    font-weight: 600;
    font-size: 15px;
}

.deliverables-table td {
    border: 1px solid #efefef;
    padding: 15px 10px;
    font-size: 15px;
    vertical-align: middle;
}

.deliverables-table tr:nth-child(even) {
    background-color: #f3f9f9;
}

.deliverable-item {
    display: flex;
    flex-direction: column;
}

.deliverable-item .title {
    font-weight: 500;
}

.deliverable-item .remarks {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.status-completed {
    background-color: #e5f5ea;
    color: #20623d;
    border: 1px solid #3fae61;
}

.status-pending {
    background-color: #f9e9e9;
    color: #d93025;
    border: 1px solid #f9c8c8;
}

.status-hold {
    background-color: #fff8e5;
    color: #e59d1f;
    border: 1px solid #ffe9b1;
}

.status-progress {
    background-color: #e5f0f9;
    color: #2b7cd3;
    border: 1px solid #c1e0ff;
}

/* Notes Section */
.notes-section {
    margin-bottom: 40px;
    padding: 15px;
    background-color: #f3f9f9;
    border-left: 3px solid #3fae61;
}

.notes-section h4 {
    font-size: 16px;
    margin: 0 0 10px 0;
    color: #20623d;
    font-weight: 600;
}

.notes-section ul {
    margin: 0;
    padding-left: 20px;
}

.notes-section li {
    font-size: 13px;
    margin-bottom: 5px;
    color: #555;
}

/* Footer */
.pdf-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #3fae61;
    padding-top: 15px;
    margin-top: 50px;
}

.footer-info {
    font-size: 11px;
    color: #666;
}

.footer-info p {
    margin: 2px 0;
}

.page-number {
    font-size: 11px;
    color: #666;
}

/* Media query for print */
@media print {
    body * {
        visibility: hidden;
    }
    #pdf-content, #pdf-content * {
        visibility: visible;
    }
    #pdf-content {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
}
</style>