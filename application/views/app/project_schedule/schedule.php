<?php
$project_id = $param1;
$phases = $this->project_phases_m->get(null, null, ['key' => 'phase_order', 'direction' => 'asc'])->result_array();

// Get existing schedules for this project
$existing_schedules = [];
$schedules = $this->project_schedule_m->get(['project_id' => $project_id])->result_array();
foreach ($schedules as $schedule) {
    $existing_schedules[$schedule['phase_id']] = $schedule;
}

// Pre-process deliverable data for the summary table
$deliverable_phases = [];
foreach ($phases as $phase) {
    if ($phase['is_deliverable'] && isset($existing_schedules[$phase['id']]) && !empty($existing_schedules[$phase['id']]['date'])) {
        $deliverable_phases[] = [
            'id' => $phase['id'],
            'title' => $phase['title'],
            'date' => $existing_schedules[$phase['id']]['date'],
            'order' => $phase['phase_order']
        ];
    }
}

// Sort by date
usort($deliverable_phases, function($a, $b) {
    return strtotime($a['date']) - strtotime($b['date']);
});
?>

<div class="alert alert-info mb-3">
    <i class="fas fa-info-circle"></i> Configure which phases are applicable to this project and set their schedule details.
</div>

<form id="schedule-setup-form">
    <input type="hidden" name="project_id" value="<?= $project_id ?>">
    
    <table class="table table-hover table-striped">
        <thead class="thead-light">
            <tr>
                <th style="width: 50px" class="text-center">Apply</th>
                <th>Phase</th>
                <th style="width: 200px">Date</th>
                <th>Remarks</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($phases as $phase): 
                $is_applicable = isset($existing_schedules[$phase['id']]);
                $schedule_data = $is_applicable ? $existing_schedules[$phase['id']] : null;
            ?>
            <tr>
                <td class="text-center align-middle">
                    <div class="custom-control custom-switch">
                        <input type="checkbox" class="custom-control-input phase-toggle" 
                            id="phase_applicable_<?= $phase['id'] ?>" 
                            data-phase-id="<?= $phase['id'] ?>" 
                            <?= $is_applicable ? 'checked' : '' ?>>
                        <label class="custom-control-label" for="phase_applicable_<?= $phase['id'] ?>"></label>
                    </div>
                </td>
                <td class="align-middle">
                    <span class="badge badge-secondary mr-2"><?= $phase['phase_order'] ?></span>
                    <strong><?= $phase['title'] ?></strong>
                    <?php if($phase['is_deliverable']): ?>
                        <span class="badge badge-success">Deliverable</span>
                    <?php endif; ?>
                </td>
                <td>
                    <input type="date" class="form-control form-control-sm phase-field" 
                        name="phases[<?= $phase['id'] ?>][date]" 
                        value="<?= $is_applicable && $schedule_data['date'] ? date('Y-m-d', strtotime($schedule_data['date'])) : '' ?>"
                        <?= !$is_applicable ? 'disabled' : '' ?>>
                </td>
                <td>
                    <input type="text" class="form-control form-control-sm phase-field"
                        name="phases[<?= $phase['id'] ?>][remarks]" 
                        placeholder="Remarks" 
                        value="<?= $is_applicable ? ($schedule_data['remarks'] ?? '') : '' ?>"
                        <?= !$is_applicable ? 'disabled' : '' ?>>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</form>

<!-- Deliverables Summary Table -->
<div class="card mt-4 mb-4">
    <div class="card-header bg-primary-lighten">
        <h6 class="mb-0 text-primary"><i class="fas fa-calendar-check mr-2"></i> Project Timeline Summary</h6>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-sm table-bordered mb-0">
                <thead class="thead-light">
                    <tr>
                        <th class="text-center" style="width: 50px;">#</th>
                        <th style="width: 100px;">Status</th>
                        <th>Deliverable</th>
                        <th style="width: 140px;">Date</th>
                        <th style="width: 100px;">Days to Next</th>
                        <th style="width: 120px;">Notes</th>
                    </tr>
                </thead>
                <tbody id="deliverables-summary">
                    <?php 
                    $previous_date = null;
                    
                    foreach ($deliverable_phases as $index => $deliverable) {
                        $date = new DateTime($deliverable['date']);
                        $date_formatted = $date->format('d M, Y');
                        
                        // Get status information if available
                        $status = isset($existing_schedules[$deliverable['id']]['status']) ? $existing_schedules[$deliverable['id']]['status'] : 'pending';
                        
                        // Set status color and icon
                        $status_color = 'secondary';
                        $status_icon = 'clock';
                        
                        switch($status) {
                            case 'completed':
                                $status_color = 'success';
                                $status_icon = 'check';
                                break;
                            case 'pending':
                                $status_color = 'danger';
                                $status_icon = 'hourglass-start';
                                break;
                            case 'on_hold':
                                $status_color = 'warning';
                                $status_icon = 'pause';
                                break;
                            case 'in_progress':
                                $status_color = 'info';
                                $status_icon = 'spinner';
                                break;
                        }
                        
                        // Calculate days difference
                        $days_to_next = '';
                        if (isset($deliverable_phases[$index + 1])) {
                            $next_date = new DateTime($deliverable_phases[$index + 1]['date']);
                            $interval = $date->diff($next_date);
                            $days_to_next = $interval->days . ' days';
                        }
                        
                        // Check if date is Sunday or third Saturday
                        $is_sunday = ($date->format('w') == 0);
                        $is_third_saturday = false;
                        
                        $day_of_month = $date->format('j');
                        $day_of_week = $date->format('w');
                        
                        // Check if it's a Saturday (6)
                        if ($day_of_week == 6) {
                            // Calculate which Saturday of the month it is
                            $first_day = new DateTime($date->format('Y-m-01'));
                            $first_day_of_week = $first_day->format('w');
                            $first_saturday_date = (7 - $first_day_of_week + 6) % 7 + 1;
                            $third_saturday_date = $first_saturday_date + 14;
                            
                            $is_third_saturday = ($day_of_month == $third_saturday_date);
                        }
                        
                        $date_class = ($is_sunday || $is_third_saturday) ? 'text-danger font-weight-bold' : '';
                        $note = '';
                        
                        if ($is_sunday) {
                            $note = '<span class="badge badge-danger">Sunday</span>';
                        } elseif ($is_third_saturday) {
                            $note = '<span class="badge badge-danger">3rd Saturday</span>';
                        }
                    ?>
                    <tr>
                        <td class="text-center"><?= $index + 1 ?></td>
                        <td class="text-center">
                            <div class="d-inline-flex align-items-center justify-content-center bg-<?= $status_color ?> text-white rounded-circle" style="width: 32px; height: 32px;">
                                <i class="fas fa-<?= $status_icon ?>"></i>
                            </div>
                        </td>
                        <td><?= $deliverable['title'] ?></td>
                        <td class="<?= $date_class ?>"><?= $date_formatted ?></td>
                        <td class="text-center"><?= $days_to_next ?></td>
                        <td class="text-center"><?= $note ?></td>
                    </tr>
                    <?php } ?>
                    
                    <?php if (empty($deliverable_phases)): ?>
                    <tr>
                        <td colspan="6" class="text-center py-3 text-muted">
                            <i class="fas fa-info-circle mr-1"></i> No deliverables scheduled yet
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="text-right">
    <div id="schedule-status-message" class="d-inline-block mr-3"></div>
    <button type="button" id="save-schedule-btn" class="btn btn-primary">
        <i class="fas fa-save"></i> Save Schedule
    </button>
</div>

<script type="text/javascript">
// Wait for document to be fully loaded and jQuery to be available
function initializeSchedule() {
    if (typeof jQuery === 'undefined') {
        // If jQuery isn't loaded yet, wait a bit and try again
        setTimeout(initializeSchedule, 100);
        return;
    }
    
    $(function() {
        // Toggle phase details
        $('.phase-toggle').on('change', function() {
            var phaseId = $(this).data('phase-id');
            var isEnabled = $(this).is(':checked');
            
            // Enable or disable the fields for this phase
            $('input[name^="phases[' + phaseId + ']"]').prop('disabled', !isEnabled);
            
            // Update summary if toggling deliverable
            var isDeliverable = $(this).closest('tr').find('.badge-success').length > 0;
            if (isDeliverable) {
                updateDeliverableSummary();
            }
        });
        
        // Update summary when date changes
        $(document).on('change', 'input[name$="[date]"]', function() {
            var phaseRow = $(this).closest('tr');
            if (phaseRow.length > 0) {
                var isDeliverable = phaseRow.find('.badge-success').length > 0;
                var isApplicable = phaseRow.find('.phase-toggle').is(':checked');
                
                if (isDeliverable && isApplicable) {
                    updateDeliverableSummary();
                }
            }
        });
        
        // Save schedule
        $('#save-schedule-btn').on('click', function() {
            $('#schedule-status-message').html('<span class="text-warning"><i class="fas fa-spinner fa-spin"></i> Saving...</span>');
            
            var formData = new FormData(document.getElementById('schedule-setup-form'));
            
            // Add phase applicability data
            $('.phase-toggle').each(function() {
                var phaseId = $(this).data('phase-id');
                var isApplicable = $(this).is(':checked');
                formData.append('phase_applicable[' + phaseId + ']', isApplicable ? '1' : '0');
            });
            
            $.ajax({
                url: '<?= base_url("app/project_schedule/save_schedule") ?>',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    try {
                        var data = JSON.parse(response);
                        if (data.status === 'success') {
                            $('#schedule-status-message').html('<span class="text-success"><i class="fas fa-check-circle"></i> ' + data.message + '</span>');
                            updateDeliverableSummary();
                            setTimeout(function() {
                                $('#schedule-status-message').html('');
                            }, 3000);
                        } else {
                            $('#schedule-status-message').html('<span class="text-danger"><i class="fas fa-exclamation-circle"></i> ' + data.message + '</span>');
                        }
                    } catch(e) {
                        $('#schedule-status-message').html('<span class="text-danger"><i class="fas fa-exclamation-circle"></i> Error processing server response</span>');
                    }
                },
                error: function(xhr, status, error) {
                    $('#schedule-status-message').html('<span class="text-danger"><i class="fas fa-exclamation-circle"></i> Error: ' + error + '</span>');
                }
            });
        });
        
        // Function to update the deliverables summary
        function updateDeliverableSummary() {
            var deliverables = [];
            
            $('.phase-toggle').each(function() {
                var phaseId = $(this).data('phase-id');
                var isApplicable = $(this).is(':checked');
                var isDeliverable = $(this).closest('tr').find('.badge-success').length > 0;
                
                if (isApplicable && isDeliverable) {
                    var dateInput = $('input[name="phases[' + phaseId + '][date]"]');
                    var date = dateInput.val();
                    
                    if (date) {
                        var title = $(this).closest('tr').find('strong').text().trim();
                        var phaseOrder = parseInt($(this).closest('tr').find('.badge-secondary').text().trim());
                        
                        // Get status if available from database
                        var status = 'pending'; // default
                        
                        deliverables.push({
                            id: phaseId,
                            title: title,
                            date: date,
                            order: phaseOrder,
                            status: status
                        });
                    }
                }
            });
            
            // Sort by date
            deliverables.sort(function(a, b) {
                return new Date(a.date) - new Date(b.date);
            });
            
            var tableHtml = '';
            
            if (deliverables.length === 0) {
                tableHtml = '<tr><td colspan="6" class="text-center py-3 text-muted"><i class="fas fa-info-circle mr-1"></i> No deliverables scheduled yet</td></tr>';
            } else {
                for (var i = 0; i < deliverables.length; i++) {
                    var deliverable = deliverables[i];
                    var date = new Date(deliverable.date);
                    
                    // Format date manually
                    var months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                    var dateFormatted = date.getDate() + ' ' + months[date.getMonth()] + ', ' + date.getFullYear();
                    
                    // Calculate days to next
                    var daysToNext = '';
                    if (i < deliverables.length - 1) {
                        var nextDate = new Date(deliverables[i + 1].date);
                        var timeDiff = Math.abs(nextDate.getTime() - date.getTime());
                        var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
                        daysToNext = diffDays + ' days';
                    }
                    
                    // Check special dates
                    var dayOfWeek = date.getDay();
                    var isSunday = (dayOfWeek === 0);
                    var isThirdSaturday = false;
                    
                    if (dayOfWeek === 6) { // Saturday
                        var dayOfMonth = date.getDate();
                        var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
                        var firstDayOfWeek = firstDay.getDay();
                        var firstSaturdayDate = (7 - firstDayOfWeek + 6) % 7 + 1;
                        var thirdSaturdayDate = firstSaturdayDate + 14;
                        
                        isThirdSaturday = (dayOfMonth === thirdSaturdayDate);
                    }
                    
                    var dateClass = (isSunday || isThirdSaturday) ? 'text-danger font-weight-bold' : '';
                    var note = '';
                    
                    if (isSunday) {
                        note = '<span class="badge badge-danger">Sunday</span>';
                    } else if (isThirdSaturday) {
                        note = '<span class="badge badge-danger">3rd Saturday</span>';
                    }
                    
                    // Set status color and icon
                    var statusColor = 'secondary';
                    var statusIcon = 'clock';
                    
                    switch(deliverable.status) {
                        case 'completed':
                            statusColor = 'success';
                            statusIcon = 'check';
                            break;
                        case 'pending':
                            statusColor = 'danger';
                            statusIcon = 'hourglass-start';
                            break;
                        case 'on_hold':
                            statusColor = 'warning';
                            statusIcon = 'pause';
                            break;
                        case 'in_progress':
                            statusColor = 'info';
                            statusIcon = 'spinner';
                            break;
                    }
                    
                    tableHtml += '<tr>' +
                        '<td class="text-center">' + (i + 1) + '</td>' +
                        '<td class="text-center">' + 
                            '<div class="d-inline-flex align-items-center justify-content-center bg-' + statusColor + ' text-white rounded-circle" style="width: 32px; height: 32px;">' +
                                '<i class="fas fa-' + statusIcon + '"></i>' +
                            '</div>' +
                        '</td>' +
                        '<td>' + deliverable.title + '</td>' +
                        '<td class="' + dateClass + '">' + dateFormatted + '</td>' +
                        '<td class="text-center">' + daysToNext + '</td>' +
                        '<td class="text-center">' + note + '</td>' +
                    '</tr>';
                }
            }
            
            $('#deliverables-summary').html(tableHtml);
        }
    });
}

// Start initialization
initializeSchedule();
</script>