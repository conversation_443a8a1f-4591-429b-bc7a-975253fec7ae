<div class="container-fluid">
    <!-- Header with back button and add button -->
    <div class="card shadow-sm mb-4">
        <div class="card-body d-flex1 py-3">
            <a href="<?= base_url("app/project_schedule/index"); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left mr-2"></i> Back
            </a>
            <?php
            if (has_permission('project_schedule/add')){
                ?>
                <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'.$_GET['project_id']); ?>/?page_name=project_schedule/schedule', '<?= get_phrase('update_project_schedule'); ?>')" class="btn btn-info float-right">
                    <i class="fas fa-calendar mr-2"></i> Set <?= $page_title ?? '' ?>
                </button>
                <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'.$_GET['project_id']); ?>/?page_name=project_schedule/pdf', 'Project Schedule PDF')" class="btn btn-primary float-right mr-4">
                    <i class="fas fa-file-pdf"></i> Download Schedule PDF
                </button>
                <?php
            }
            ?>
            
            <?php 
            if(has_permission('projects/edit')){
                ?>
                <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'.$_GET['project_id']); ?>/?page_name=projects/edit', 'Update Project')" class="btn btn-secondary float-right mr-4">
                    <i class="fas fa-edit"></i> Update Project
                </button>
                <?php
            }
            ?>
        </div>
    </div>

    <!-- Main content -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0 text-primary font-weight-bold">PROJECT SCHEDULE - <?=strtoupper($projects[$_GET['project_id'] ?? 0] ?? '')?></h5>
        </div>
        <!-- Project Details Card -->
        <div class="p-2">
            <div class="card-body pb-4" style="background-color: #f3f9f9;max-width:850px;">
                <h6 class="text-primary font-weight-bold mb-3" style="color: #20623d !important;">
                    <i class="fas fa-info-circle mr-2"></i>PROJECT DETAILS
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex mb-3 align-items-center">
                            <div class="rounded-circle p-2 mr-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; background-color: #20623d; color: white;">
                                <i class="fas fa-project-diagram"></i>
                            </div>
                            <div>
                                <label class="text-muted small mb-0">Project Type</label>
                                <div class="font-weight-medium"><?= isset($project_details['project_type']) ? $project_details['project_type'] : 'N/A' ?></div>
                            </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                            <div class="rounded-circle p-2 mr-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; background-color: #20623d; color: white;">
                                <i class="fas fa-user-tie"></i>
                            </div>
                            <div>
                                <label class="text-muted small mb-0">Project Lead</label>
                                <div class="font-weight-medium"><?= isset($project_details['project_lead']) ? $project_details['project_lead'] : 'N/A' ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex mb-3 align-items-center">
                            <div class="rounded-circle p-2 mr-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; background-color: #20623d; color: white;">
                                <i class="fas fa-vial"></i>
                            </div>
                            <div>
                                <label class="text-muted small mb-0">Tester</label>
                                <div class="font-weight-medium"><?= isset($project_details['tester']) ? $project_details['tester'] : 'N/A' ?></div>
                            </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                            <div class="rounded-circle p-2 mr-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; background-color: #20623d; color: white;">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <label class="text-muted small mb-0">Team Members</label>
                                <div class="font-weight-medium"><?= isset($project_details['team_members']) ? $project_details['team_members'] : 'N/A' ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Filter form -->
        <div class="card-body border-bottom">
            <form action="" method="get">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="project_id" class="form-label text-muted">Project</label>
                        <select class="form-control select2" id="project_id" name="project_id">
                            <option>Choose Project</option>
                            <?php
                            if (isset($projects)){
                                foreach ($projects as $project_id => $project){
                                    $selected = $_GET["project_id"] == $project_id ? 'selected' : '';
                                    echo "<option value='{$project_id}' {$selected}>{$project}</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="start_date" class="form-label text-muted">Start Date</label>
                        <input type="date" class="form-control" name="start_date" id="start_date" value="<?=$_GET['start_date']?>">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="end_date" class="form-label text-muted">End Date</label>
                        <input type="date" class="form-control" name="end_date" id="end_date" value="<?=$_GET['end_date']?>">
                    </div>
                    <div class="col-md-2 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-filter mr-2"></i> Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Status legend -->
        <div class="card-body border-bottom pb-2">
            <div class="d-flex">
                <div class="mr-4 d-flex align-items-center">
                    <span class="status-indicator pending mr-2"></span> Pending
                </div>
                <div class="mr-4 d-flex align-items-center">
                    <span class="status-indicator in-progress mr-2"></span> In Progress
                </div>
                <div class="mr-4 d-flex align-items-center">
                    <span class="status-indicator completed mr-2"></span> Completed
                </div>
                <div class="d-flex align-items-center">
                    <span class="status-indicator on-hold mr-2"></span> On Hold
                </div>
            </div>
        </div>

        <!-- Schedule items list -->
        <div class="card-body">
            <?php if (isset($list_items) && isset($projects) && isset($users) && !empty($list_items)): ?>
                <div class="schedule-container">
                    <?php foreach ($list_items as $key => $item): ?>
                        <?php
                        // Determine status
                        $status = $item['status'] ?? 'pending';
                        $status_class = '';
                        $status_icon = '';
                        
                        if ($status === 'completed') {
                            $status_class = 'completed';
                            $status_icon = '<i class="fas fa-check"></i>';
                        } elseif ($status === 'on_hold') {
                            $status_class = 'on-hold';
                            $status_icon = '<i class="fas fa-pause"></i>';
                        } elseif ($status === 'in_progress') {
                            $status_class = 'in-progress';
                            $status_icon = '<i class="fas fa-spinner"></i>';
                        } else { // pending
                            $status_class = 'pending';
                            $status_icon = '<i class="fas fa-clock"></i>';
                        }
                        
                        
                        // Get assigned users
                        // $user_ids = json_decode($item['user_id'], true) ?: [];
                        $assigned_users = [];
                        foreach ($item['user_id'] as $user_id) {
                            if (isset($users[$user_id])) {
                                $assigned_users[] = $users[$user_id];
                            }
                        }
                        ?>
                        
                        <div class="schedule-item mb-4">
                            <div class="schedule-item-content">
                                <!-- Status indicator -->
                                <div class="schedule-status <?= $status_class ?>" data-toggle="tooltip" title="<?= ucfirst($status) ?>">
                                    <?= $status_icon ?>
                                </div>
                                
                                <!-- Date section -->
                                <div class="schedule-date">
                                    <div class="date-container">
                                        <?php 
                                            if(!empty($item['date'])){
                                                $date_obj = DateTime::createFromFormat('Y-m-d', $item['date']);
                                                $month = $date_obj->format('M');
                                                $day = $date_obj->format('d');
                                                $year = $date_obj->format('Y');
                                                ?>
                                                <span class="schedule-day"><?= $day ?></span>
                                                <div class="date-right">
                                                    <span class="schedule-month"><?= strtoupper($month) ?></span>
                                                    <span class="schedule-year"><?= $year ?></span>
                                                </div>
                                                <?php
                                            }else{
                                                ?>
                                                <span class="text-danger">
                                                    No schedule
                                                </span>
                                                <?php
                                            }
                                        ?>
                                    </div>
                                </div>
                                
                                <!-- Info section -->
                                <div class="schedule-info">
                                    <div class="schedule-project"><?= get_project_title_third($projects[$item['project_id']]) ?></div>
                                    <h5 class="schedule-title"><?= $item['title'] ?></h5>
                                    
                                    <?php if (!empty($assigned_users)): ?>
                                    <div class="schedule-users">
                                        <i class="fas fa-users mr-2"></i>
                                        <?= implode(', ', $assigned_users) ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Actions section -->
                                <div class="schedule-actions">
                                    <?php if (has_permission('project_schedule/edit') && $item['schedule_id'] > 0): ?>
                                        <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['schedule_id'].'/?page_name=project_schedule/edit'); ?>', '<?php echo get_phrase('update_project_schedule'); ?>')" class="btn btn-sm btn-outline-primary mr-2">
                                            Update <i class="fas fa-edit"></i>
                                        </button>
                                    <?php endif; ?>
                                
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-2"></i> No schedule items found.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* Custom styling for schedule items */
.schedule-container {
    display: flex;
    flex-direction: column;
}

.schedule-item {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.schedule-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.schedule-item-content {
    display: flex;
    align-items: center;
    padding: 16px;
}

/* Status indicator styles */
.schedule-status {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 20px;
    flex-shrink: 0;
}

.schedule-status.pending {
    background-color: #fee2e2;
    color: #dc2626;
}

.schedule-status.in-progress {
    background-color: #dbeafe;
    color: #2563eb;
}

.schedule-status.completed {
    background-color: #dcfce7;
    color: #16a34a;
}

.schedule-status.on-hold {
    background-color: #fef3c7;
    color: #d97706;
}

/* Date section */
.schedule-date {
    width: 120px;
    margin-right: 24px;
    flex-shrink: 0;
}

.date-container {
    display: flex;
    align-items: center;
    background-color: #f9fafb;
    border-radius: 8px;
    padding: 8px 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.schedule-day {
    font-weight: 700;
    font-size: 30px;
    color: #1f2937;
    line-height: 1;
    margin-right: 10px;
}

.date-right {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.schedule-month {
    font-weight: 600;
    font-size: 14px;
    color: #4b5563;
    text-transform: uppercase;
}

.schedule-year {
    font-size: 14px;
    color: #6b7280;
}

/* Info section */
.schedule-info {
    flex-grow: 1;
    min-width: 0;
    padding: 0 20px;
}

.schedule-project {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 4px;
}

.schedule-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.schedule-users {
    font-size: 14px;
    color: #4b5563;
    background-color: #f3f4f6;
    padding: 6px 12px;
    border-radius: 4px;
    display: inline-block;
}

/* Actions section */
.schedule-actions {
    display: flex;
    align-items: center;
    margin-left: 16px;
}

/* Status legend indicators */
.status-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.pending {
    background-color: #fee2e2;
    border: 2px solid #dc2626;
}

.status-indicator.in-progress {
    background-color: #dbeafe;
    border: 2px solid #2563eb;
}

.status-indicator.completed {
    background-color: #dcfce7;
    border: 2px solid #16a34a;
}

.status-indicator.on-hold {
    background-color: #fef3c7;
    border: 2px solid #d97706;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .schedule-item-content {
        flex-wrap: wrap;
    }
    
    .schedule-status {
        margin-right: 15px;
    }
    
    .schedule-date {
        width: 110px;
        margin-right: 15px;
    }
    
    .schedule-info {
        width: 100%;
        order: 1;
        margin-top: 15px;
        padding: 0;
    }
    
    .schedule-actions {
        margin-left: auto;
    }
}
</style>

<script type="application/javascript">
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Initialize select2 if it exists
    if ($.fn.select2) {
        $('.select2').select2({
            width: '100%'
        });
    }
});
</script>