<?php
$item_id = $param1;
$schedule_data = $this->project_schedule_m->get(['id' => $item_id])->row_array();

// Get phase details
$phase_data = $this->project_phases_m->get(['id' => $schedule_data['phase_id']])->row_array();

// Get all users without any filtering
$all_users = $this->users_m->get(['employee_status' => 1, 'work_assign' => 1], ['id', 'name'])->result_array();

// Get current users assigned to this schedule, or from phase if none assigned
$assigned_user_ids = [];
if (!empty($schedule_data['user_id'])) {
    $assigned_user_ids = json_decode($schedule_data['user_id'], true) ?: [];
} else if (!empty($phase_data['user_id'])) {
    $assigned_user_ids = json_decode($phase_data['user_id'], true) ?: [];
}

// Convert to array of strings for safer comparison
$assigned_user_ids = array_map('strval', $assigned_user_ids);

// Format date for display
$date_obj = DateTime::createFromFormat('Y-m-d', $schedule_data['date']);
$formatted_date = $date_obj ? $date_obj->format('d M, Y') : '';
?>

<div class="p-1">
    <!-- Phase Title and Date Header -->
    <div class="mb-4 d-flex justify-content-between align-items-center" style="background-color:#efefef;padding:10px;">
        <div class="phase-info">
            <h5 class="text-primary mb-0"><?= $phase_data['title'] ?></h5>
            <div class="text-muted small" style="font-size: 16px;">
                <i class="fas fa-calendar-alt mr-1"></i> Scheduled: <?= $formatted_date ?>
            </div>
        </div>
        <div class="phase-badge">
            <?php if($phase_data['is_deliverable']): ?>
                <span class="badge badge-success">Deliverable</span>
            <?php endif; ?>
        </div>
    </div>

    <form id="edit-schedule-form" action="<?= base_url('app/project_schedule/update_schedule_item/'.$item_id) ?>" method="post">
        <input type="hidden" name="id" value="<?= $item_id ?>">
        <input type="hidden" name="phase_id" value="<?= $schedule_data['phase_id'] ?>">
        <input type="hidden" name="project_id" value="<?= $schedule_data['project_id'] ?>">
        
        <!-- Status Selection -->
        <div class="form-group">
            <label for="status" class="text-muted font-weight-bold">Status <span class="text-danger">*</span></label>
            <select class="form-control" id="status" name="status" required>
                <option value="pending" <?= ($schedule_data['status'] == 'pending' || empty($schedule_data['status'])) ? 'selected' : '' ?>>Pending</option>
                <option value="in_progress" <?= ($schedule_data['status'] == 'in_progress') ? 'selected' : '' ?>>In Progress</option>
                <option value="on_hold" <?= ($schedule_data['status'] == 'on_hold') ? 'selected' : '' ?>>On Hold</option>
                <option value="completed" <?= ($schedule_data['status'] == 'completed') ? 'selected' : '' ?>>Completed</option>
            </select>
        </div>
        
        <!-- Users Assignment -->
        <div class="form-group">
            <label for="assigned_users" class="text-muted font-weight-bold">Assigned Users</label>
            <select class="form-control select2" id="assigned_users" name="user_id[]" multiple>
                <?php 
                if(empty($all_users)) {
                    echo '<option value="">No users found</option>';
                } else {
                    foreach ($all_users as $user): 
                        $selected = in_array($user['id'], $assigned_user_ids) ? 'selected' : '';
                        echo "<option value='{$user['id']}' {$selected}>{$user['name']}</option>";
                    endforeach;
                }
                ?>
            </select>
            <small class="text-muted">You can select multiple users</small>
        </div>
        
        <!-- Remarks -->
        <div class="form-group">
            <label for="remarks" class="text-muted font-weight-bold">Remarks</label>
            <textarea class="form-control" id="remarks" name="remarks" rows="3"><?= $schedule_data['remarks'] ?? '' ?></textarea>
        </div>
        
        <div class="form-group mb-0 text-right">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save mr-1"></i> Update Schedule
            </button>
        </div>
    </form>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Initialize select2 AFTER ensuring the DOM is loaded
    $(window).on('load', function() {
        console.log("Window loaded, initializing Select2...");
        try {
            $('#assigned_users').select2({
                width: '100%',
                dropdownParent: $('#large-modal')
            });
        } catch(e) {
            console.error("Select2 initialization error:", e);
        }
    });
    
    // Submit form via AJAX
    $('#edit-schedule-form').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // Close modal and refresh page
                    $('#large-modal').modal('hide');
                    if (typeof refreshScheduleList === 'function') {
                        refreshScheduleList();
                    } else {
                        window.location.reload();
                    }
                } else {
                    alert(response.message || 'Error updating schedule');
                }
            },
            error: function(xhr, status, error) {
                alert('An error occurred while updating the schedule');
                console.error(xhr.responseText);
            }
        });
    });
    
    // Visual feedback for status selection
    $('#status').on('change', function() {
        var status = $(this).val();
        var statusColors = {
            'pending': '#dc3545', // Red
            'in_progress': '#007bff', // Blue
            'on_hold': '#fd7e14', // Orange
            'completed': '#28a745' // Green
        };
        
        $(this).css('border-left', '4px solid ' + (statusColors[status] || '#6c757d'));
    }).trigger('change'); // Apply initial styling
});
</script>

<script type="application/javascript">
$('.select2').select2();
</script>