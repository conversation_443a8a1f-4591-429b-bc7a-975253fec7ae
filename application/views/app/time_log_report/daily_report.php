<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.3.3/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
<div class="container-fluid p-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>


    </div>
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title">
                <?= strtoupper($page_title ?? '')?>
                (<?= DateTime::createFromFormat('Y-m-d', $_GET['log_date'])->format('d-m-Y')?>)
            </h3>
            <div class="float-right">
                <input type="date" name="log_date" onchange="get_value(this.value)" value="<?=$_GET['log_date']?>"
                       class="form-control" style="font-size: 18px;border-radius: 0!important;width: 160px;">
            </div>
            <?php
                if (is_hr() || get_user_id() == 57){
                    ?>
                    <div class="float-right p-1 pr-3">
                        <a href="<?= base_url("app/time_log/daily_log/?log_date={$_GET['log_date']}"); ?>" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-pencil-alt"></i> Update Attendance
                        </a>
                    </div>
                    <?php
                }
            ?>

        </div>
        <!-- /.card-header -->
        <div class="card-body p-1" id="download_area">
            <?php
            if (isset($users) && isset($time_log) && isset($time_log_type) && isset($_GET['log_date']) && isset($attendance_data)) {
                ?>
                <div class="shadow-pro pt-2 pb-2 bg-white p-1" >
                    <div class="p-3 text-center">
                        <div style="font-size: 24px; font-weight: bold">Time Log Report - <?= DateTime::createFromFormat('Y-m-d', $_GET['log_date'])->format('d-m-Y, l')?></div>
                    </div>
                    <table class="table table-bordered">
                        <tr style="background-color: #f1f2f8; font-size: 13px">
                            <th>SL.NO</th>
                            <th>E. CODE</th>
                            <th>NAME</th>
                            <th>ATT</th>
                            <?php
                            foreach ($time_log_type as $log_type){
                                echo "<th>";
                                if ($log_type['id']==1 || $log_type['id']==8){
                                    echo '<span>'.strtoupper(get_phrase($log_type['title'])).'</span>';
                                }else{
                                    echo '<small><b>'.strtoupper(get_phrase($log_type['title'])).'</b></small>';
                                }
                                echo "</th>";
                            }
                            ?>
                            <th>TOTAL</th>
                            <th>DIFF</th>
                            <th>BREAK</th>
                        </tr>
                        <?php
                        foreach ($users as $key => $user) {

                            $attendance = $attendance_data[$user['id']]['attendance'] ?? '';
                            $remarks = $attendance_data[$user['id']]['remarks'] ?? '';
                            if (!empty($attendance_data[$user['id']]['off_date'])){
                                $off_date = DateTime::createFromFormat('Y-m-d', $attendance_data[$user['id']]['off_date'])->format('d-m-Y, l');
                            }else{
                                $off_date = '';
                            }

                            ?>
                            <tr>
                                <td><?=$key+1?></td>
                                <th style="background-color: #f5f9fd;font-size:23px" class="text-muted"><?=$user['employee_code']?></th>
                                <th style="background-color: #f5fdfc;">
                                    <?=$user['name']?>
                                    <div class="pt-2">
                                        <button class="btn btn-outline-secondary btn-sm pull-right" style="padding: 1px 3px!important;font-size: 11px!important;"
                                                onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'.$user['id']); ?>/?page_name=dashboard/eemployee_time_log_report', '<?= get_phrase('time_log_report').' - '.$user['name']; ?>')">
                                            Detailed Report
                                        </button>
                                    </div>
                                </th>
                                <th style="background-color: rgba(246,243,243,0.41);" class="text-center"><?=$attendance?></th>
                                <?php
                                if ($attendance != 'P' && $attendance != 'HD'){
                                    echo '<td class="text-center" colspan="8" style="background-color: #eff2f5">';
                                    echo "<b>{$attendance}</b>";
                                    if (!empty($remarks)){
                                        echo " (<small>{$remarks}</small>)";
                                    }
                                    if ($attendance =='OF'){
                                        echo "<br><small>OFF FOR - {$off_date}</small>";
                                    }
                                    echo '</td>';
                                }else{
                                    foreach ($time_log_type as $log_type){
                                        ?>
                                        <td class="text-center" style="font-size: 15px;">
                                            <?php
                                            if (!empty($time_log[$user['id']][$log_type['id']])){
                                                echo print_punch_time($time_log[$user['id']][$log_type['id']], $log_type['id']);
                                            }else{
                                                echo '-';
                                            }
                                            ?>
                                        </td>
                                        <?php
                                    }
                                }

                                ?>
                                <td style="background-color: #f5fdfc;font-size: 16px;" class="text-center">
                                    <?php
                                        if (!empty($time_log[$user['id']]['total_duration']) && duration_to_time($time_log[$user['id']]['total_duration']) != '00:00'){
                                            echo print_daily_time(duration_to_time($time_log[$user['id']]['total_duration']));
                                        }else{
                                            echo '-';
                                        }
                                    ?>
                                </td>
                                <td style="background-color: rgba(247,245,248,0.82);font-size: 14px;" class="text-center">
                                    <?php
                                    if (!empty($time_log[$user['id']]['total_duration']) && duration_to_time($time_log[$user['id']]['total_duration']) != '00:00'){
                                        echo print_daily_diff(duration_to_time($time_log[$user['id']]['total_duration']));
                                    }else{
                                        echo '-';
                                    }
                                    ?>
                                </td>
                                <td style="background-color: rgba(247,245,248,0.82);font-size: 14px;" class="text-center">
                                    <?php
                                    if (!empty($time_log[$user['id']]['total_break']) && duration_to_time($time_log[$user['id']]['total_break']) != '00:00'){
                                        echo print_break_time(duration_to_time($time_log[$user['id']]['total_break']));
                                    }else{
                                        echo '-';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </table>

                </div>

                <?php
            }
            ?>

        </div>
        <div class="text-center p-2">
            <button id="download_image" class="btn btn-primary">Download Image</button>
            <button id="download_pdf" class="btn btn-primary">Download PDF</button>
        </div>
        <div id="canvasContainer">
            <canvas id="canvas" style="display: none"></canvas>
        </div>
        <div id="imageContainer" style="display: none"></div>
        <!-- /.card-body -->
    </div>
</div>

<script>
    $(document).ready(function() {
        $("#download_pdf").click(function() {
            var tableToConvert = document.getElementById('download_area');

            html2canvas(tableToConvert, {
                width: tableToConvert.offsetWidth,
                height: tableToConvert.offsetHeight
            }).then(function(canvas) {
                var imgData = canvas.toDataURL('image/png');

                var pdf = new jsPDF();
                pdf.addImage(imgData, 'PNG', 10, 10);  // coordinates (10, 10) define the position in the PDF. Adjust as needed.
                pdf.save("attendance_report_<?=$_GET['log_date']?>.pdf");
            });


        });

        $('#download_image').on('click', function() {
            // Select the table you want to convert
            var tableToConvert = document.getElementById('download_area');

            // Convert the table to a canvas
            html2canvas(tableToConvert, {
                width: tableToConvert.offsetWidth,
                height: tableToConvert.offsetHeight
            }).then(function(canvas) {
                // Hide the canvas
                document.getElementById('canvas').style.display = 'none';

                // Convert the canvas to an image
                var image = new Image();
                image.src = canvas.toDataURL('image/png');

                // Append the image to a container
                var imageContainer = document.getElementById('imageContainer');
                imageContainer.innerHTML = ''; // Clear previous content
                imageContainer.appendChild(image);

                // Create a hidden link to trigger automatic download
                var downloadLink = document.createElement('a');
                downloadLink.href = image.src;
                downloadLink.download = 'attendance_report_<?=$_GET['log_date']?>.png'; // Set the desired download filename
                downloadLink.style.display = 'none'; // Hide the link
                imageContainer.appendChild(downloadLink);

                // Simulate a click on the download link
                downloadLink.click();
            });
        });
    });
</script>
<script type="text/javascript">
    function get_value(date){
        window.location.href='<?=base_url('app/time_log_report/daily_report/?log_date=')?>' + date;
    }
</script>


