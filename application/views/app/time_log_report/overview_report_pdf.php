<?php
if (isset($start_date) && isset($end_date) && isset($users)){
    $work_date = DateTime::createFromFormat('Y-m-d', $start_date)->format('F, Y');
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Employee Monthly Report : <?=$work_date?></title>
    </head>
    <body style="background-color: #f5faf8">
    <div class="header">
        <div class="col-6">
            <div id="logo_title" style="padding: 10px;padding-left:13px;padding-bottom:5px;font-size: 14px">
                <a href="https://pms.trogon.info" target="_blank" style="text-decoration: none; color: azure">pms.trogon.info</a>
            </div>
            <div id="logo" style="padding: 10px;padding-top: 2px;">
                <a href="https://pms.trogon.info" target="_blank">
                    <img src="<?=base_url('assets/logo/logo.png')?>" alt="Trogon Logo" style="width: 115px;height: auto">
                </a>
            </div>

        </div>
        <div class="col-6" style="text-align: right">
            <div class="header_title">Employee Monthly Report</div>
            <div class="header_date" style="color: #bdecec!important;">[ <?=strtoupper($work_date)?> ]</div>

        </div>
    </div>
    <div class="main">
        <table class="table table-bordered" style="color: #333">
            <tr style="background-color: #f1f2f8; font-size: 13px">
                <th>Rank</th>
                <th>NAME</th>
                <th>P</th>
                <th>OD</th>
                <th>A</th>
                <th>WH</th>
                <th>OF</th>
                <th>HD</th>
                <th>Late</th>
                <th>Early</th>
                <th>Productive</th>
                <th>Office</th>
                <th>Break</th>
                <th>Attendance Points</th>
                <th>Job Points</th>
                <th>Total Points</th>
            </tr>
            <?php
            foreach ($users as $key => $user) {
                if ($user['is_performance'] == 0){
                    $rank_style = 'background-color: #f1bdc6;';
                    $row_style = 'style="opacity:0.8!important;background-color:#effefef!important;"';
                }else{
                    $rank_style = 'background-color: #eff5f4;font-size:23px;color: #268a82';
                    $row_style = '';
                }
                ?>
                <tr <?=$row_style?>>
                    <th style="<?=$rank_style?>" class="text-muted1"><?=$user['rank']?></th>
                    <th style="background-color: #f5fdfc;width: 110px">
                        <?=$user['name']?>
                    </th>
                    <th style="background-color: rgba(225,248,240,0.41);color: #0f6629" class="text-center"><?=$user['attendance_overview']['P']?></th>
                    <th style="background-color: rgba(225,248,240,0.41);" class="text-center"><?=$user['attendance_overview']['OD']?></th>
                    <th style="background-color: rgba(246,181,181,0.23);color: #a12829;" class="text-center"><?=$user['attendance_overview']['A']?></th>
                    <th style="background-color: rgba(241,208,208,0.23);color: #a12829;" class="text-center"><?=$user['attendance_overview']['WH']?></th>
                    <th style="background-color: rgba(234,243,248,0.41);" class="text-center"><?=$user['attendance_overview']['OF']?></th>
                    <th style="background-color: rgba(234,243,248,0.41);" class="text-center"><?=$user['attendance_overview']['HD']?></th>
                    <th style="background-color: rgba(239,189,189,0.23);color: #a12829" class="text-center"><?=$user['attendance_overview']['late_coming']?></th>
                    <th style="background-color: rgba(239,189,189,0.23);color: #a12829" class="text-center"><?=$user['attendance_overview']['early_going']?></th>
                    <th style="background-color: #eff5f1;color: #0f5022; font-size: 18px;" class="text-center"><?=$user['time_log_overview']['work_duration']?></th>
                    <th style="background-color: rgba(246,243,243,0.41); font-size: 12px;" class="text-center"><?=$user['time_log_overview']['office_duration']?></th>
                    <th style="background-color: rgba(239,189,189,0.23); color: #a12829; font-size:14px;" class="text-center"><?=$user['time_log_overview']['break_duration']?></th>
                    <th style="background-color: #eff5f4;color: #268a7b; font-size: 17px;" class="text-center">
                        <?=$user['attendance_points']?>
                    </th>
                    <th style="background-color: #eff5f5;color: #268a82; font-size: 17px;" class="text-center">
                        <?=$user['job_points']?>
                    </th>
                    <th style="background-color: #eff5f1;color: #268a43; font-size: 19px;" class="text-center">
                        <?=$user['employee_points']?>
                    </th>
                </tr>
                <?php
            }
            ?>
        </table>
    </div>
    <style>
        td, th{
            padding: 23px 8px;
        }
        .col-6{
            width: 50%!important;
            float: left!important;
        }
        body{
            border:1px solid #454545;
            font-family: Arial, sans-serif;
        }
        .header{
            background-image: linear-gradient(90deg, #23384E, #166686)!important;
            color:#efefef;
            padding:15px 5px;
            border-radius: 7px;
        }
        .header_title{
            font-size: 21px;
            font-weight: bold;
            padding: 5px 10px;
        }
        .header_date{
            padding: 2px 10px 10px;
            font-weight: bold;
            font-size: 17px;
            color: azure;
        }
    </style>
    </body>
    </html>
    <?php
}
?>