<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.3.3/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
<div class="container-fluid p-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>


    </div>
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title">
                <?= strtoupper($page_title ?? '')?>
                (<?= DateTime::createFromFormat('Y-m-d', $_GET['start_date'])->format('d-m-Y')?>)
            </h3>
            <div class="float-right">
                <input type="date" name="end_date" id="end_date" onchange="get_value()" value="<?=$_GET['end_date']?>"
                       class="form-control" style="font-size: 18px;border-radius: 10px!important;width: 160px;">
            </div>
            <div class="float-right p-2">to</div>
            <div class="float-right mr-2">
                <input type="date" name="start_date" id="start_date" onchange="get_value()" value="<?=$_GET['start_date']?>"
                       class="form-control" style="font-size: 18px;border-radius: 10px!important;width: 160px;">
            </div>
            <div class="float-right mr-2">
                <input type="number" name="working_days" id="working_days" onchange="get_value()" value="<?=$_GET['working_days']?>"
                       class="form-control" style="font-size: 18px;border-radius: 10px!important;width: 80px;">
            </div>

        </div>
        <!-- /.card-header -->
        <div class="card-body p-1" id="download_area">
            <?php
            if (isset($users)) {
                ?>
                <div class="pt-2 pb-2 bg-white p-1" >
                    <div class="p-3 text-center" style="background-color: #eaf9fe">
                        <?php
                        $start_date = DateTime::createFromFormat('Y-m-d', $_GET['start_date'])->format('d-m-Y');
                        $end_date = DateTime::createFromFormat('Y-m-d', $_GET['end_date'])->format('d-m-Y');
                        ?>
                        <div style="font-size: 22px; font-weight: bold">Time Log Report <br> <small>(<?="<b>{$start_date}</b> to <b>{$end_date}</b>"?>)</small></div>
                    </div>
                    <table class="table table-bordered" style="color: #333">
                        <tr style="background-color: #f1f2f8; font-size: 13px">
                            <th>E. CODE</th>
                            <th>Rank</th>
                            <th>NAME</th>
                            <th>P</th>
                            <th>OD</th>
                            <th>A</th>
                            <th>WH</th>
                            <th>OF</th>
                            <th>HD</th>
                            <th>Late</th>
                            <th>Early</th>
                            <th>Punch In</th>
                            <th>AVG/ EFFECT</th>
                            <th>Office</th>
                            <th>Break</th>
                            <th>Punctuality Points</th>
                            <th>Job Points</th>
                            <th>Total Points</th>
                        </tr>
                        <?php
                        foreach ($users as $key => $user) {
                            if ($user['is_performance'] == 0){
                                $rank_style = 'background-color: #f1bdc6;';
                                $row_style = 'style="opacity:0.8!important;background-color:#effefef!important;"';
                            }else{
                                $rank_style = 'background-color: #eff5f4;font-size:23px;color: #268a82';
                                $row_style = '';
                            }
                            ?>
                            <tr <?=$row_style?>>
                                <th style="background-color: #e9eff6;font-size:16px" class="text-muted"><?=$user['employee_code']?></th>
                                <th style="<?=$rank_style?>" class="text-muted1"><?=$user['rank']?></th>
                                <th style="background-color: #f5fdfc;">
                                    <?=$user['name']?>
                                    <div class="pt-2">
                                        <button class="btn btn-outline-secondary btn-sm pull-right" style="padding: 1px 3px!important;font-size: 11px!important;"
                                                onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'.$user['id']); ?>/<?=$_GET['start_date']?>/<?=$_GET['end_date']?>?page_name=dashboard/eemployee_time_log_report', '<?= get_phrase('time_log_report').' - '.$user['name']; ?>')">
                                            Detailed Report
                                        </button>
                                    </div>
                                </th>
                                <th style="background-color: rgba(225,248,240,0.41);color: #0f6629" class="text-center"><?=$user['attendance_overview']['P']?></th>
                                <th style="background-color: rgba(225,248,240,0.41);" class="text-center"><?=$user['attendance_overview']['OD']?></th>
                                <th style="background-color: rgba(246,181,181,0.23);color: #a12829;" class="text-center"><?=$user['attendance_overview']['A']?></th>
                                <th style="background-color: rgba(241,208,208,0.23);color: #a12829;" class="text-center"><?=$user['attendance_overview']['WH']?></th>
                                <th style="background-color: rgba(234,243,248,0.41);" class="text-center"><?=$user['attendance_overview']['OF']?></th>
                                <th style="background-color: rgba(234,243,248,0.41);" class="text-center"><?=$user['attendance_overview']['HD']?></th>
                                <th style="background-color: rgba(239,189,189,0.23);color: #a12829" class="text-center"><?=$user['attendance_overview']['late_coming']?></th>
                                <th style="background-color: rgba(239,189,189,0.23);color: #a12829" class="text-center"><?=$user['attendance_overview']['early_going']?></th>
                                <th style="background-color: #eff5f1;color: #0f5022; font-size: 18px;" class="text-center"><?=$user['time_log_overview']['work_duration']?></th>
                                <th style="background-color: #eff5f1;color: #268a43; font-size: 19px;" class="text-center"><?=number_format($user['time_log_overview']['work_duration']/($_GET['working_days']-$user['attendance_overview']['OD']), 2)?></th>
                                <th style="background-color: rgba(246,243,243,0.41); font-size: 14px;" class="text-center"><?=$user['time_log_overview']['office_duration']?></th>
                                <th style="background-color: rgba(239,189,189,0.23); color: #a12829; font-size:14px;" class="text-center"><?=$user['time_log_overview']['break_duration']?></th>
                                <th style="background-color: #eff5f4;color: #268a7b; font-size: 17px;" class="text-center">
                                    <?=$user['attendance_points']?>
                                </th>
                                <th style="background-color: #eff5f5;color: #268a82; font-size: 17px;" class="text-center">
                                    <?=$user['job_points']?>
                                </th>
                                <th style="background-color: #eff5f1;color: #268a43; font-size: 19px;" class="text-center">
                                    <?=$user['employee_points']?>
                                </th>
                            </tr>
                            <?php
                        }
                        ?>
                    </table>

                </div>

                <?php
            }
            ?>

        </div>
        <div class="text-center p-2">
            <button id="download_image" class="btn btn-primary">Download Image</button>
            <a href="<?=$pdf_url??''?>" class="btn btn-primary" download="">Download PDF</a>
        </div>
        <div id="canvasContainer">
            <canvas id="canvas" style="display: none"></canvas>
        </div>
        <div id="imageContainer" style="display: none"></div>
        <!-- /.card-body -->
    </div>
</div>

<script>
    $(document).ready(function() {
        $("#download_pdf").click(function() {
            var tableToConvert = document.getElementById('download_area');

            html2canvas(tableToConvert, {
                width: tableToConvert.offsetWidth,
                height: tableToConvert.offsetHeight
            }).then(function(canvas) {
                var imgData = canvas.toDataURL('image/png');

                var pdf = new jsPDF();
                pdf.addImage(imgData, 'PNG', 10, 10);  // coordinates (10, 10) define the position in the PDF. Adjust as needed.
                pdf.save("attendance_report_<?=$_GET['log_date']?>.pdf");
            });


        });

        $('#download_image').on('click', function() {
            // Select the table you want to convert
            var tableToConvert = document.getElementById('download_area');

            // Convert the table to a canvas
            html2canvas(tableToConvert, {
                width: tableToConvert.offsetWidth,
                height: tableToConvert.offsetHeight
            }).then(function(canvas) {
                // Hide the canvas
                document.getElementById('canvas').style.display = 'none';

                // Convert the canvas to an image
                var image = new Image();
                image.src = canvas.toDataURL('image/png');

                // Append the image to a container
                var imageContainer = document.getElementById('imageContainer');
                imageContainer.innerHTML = ''; // Clear previous content
                imageContainer.appendChild(image);

                // Create a hidden link to trigger automatic download
                var downloadLink = document.createElement('a');
                downloadLink.href = image.src;
                downloadLink.download = 'attendance_report_<?=$_GET['log_date']?>.png'; // Set the desired download filename
                downloadLink.style.display = 'none'; // Hide the link
                imageContainer.appendChild(downloadLink);

                // Simulate a click on the download link
                downloadLink.click();
            });
        });
    });
</script>
<script type="text/javascript">
    function get_value(){
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        var working_days = $('#working_days').val();
        window.location.href='<?=base_url('app/time_log_report/overview_report/?')?>' + 'start_date='+start_date+'&end_date='+end_date+'&working_days=' + working_days;
    }
</script>


