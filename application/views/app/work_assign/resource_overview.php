<div class="container-fluid pt-0 mt-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left btn-round">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <div class="float-right">
            <input type="date" class="form-control" style="width: 150px;" onchange="get_value(this.value)" value="<?=$_GET['job_date']?>">
        </div>
    </div>
    <div class="p-3">
        <div class="row">
            <?php
            if (isset($users) && isset($projects)){
                foreach ($users as $user){
                    $name_bg_color = $user['work_status'] == 'working' ? '#0fad3f': '#e15649';
                    ?>
                    <div class="col-4 p-2">
                        <div class="shadow-pro bg-white p-2 user_card">
                            <h5 class="user_card_name" style="background-color: <?=$name_bg_color?>">
                                <?=strtoupper($user['name'])?>
                            </h5>
                            <div style="background-color: aliceblue" class="p-1">
                                <?php
                                if (is_array($user['tasks'])){
                                    foreach ($user['tasks'] as $project_id => $task){
                                        ?>
                                        <div class="p-2">
                                            <div class="project_title bg-white p-2">
                                                <?= $projects[$project_id];?>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            }
            ?>
        </div>
    </div>
</div>

<style>
    .user_card{
        height: 300px;
        border-radius: 5px;
    }
    .user_card_name{
        color: #fff;
        padding: 10px;
        border-radius: 5px;
    }
    .project_title{
        font-weight: bold;
        color: rgba(125,86,255)!important;
    }

</style>