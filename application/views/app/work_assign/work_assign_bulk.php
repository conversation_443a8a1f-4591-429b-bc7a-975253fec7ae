<?php 
    $job_date = $param1 ?? date('Y-m-d');
    $date = new DateTime($job_date);
    $date_array[] = $job_date;
    $date_array[] = $date->modify('-1 day')->format('Y-m-d');
    $date_array[] = $date->modify('-1 day')->format('Y-m-d');
    $date_array[] = $date->modify('-1 day')->format('Y-m-d');
    
    $users = $this->work_assign_m->get_jobs_by_date($job_date, $date_array);
    $projects = $this->db->select('id, title')->get('projects')->result_array();
    $projects = array_column($projects, 'title', 'id');
//    echo json_encode($users, JSON_PRETTY_PRINT);
?>

<div>
    <form action="<?=base_url('app/work_assign/work_assign_bulk/')?>" method="post">
        <input type="hidden" name="job_date" value="<?=$job_date?>">
        <?php
            foreach($users as $user){
                ?>
                <div class="p-2">
                        <div class="employee_name"><?=$user['name']?></div>
                        <?php
                        if(!empty($user['tasks'])){
                            foreach($user['tasks'] as $project_id => $tasks){
                                ?>
                                <div style="background-color: rgba(215,213,243,0.09)" class="p-1">
                                    <?php
                                    $project_title = strtoupper($projects[$project_id]);
                                    echo "<div class=\"p-1\" style='font-weight: bold'>{$project_title}</div>";
                                    foreach ($tasks as $task){
                                        ?>
                                        <div class="">
                                            <div class="p-1">
                                                <input type="checkbox" name="task[<?=$task['id']?>]" id="task_<?=$task['id']?>" value="1" class="task_checkbox">
                                                <label for="task_<?=$task['id']?>" class="task_title">
                                                    [TRG-<?=$task['id']?>]
                                                    <span style="color: #24073e">
                                                        <?=$task['title']?>
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                    ?>
                                </div>
                                <?php
                            }
                        }
                        ?>
                    </div>
                <?php
            }
        ?>
        <div class="p-2">
            <button type="submit" class="btn btn-success float-right">Save Changes</button>
        </div>
    </form>
</div>

<style>
    .employee_name{
        font-size:18px!important;
        background-color: #ebe0f1;
        padding:5px;
        color: #7d0aba;
        font-weight: bold;
    }
    .task_title{
        cursor: pointer;
        font-weight: normal!important;
        font-size: 17px;
    }
    .task_checkbox{
        width: 20px;
        height: 20px;
        border-radius: 10px;
    }
</style>