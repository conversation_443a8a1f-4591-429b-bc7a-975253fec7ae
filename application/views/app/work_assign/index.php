<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.3.3/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
<div class="container-fluid pt-0 mt-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2 bg-primary">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <div class="float-right">
            <input type="date" class="form-control" style="width: 150px;" onchange="get_value(this.value)" value="<?=$_GET['job_date']?>">
        </div>
    </div>
    <form action="" method="post">
        <div class="mt-3" style="margin:14px;max-width: 900px" id="download_area">
            <div class="shadow-pro pt-2 pb-2 bg-white p-1" >
                <div class="row p-2 m-0 mb-2" style="background-image: linear-gradient(90deg, #23384E, #166686) !important;border-radius: 5px">
                    <div class="col-6 pt-1">
                        <img src="<?=base_url('assets/logo/logo.png')?>" style="width: 130px;height: auto">
                    </div>
                    <div class="col-6 text-right">
                        <div style="font-size: 18px; color: #a0ccf5" class="pb-1">pms.trogon.info</div>
                        <div style="font-size: 18px; color: #ffffff">
                            <?= DateTime::createFromFormat('Y-m-d', $_GET['job_date'])->format('d-m-Y (l)')?>
                        </div>
                    </div>
                </div>
                <div class="alert bg-danger-lighten text-danger p-3 text-center" role="alert" style="font-weight:500">
                    <b>Note:</b> All should update their daily work status before 8PM to ensure smooth work assignment and project completion.
                </div>
                <div class="p-1 pb-2" style="text-align:right;margin-top:-10px">
                    <div class="btn btn-primary" onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$_GET['job_date'].'/?page_name=work_assign/work_assign_bulk'); ?>', 'Bulk Work Assign')">
                        Bulk Assign
                    </div>
                </div>
                <table class="table table-bordered">
                    <tr style="background-color: #f1f2f8; font-size: 13px">
                        <th style="width: 50px;">#</th>
                        <th style="width: 220px">Name</th>
                        <th>Projects</th>
                    </tr>
                    <?php
                    if (isset($users) && isset($projects)){
                        $key = 0;
                        foreach ($users as $user){
                            if($user['work_status'] == 'working' && empty($user['tasks'])){
                                $bg_color = '#FCE9ED';
                                $text_color = 'text-danger';
                                $item_border = "border-bottom:4px solid #DC3444";
                            }elseif($user['work_status'] == 'absent'){
                                $bg_color = '#f5f0e4';
                                $text_color = 'text-warning';
                                $item_border = "border-bottom:2px solid #F0AD4E";
                            }else{
                                $bg_color = '';
                                $text_color = 'text-primary';
                                $item_border = "";
                            }
                            ?>
                            <tr style="background-color:<?=$bg_color?>!important;<?=$item_border?>">
                                <td style="background-color: <?=$bg_color?>;font-size:18px;font-weight:bold;"><?=++$key?></td>
                                <td style="background-color: <?=$bg_color?>;font-size:18px;font-weight:bold;" id="user_<?=$user['id']?>">
                                    <?php
                                        if (is_mobile()){
                                            ?>
                                            <a href="javascript:void(0)" class="<?=$text_color?>" onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$user['id'].'/?page_name=tasks_report/task_list_employee'); ?>', '<?= "[{$user['name']}] - Assigned Tasks" ?>')">
                                                <?=strtoupper($user['name'])?>
                                            </a>
                                            <?php
                                        }else{
                                            ?>
                                            <a href="javascript:void(0)" class="<?=$text_color?>" onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$user['id'].'/?page_name=tasks_report/task_list_employee'); ?>', '<?= "[{$user['name']}] - Assigned Tasks" ?>')">
                                                <?=strtoupper($user['name'])?>
                                            </a>
                                            <?php
                                        }
                                    ?>

                                </td>
                                <td style="background-color: rgba(246,243,243,0.41);">

                                    <?php
                                    if(empty($user['tasks'])){
                                        ?>
                                        <select name="work_status[<?=$user['id']?>]" class="form-control" style="font-size: 16px;color: rgba(232,5,38,0.99);background-color: rgba(254,136,145,0.07)">
                                            <option value="working" <?=$user['work_status'] == 'working' ? 'selected' : ''?>>No Jobs Assigned</option>
                                            <option value="absent" <?=$user['work_status'] == 'absent' ? 'selected' : ''?>>Absent</option>
                                            <option value="off" <?=$user['work_status'] == 'off' ? 'selected' : ''?>>Off</option>
                                        </select>
                                        <?php
                                    }else{
                                        foreach ($user['tasks'] as $project_id => $tasks){
                                            ?>
                                                <input type="hidden" name="work_status[<?=$user['id']?>]" value="working">
                                            <div>
                                                <?=get_project_title_secondary($projects[$project_id])?>
                                            </div>
                                            <div class="">
                                                <?php
                                                foreach ($tasks as $task){
                                                    ?>
                                                    <div class="p-1">
                                                        <div style="padding: 5px; background-color: rgba(125,230,234,0.07); border-radius: 5px;font-size: 18px;">
                                                            <div style="color: rgba(9,95,185,0.97);float: right">
                                                                [TRG-<b><?=$task['id']?></b>]
                                                            </div>
                                                            <span style="color: rgba(42,78,91,0.98); font-weight: 500">
                                                                <?=$task['title']?>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <?php
                                                }
                                                ?>
                                            </div>
                                            <hr>

                                            <?php
                                        }
                                    }

                                    ?>
                                    <div class="pt-2">
                                        <input class="form-control" type="text" placeholder="Remarks" name="remarks[<?=$user['id']?>]" value="<?=$user['remarks'] ?? ''?>">
                                    </div>
                                    <div class="p-1 pt-3 d-flex">
                                        <input class="form-control mr-2" style="width: 22px;margin-top: -7px;" type="checkbox" id="is_support_<?=$user['id']?>" name="is_support[<?=$user['id']?>]" value="1" <?=$user['is_support']==1 ? 'checked' : ''?>>
                                        <label for="is_support_<?=$user['id']?>" class="text-primary">Is Support?</label>
                                    </div>
                                </td>
                            </tr>
                            <?php
                        }
                    }
                    ?>
                </table>
                <div class="p-2 d-none">
                    <?php
                    $message = "";
                    echo "<textarea class=\"form-control\" style='height: 400px'>";
                    if (isset($users) && isset($projects)){
                        $key = 0;
                        foreach ($users as $user){
                            $name = trim(strtoupper($user['name']));
                            if ($user['work_status']!='working'){
                                $working_status = "- [{$user['work_status']}]";
                            }else{
                                $working_status = '';
                            }
                            $message .= "\n";
                            $message .= "🔸 *{$name}* {$working_status}\n\n";

                            // print projects
                            if ($user['is_support']==1){
                                $message .= "🔺 *SUPPORT WORKS* \n";
                            }
                            if (empty($user['tasks'])){
                                $message .= "> No Jobs Assigned! \n\n";
                            }else{
                                foreach ($user['tasks'] as $project_id => $tasks){
                                    $message .= "* *" . strtoupper($projects[$project_id])."* \n";
                                    $key = 0;
                                    foreach ($tasks as $task){
                                        $key++;
                                        $message .= " {$key}. [TRG-{$task['id']}] {$task['title']} \n";
                                    }
                                }
                            }

                        }
                    }
                    echo $message;
                    echo "</textarea>";
                    ?>
                </div>
            </div>
        </div>

        <div class="text-center p-2" style="max-width: 900px;">
            <button type="submit" name="submit" value="save" class="btn btn-primary">Submit & Save</button>
            <button type="submit" name="submit" value="download" class="btn btn-primary">Download PDF</button>
        </div>
    </form>
</div>

<script type="text/javascript">
    <?php
        if (is_mobile()){
            $modal_type = 'show_ajax_modal';
        }else{
            $modal_type = 'show_large_modal';
        }
        if ($_GET['user_id'] > 0){
            $user = $this->db->get_where('users', ['id' => $_GET['user_id']])->row();
            ?>
            $( document ).ready(function() {
                <?=$modal_type?>('<?= site_url('app/modal/popup/get/'.$user->id.'/?page_name=tasks_report/task_list_employee'); ?>', '<?= "[{$user->name}] - Assigned Tasks" ?>')
            });
            <?php
        }
    ?>
    function get_value(date){
        window.location.href='<?=base_url('app/work_assign/index/?job_date=')?>' + date;
    }
</script>


