<?php
if (isset($job_date) && isset($projects) && isset($users)){

    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Trogon Projects : <?= DateTime::createFromFormat('Y-m-d', $job_date)->format('d-m-Y')?></title>
    </head>
    <body style="background-color: #f5faf8">
    <div class="header">
        <div class="col-6">
            <div id="logo_title" style="padding: 10px;padding-left:13px;padding-bottom:5px;font-size: 14px">
                <a href="https://pms.trogon.info" target="_blank" style="text-decoration: none; color: azure">pms.trogon.info</a>
            </div>
            <div id="logo" style="padding: 10px;padding-top: 2px;">
                <a href="https://pms.trogon.info" target="_blank">
                    <img src="<?=base_url('assets/logo/logo.png')?>" alt="Trogon Logo" style="width: 115px;height: auto">
                </a>
            </div>

        </div>
        <div class="col-6" style="text-align: right">
            <div class="header_title">Trogon Projects</div>
            <div class="header_date"><?= DateTime::createFromFormat('Y-m-d', $job_date)->format('d-m-Y, (l)')?></div>
            <div style="font-size: 10px; padding-right: 10px; color: #bdecec;">
                Generated on: <?= DateTime::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->format('d-m-Y g:i A')?>
            </div>
        </div>
    </div>
    <div class="footer"
         style="margin-top:10px;margin-bottom:10px;text-align: center;background-color: #faeeef; color: #8c0d26;border-radius: 7px;font-size: 16px;padding: 50px 30px;">
        <b>Note:</b> All should update their daily work status before 8PM to ensure smooth work assignment and project completion.
    </div>
    <div class="main">
        <?php
        foreach ($users as $user){
            ?>
            <div style="padding:7px 2px;">
                <div style="background-color: #ffffff;border-radius: 5px;padding: 10px">
                    <div id="employee_name" style="padding:5px;padding-left:10px;margin-bottom:10px;border-left:5px solid #96bfea;border-radius:4px;font-size: 18px;font-weight: bold;color: #23384E; background-color: #d9eff6!important;text-align: left">
                        <?=strtoupper($user['name'])?>
                    </div>
                    <div class="">
                        <?php
                        if ($user['work_status'] == 'working'){
                            if ($user['is_support']==1){
                                ?>
                                <div style='margin-top:10px;font-size: 15px;font-weight:bold;color: rgb(13,119,225);padding: 8px;background-color: rgba(239,246,250,0.73);'>
                                    SUPPORT WORKS
                                </div>
                                <?php
                            }
                            $user_tasks = $user['tasks'] ?? [];
                            foreach ($user_tasks as $project_id => $tasks){
                                ?>
                                <div style="padding: 5px;">
                                    <div style="font-size: 16px; padding:5px; font-weight: bold;color: #356772; background-color: rgba(38,241,97,0.07)">
                                        <?=strtoupper($projects[$project_id])?>
                                    </div>
                                </div>
                                <?php
                                foreach ($tasks as $task){
                                    ?>
                                    <div style="padding: 5px;">

                                        <div style="font-size: 14px;color:#113665;background-color: rgba(239,246,250,0.73);padding: 7px;font-weight: bold">
                                            <?=$task['title']?>

                                            <div style="font-size: 12px!important;float: right!important;color: rgb(31,84,225);text-align: right;font-weight: normal!important;">
                                                [TRG-<b><?=$task['id']?></b>]
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                }
                                echo "<div style='border-bottom: 1px solid #eee;margin: 5px;'></div>";
                            }
                        }else{
                            ?>
                            <div style='margin-top:10px;font-size: 15px;color: #e50719;text-align: center;padding: 8px;background-color: rgba(254,136,145,0.13)'>
                                <?=strtoupper($user['work_status'])?>
                            </div>
                            <?php
                        }



                        ?>
                    </div>
                </div>
            </div>
            <?php
        }
        ?>
    </div>

    <style>
        .col-6{
            width: 50%!important;
            float: left!important;
        }
        body{
            border:1px solid #454545;
            font-family: Arial, sans-serif;
        }
        .header{
            background-image: linear-gradient(90deg, #23384E, #166686)!important;
            color:#efefef;
            padding:5px;
            border-radius: 7px;
        }
        .header_title{
            font-size: 21px;
            font-weight: bold;
            padding: 5px 10px;
        }
        .header_date{
            padding: 2px 10px 10px;
            font-weight: bold;
            font-size: 13px;
            color: azure;
        }
    </style>
    </body>
    </html>
    <?php
}
?>