<div class="padding-20"></div>
</div>
<!-- /.container-fluid -->
</div>
<!-- /.content -->
</div>
<!-- /.content-wrapper -->

<!-- Control Sidebar -->
<aside class="control-sidebar control-sidebar-dark">
	<!-- Control sidebar content goes here -->
	<div class="p-3">
		<h5>Title</h5>
		<p>Sidebar content</p>
	</div>
</aside>
<!-- /.control-sidebar -->

</div>
<!-- ./wrapper -->

<div class="loading">
	<div class="loading_data">
		<div class="d-block">
		</div>
	</div>
</div>
<style>
	.loading{
		display: none;
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 1001;
		background: rgba(255, 255, 255, 1.0) url("<?= base_url('assets/loader.gif') ?>") center no-repeat;
	}
	.loading_data{
		margin-top: 35vh;
		text-align: center;
	}
	.loading img{
		position: relative;
		animation: flip 2s infinite linear;
		transform-style: preserve-3d;
	}

	@keyframes flip {
		0% {
			transform: perspective(800px) rotateY(0deg);
		}
		50% {
			transform: perspective(800px) rotateY(180deg);
		}
		100% {
			transform: perspective(800px) rotateY(360deg);
		}
	}
	/**
	   Toast Color
	*/
	.toast-top-full-width{
		margin-top: 75px;
	}
	.toast{
		padding-top: 13px!important;
		padding-bottom: 13px!important;
		border: 0px !important;
		border-radius: 100px!important;
	}
	.toast-info{
		background-color: #0d6efd!important;
	}
    #table_no_btn_filter input {
        width: 550px;
        padding: 20px 10px!important;
        font-size: 17px!important;
    }
    input[type="date"] {
        font-size: 17px;
    }
    .cke_editable{
        font-size: 15px!important;
    }
</style>
<!-- REQUIRED SCRIPTS -->

<!-- jQuery -->
<script src="<?= base_url('assets/'); ?>plugins/jquery/jquery.min.js"></script>
<!--<script src="--><?php //= base_url('assets/'); ?><!--browser.js?v=--><?php //=rand()?><!--"></script>-->

<script src="<?= base_url('assets/'); ?>plugins/toastr/toastr.min.js"></script>
<!-- Bootstrap 4 -->
<script src="<?= base_url('assets/'); ?>plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- Select2 -->
<script src="<?= base_url('assets/'); ?>plugins/select2/js/select2.full.min.js"></script>
<!-- DataTables -->
<script src="<?= base_url('assets/'); ?>plugins/datatables/jquery.dataTables.min.js"></script>
<script src="<?= base_url('assets/'); ?>plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="<?= base_url('assets/'); ?>plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="<?= base_url('assets/'); ?>plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="<?= base_url('assets/'); ?>plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="<?= base_url('assets/'); ?>plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="<?= base_url('assets/'); ?>plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
<script src="<?= base_url('assets/'); ?>plugins/pdfmake/pdfmake.min.js"></script>
<script src="<?= base_url('assets/'); ?>plugins/jszip/jszip.min.js"></script>
<script src="<?= base_url('assets/'); ?>plugins/chart.js/Chart.min.js"></script>

<!-- AdminLTE App -->
<script src="<?= base_url('assets/'); ?>dist/js/adminlte.min.js"></script>
<!--<script src="--><?php //= base_url('assets/'); ?><!--calendar_tracking.js"></script>-->
<!--<script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/js/adminlte.min.js"></script>-->


<script>

    document.addEventListener('DOMContentLoaded', function () {
        var ta_search_list = document.getElementById("ta_search_list");
        var ta_search_items = document.querySelectorAll(".ta_search_item");
        var noResultElement = document.getElementById('ta_no_result');

        ta_search_list.addEventListener("input", function () {
            var inputVal = ta_search_list.value.trim().toLowerCase();
            var anyVisible = false;

            ta_search_items.forEach(function (item) {
                var textContent = item.textContent.toLowerCase();

                // Check if the item's text includes the search input
                if (textContent.includes(inputVal)) {
                    item.style.display = ''; // Show the item
                    anyVisible = true;
                } else {
                    item.style.display = 'none'; // Hide the item
                }
            });

            // Display or hide the "No Result Found" message based on search results
            noResultElement.style.display = anyVisible ? "none" : "block";
        });
    });

    document.addEventListener('DOMContentLoaded', function () {
        var ta_search_list = document.getElementById("search_by_job_id");
        var ta_search_items = document.querySelectorAll(".ta_search_item");
        var noResultElement = document.getElementById('ta_no_result');

        ta_search_list.addEventListener("input", function () {
            var inputVal = 'trg-' + ta_search_list.value.trim().toLowerCase();
            var anyVisible = false;

            ta_search_items.forEach(function (item) {
                var textContent = item.textContent.toLowerCase();

                // Check if the item's text includes the search input
                if (textContent.includes(inputVal)) {
                    item.style.display = ''; // Show the item
                    anyVisible = true;
                } else {
                    item.style.display = 'none'; // Hide the item
                }
            });

            // Display or hide the "No Result Found" message based on search results
            noResultElement.style.display = anyVisible ? "none" : "block";
        });
    });


    // duration input
    function duration_input(item){
        var item_id = item.id;
        var value = $('#' + item_id).val().replace(/[^0-9]/g, "");
        if (value.length > 2) {
            var hours = value.slice(0, 2);
            var minutes = value.slice(2);
            if (parseInt(minutes, 10) > 59) {
                minutes = "59";
            }
            $('#' + item_id).val(hours + ":" + minutes);
        }
    }
    
    




    <?php
        $pending_tasks_count = $this->tasks_m->get_user_pending_count();
        $pending_todo_count = $this->todo_m->get_user_pending_count();
        $item_count = $pending_tasks_count + $pending_todo_count;
        if ($item_count > 0) {
            ?>
            var notification = "(<?=$item_count?>) Pending Tasks/ Todo";
            var originalTitle = document.title;
            var blink = false;

            setInterval(function() {
                if(blink) {
                    $(document).prop('title', originalTitle);
                    blink = false;
                } else {
                    $(document).prop('title', notification);
                    blink = true;
                }
            }, 1500); // blink interval in milliseconds
    <?php
        }
    ?>


	$('#example1').dataTable( {
		"pageLength": 20,
		dom: 'Bfrtip',
		buttons: [
			'csv', 'excel', 'print'
		]
	} );
    $('#example-basic').dataTable( {
		"pageLength": 50,
		dom: 'Bfrtip',
        buttons: [
        ]
	} );
	$('#example2').dataTable( {
		"pageLength": 500,
		dom: 'Bfrtip',
		buttons: [
			'csv', 'excel', 'print'
		]
	} );
    $('#example3').dataTable( {
		"pageLength": 500,
		dom: 'Bfrtip',
		buttons: [
			'csv', 'excel', 'print'
		]
	} );
    $('#table_no_btn').dataTable( {
        "pageLength": 500,
        dom: 'Bfrtip',
        buttons: [
        ],
        "initComplete": function(settings, json) {
            $('.dataTables_filter input[type="search"]').attr('placeholder','Enter search term here');
        }
    } );
</script>
<script>
    <?php
//        if (is_mobile()){
//            ?>
//            window.onload = function() {
//                // Hide the loading message
//                hide_loading();
//            };
//            <?php
//        }
        if (isset($page_name) && $page_name == 'dashboard/index'){
            ?>
            setInterval(function() {
                if(!($('.modal').hasClass('show'))) {
                    location.reload();
                }
            }, 60000);
            <?php
        }
    ?>

	$(document).ready(function() {
		// setTimeout( function(){
		// 	toastr.success('Lorem ipsum dolor sit amet, consetetur sadipscing elitr.')
		// }  , 500 );
		<?php show_alert(); ?>
	});
	$(function () {

		//Initialize Select2 Elements
		$('.select2').select2();
		//Datatables
		$("#example2").DataTable({
			"responsive": true, "lengthChange": false, "autoWidth": false,
			"buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
		}).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');
		// $('#example2').DataTable({
		// 	"paging": true,
		// 	"lengthChange": false,
		// 	"searching": false,
		// 	"ordering": true,
		// 	"info": true,
		// 	"autoWidth": false,
		// 	"responsive": true,
		// });
	});


	function toggle_modal(modal_id) {
		$(modal_id).toggle();
	}
	function hide_loading() {
		$('.loading').hide();
	}
	function show_loading() {
		$('.loading').show();
	}
	function message_success(message) {
		toastr.success(message);
	}
	function message_error(message) {
		toastr.error(message);
	}
	function message_info(message) {
		toastr.info(message);
	}

	/**
	 * GENERATE URL SLUG
	 */
	function generate_url_slug(from_id, to_id) {
		var text = $("#"+from_id).val();
		text = text.toLowerCase()
			.replace(/ /g, '-')
			.replace(/[^\w-]+/g, '');
		$("#"+to_id).val(text);
	}

	/*
		Switch Page
	 */
	function switch_page(page_url, keep_history = false ,timeOut = 400) {
		show_loading();
		window.setTimeout(function(){
			hide_loading();
			if(keep_history===false){
				window.location.replace(page_url);
			}else{
				window.location.href = page_url;
			}
		}, timeOut);
	}

    // Form submit action
	$("#ta_form").submit(function() {
		$('#submit_button').hide();
		$('#submit_button_loading').show();
	});

    // Check email duplication
    function check_email_duplication(email, user_id, email_field = '#email') {
        $.ajax({
            url: '<?=base_url('app/users/check_email_duplication/')?>',
            type: 'POST',
            data: {email: email, user_id: user_id},
            dataType: 'JSON',
            success: function(response) {
                if(response.status == 0) {
                   message_error(response.message);
                   $(email_field).val('');
                }
            },
            error: function() {
                alert("An error occurred while checking the email.");
            }
        });
    }

    // Check phone duplication
    function check_phone_duplication(phone, user_id, phone_field = '#phone') {
        $.ajax({
            url: '<?=base_url('app/users/check_phone_duplication/')?>',
            type: 'POST',
            data: {phone: phone, user_id: user_id},
            dataType: 'JSON',
            success: function(response) {
                if(response.status === 0) {
                   message_error(response.message);
                   $(phone_field).val('');
                }
            },
            error: function() {
                alert("An error occurred while checking the phone.");
            }
        });
    }

    // get sections by classes id
    function get_sections(classes_id, section_div = '#section_id'){
        $.ajax({
            type: 'GET',
            url: "<?=base_url('app/section/get_section_by_classes/?classes_id=')?>" + classes_id,
            dataType: "html",
            success: function (data) {
                $(section_div).html(data);
            }
        });
    }
    
    
    // get class section wise students list
    function get_student_by_section(section_id, students_div = '#students'){
        $.ajax({
            url: '<?=base_url('app/student/get_student_by_section/')?>',
            type: 'POST',
            data: {section_id: section_id},
            dataType: "html",
            success: function (data) {
                $(students_div).html(data);
            }
        });
    }

</script>

</body>
</html>
