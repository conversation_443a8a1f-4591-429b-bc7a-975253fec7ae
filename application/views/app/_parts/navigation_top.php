<nav class="main-header navbar navbar-expand navbar-dark" style="background-image: linear-gradient(135deg, #12623e 0%, #24ae61 100%)">
<!--    linear-gradient(90deg, #166686, #14A885)!important;-->
	<!-- Left navbar links -->
	<ul class="navbar-nav">
		<li class="nav-item">
			<a class="nav-link" data-widget="pushmenu" href="#"><i class="fas fa-bars"></i></a>
		</li>

		<li class="nav-item d-none d-sm-inline-block mobile_hide">
            <div class="text-white p-1 rounded text-center" style="background-color:rgb(13, 78, 49); width: 160px;font-size: 18px;">
                <?=date('M-d, <b>g:i A</b>')?>
            </div>
		</li>
        <?php
            if (is_mobile()){
                ?>
                <li class="nav-item d-sm-inline-block">
                    <a href="<?=base_url('app/dashboard/index')?>" style="padding-top:30px;">
                        <img src="<?=base_url('assets/logo/logo.png')?>" style="margin-left:15px;max-height: 35px;width: auto;max-width: 100%" alt="">
                    </a>
                </li>
                <?php
            }
        ?>
        <?php
            if (has_permission('dashboard/index')){
                ?>
                <li class="nav-item d-sm-inline-block mobile_hide">
                    <a href="<?=base_url('app/dashboard/index')?>" class="nav-link">
                        <i class="bi bi-speedometer2"></i> DASHBOARD
                    </a>
                </li>
                <?php
            }

            ?>
            
            <?php
            if (has_permission('tasks/add')){
                ?>
                <li class="nav-item d-sm-inline-block mobile_hide">
                    <a href="javascript::void(0)" onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add', '<?= get_phrase('add_tasks'); ?>')"
                       class="nav-link">
                        <i class="bi bi-view-list"></i>
                        ADD
                        <span style="font-weight: bold">
                             TASKS
                        </span>
                    </a>
                </li>
                <?php
            }
            

            if (has_permission('tasks/index')){
                ?>
                <li class="nav-item d-sm-inline-block mobile_hide">
                    <a href="<?=base_url('app/tasks/index/?task_status=pending')?>" class="nav-link">
                        <i class="bi bi-view-list"></i> TASKS
                    </a>
                </li>
                <?php
            }
            
            if (has_permission('time_log/daily_log') || get_user_id() == 45){
                ?>
                <li class="nav-item d-sm-inline-block">
                    <a href="<?=base_url('app/time_log/daily_log')?>" class="nav-link">
                        <i class="bi bi-calendar-check"></i> ATTENDANCE
                    </a>
                </li>
                <?php
            }


        ?>
	</ul>


	<ul class="navbar-nav ml-auto">
		<li class="nav-item d-sm-inline-block mobile_hide">
			<a href="<?= base_url('app/notification/index/'); ?>" class="nav-link">
				<i class="bi bi-bell-fill"></i>
			</a>
		</li>
		<li class="nav-item d-sm-inline-block mobile_hide">
			<a href="<?= base_url('app/profile/index/'); ?>" class="nav-link">
				<i class="bi bi-person-fill"></i>
			</a>
		</li>
		<li class="nav-item d-sm-inline-block">
			<div onclick="switch_page('<?= base_url('login/logout/'); ?>')" class="nav-link">
				<i class="bi bi-box-arrow-right"></i> </div>
		</li>
	</ul>


</nav>
