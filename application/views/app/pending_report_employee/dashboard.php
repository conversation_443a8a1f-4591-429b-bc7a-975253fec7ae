<div class="container-fluid p-2">
    <div class="card card-primary row shadow-none p-0" style="margin:3px;margin-top: -15px!important;">
        <div class="card-header">
            <h3 class="card-title">
                <a href="<?= $_SERVER['HTTP_REFERER']?>" class="btn btn-outline-success btn-sm pull-left">
                    <i class="fas fa-arrow-circle-left"></i> Go Back
                </a>
                <span class="pl-2">
                    <?php
                    if (isset($user_details)){
                        ?>
                        Pending Tasks - <?=$user_details['name']?>
                        <?php
                    }
                    ?>
                </span>

            </h3>
        </div>
        <!-- /.card-header -->
        <div class="card-body p-0 m-0">
            <div class="row p-1 pt-0 mobile_hide" style="margin-top: -2px">
                <div class="col-7 p-1">
                    <ul class="nav nav-tabs" id="custom-tabs-three-tab" role="tablist" style="width: 100%">
                        <li class="nav-item" style="width: 50%!important;">
                            <a class="p-0 nav_switch_tasks nav-link active" id="tabs_nav_tasks" data-toggle="pill" href="#tabs_content_tasks" role="tab" aria-controls="custom-tabs-three-home" aria-selected="true">
                                <div class="p-1" style="width:100%;background-color: #e1e7f5; border:0!important;color: #2a5bb6;">
        
                                    <div class="row">
                                        <div class="col-5">
                                            <b>
                                                <i class="bi bi-view-list"></i>
                                                TASKS
                                            </b>
                                        </div>
                                        <div class="col-7 text-danger mb-0 text-right">
                                            <span style="font-weight: 900!important;font-size: 18px;"><?=$tasks_pending_count ?? 0?></span>
                                            <small>PENDING</small>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li class="nav-item" style="width: 50%!important;">
                            <a class="p-0 nav_switch_todo nav-link" id="tabs_nav_todo" data-toggle="pill" href="#tabs_content_todo" role="tab" aria-controls="custom-tabs-three-profile" aria-selected="false">
                                <div class="p-1" style="width:100%;background-color: #d2f6ec; border:0!important;color: #2ab685; ">
                                    <div class="row">
                                        <div class="col-5">
                                            <b>
                                                <i class="bi bi-check2-square"></i>
                                                TODO
                                            </b>
                                        </div>
                                        <div class="col-7 text-danger mb-0 text-right">
                                            <span style="font-weight: 900!important;font-size: 18px;"><?=$todo_pending_count ?? 0?></span>
                                            <small>PENDING</small>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content" id="custom-tabs-three-tabContent">
                        <div class="tab-pane fade active show" id="tabs_content_tasks" role="tabpanel" aria-labelledby="tabs_nav_tasks">
                            <div class="card card-primary shadow-pro ">
                                <div class="card-header p-2" style="background-color: #e1e7f5; border:0!important;color: #2a5bb6; ">
                                    <div class="text-center" >
                                        <div class="d-inline-block text-primary float-right mr-1">
                                            <span style="font-weight: 900!important;font-size: 18px;"><?=$tasks_pending_count ?? 0?></span>
                                            <small>TOTAL PENDING</small>
                                        </div>
                                        <div class="d-inline-block text-danger float-right mr-3">
                                            <span style="font-weight: 900!important;font-size: 18px;"><?=$tasks_pending_due_count ?? 0?></span>
                                            <small>DUE DATE EXPIRED</small>
                                        </div>
                                    </div>
                                </div>
        
                                <div class="card-body p-2 task_container" style="overflow-y: scroll!important;height: 80vh">
        
                                    <?php
                                    if (isset($tasks) && isset($projects) && isset($users)){
                                        foreach ($tasks as $task){
                                            $bg_color = get_task_bg_employee($task['task_status'], $task['due_date']);
                                            if ($bg_color == 'bg-success-lighten' || $bg_color == 'bg-dark-lighten'){
                                                $style = 'style="opacity: 0.7"';
                                            }else{
                                                $style = '';
                                            }
                                            ?>
                                            <div class="p-1">
                                                <div class="single_task_item shadow-sm pl-2 <?=$bg_color?>"  <?=$style?> onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/task_details_employee'); ?>', '<?php echo get_phrase('update_task_status'); ?>')">
                                                    <div class="bg-white p-2 pb-0">
                                                        <?php
                                                        if ($task['task_status'] == 'assigned'){
                                                            ?>
                                                            <div class="row pb-2">
                                                                <div class="col-6 text-left">
                                                                    <?= get_task_priority($task['task_priority'])?>
                                                                </div>
                                                                <div class="col-6 text-right">
                                                                    <?= get_due_date($task['due_date'])?>
                                                                </div>
                                                            </div>
                                                            <?php
                                                        }
                                                        ?>
        
                                                        <div class="pb-1 float-right">
                                                            <div class="pb-1 pr-1 pt-1">
                                                                <?=get_project_title($projects[$task['project_id']])?>
                                                            </div>
                                                        </div>
        
                                                        <h5 style="background-color: #f4f4fa" class="text-primary p-2 m-0"><?=$task['title']?></h5>
                                                        <?php
                                                        if ($task['task_status'] == 'assigned'){
                                                            ?>
                                                            <div class="text-muted p-2 pb-3">
                                                                <div class="task_description">
                                                                    <?=$task['description']?>
                                                                </div>
                                                            </div>
                                                            <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/task_details_employee'); ?>', '<?php echo get_phrase('update_task_status'); ?>')"
                                                                    class="btn btn-outline-info btn-sm float-right" style="width: 100px;margin-top: -20px;">
                                                                VIEW TASK <small><i class="bi bi-arrow-right-circle"></i></small>
                                                            </button>
                                                            <?php
                                                        }
                                                        ?>
                                                        <div class="float-left text-muted mt-2" style="font-size: 12px;">
                                                            Added by: <?=strtoupper($users[$task['created_by']])?> | Updated on: <?= DateTime::createFromFormat('Y-m-d H:i:s', $task['updated_on'])->format('d-m-Y g:i A')?>
                                                        </div>
                                                        <div class="clearfix"></div>
        
                                                    </div>
                                                </div>
                                            </div>
                                            <?php
                                        }
                                    }
                                    ?>
        
                                </div>
        
                            </div>
                        </div>
                        <div class="tab-pane fade" id="tabs_content_todo" role="tabpanel" aria-labelledby="tabs_nav_todo">
                            <div class="card card-primary shadow-pro">
                                <div class="card-header p-2" style="background-color: #d2f6ec; border:0!important;color: #2ab685; ">
                                    <div class="text-center" >
                                        <div class="d-inline-block text-primary float-right mr-1">
                                            <span style="font-weight: 900!important;font-size: 18px;"><?=$todo_pending_count ?? 0?></span>
                                            <small>TOTAL PENDING</small>
                                        </div>
                                        <div class="d-inline-block text-danger float-right mr-3">
                                            <span style="font-weight: 900!important;font-size: 18px;"><?=$todo_pending_due_count ?? 0?></span>
                                            <small>DUE DATE EXPIRED</small>
                                        </div>
                                    </div>
                                </div>
        
                                <div class="card-body p-2 task_container" style="overflow-y: scroll!important;height: 80vh">
                                    <?php
                                    if (isset($todo) && isset($users)){
                                        foreach ($todo as $todo_item){
                                            $bg_color = get_todo_bg_employee($todo_item['todo_status'], $todo_item['due_date']);
                                            if ($bg_color == 'bg-success-lighten' || $bg_color == 'bg-dark-lighten'){
                                                $style = 'style="opacity: 0.7"';
                                            }else{
                                                $style = '';
                                            }
                                            ?>
                                            <div class="p-1">
                                                <div class="single_todo_item shadow-sm pl-2  <?=$bg_color?>"  <?=$style?> onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$todo_item['id'].'/?page_name=todo/todo_details_employee'); ?>', '<?php echo get_phrase('update_todo_status'); ?>')">
                                                    <div class="bg-white p-2 pb-0">
                                                        <?php
                                                        if ($todo_item['todo_status'] == 'assigned'){
                                                            ?>
                                                            <div class="row pb-2">
                                                                <div class="col-6 text-left">
                                                                    <?= get_todo_priority($todo_item['todo_priority'])?>
                                                                </div>
                                                                <div class="col-6 text-right">
                                                                    <?= get_due_date($todo_item['due_date'])?>
                                                                </div>
                                                            </div>
                                                            <?php
                                                        }
                                                        ?>
        
                                                        <h5 style="background-color: #f4f4fa; font-size: 16px;" class="text-primary p-1 m-0"><?=$todo_item['title']?></h5>
                                                        <?php
                                                        if ($todo_item['todo_status'] == 'assigned'){
                                                            ?>
                                                            <div class="text-muted p-2 pb-3">
                                                                <div class="task_description">
                                                                    <?=$todo_item['description']?>
                                                                </div>
                                                            </div>
                                                            <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$todo_item['id'].'/?page_name=tasks/todo_details_employee'); ?>', '<?php echo get_phrase('update_task_status'); ?>')"
                                                                    class="btn btn-outline-info btn-sm float-right" style="width: 100px;margin-top: -35px">
                                                                VIEW TODO <small><i class="bi bi-arrow-right-circle"></i></small>
                                                            </button>
                                                            <?php
                                                        }
                                                        ?>
                                                        <div class="float-right text-muted mt-2" style="font-size: 12px;">
                                                            Added by: <?=strtoupper($users[$todo_item['created_by']])?>
                                                        </div>
        
                                                        <div class="clearfix"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php
                                        }
                                    }
                                    ?>
                                </div>
        
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-5 p-1">
                    <div class="card card-primary shadow-pro ">
                        <div class="card-header p-2" style="background-color: #e1f5ec; border:0!important;color: #2ab685; ">
                            <h3 class="card-title" style="font-weight: 600!important;font-size: 15px;margin-top: 4px;">
                                <i class="bi bi-calendar-fill"></i>
                                ATTENDANCE - <?=strtoupper(date('M'))?>
                                <button class="btn btn-outline-success btn-sm pull-right"
                                        onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'.$user_details['id']); ?>/?page_name=dashboard/employee_time_log_report', '<?= get_phrase('time_log_report').' - '.$user_details['name']; ?>')">
                                    View Detailed Report
                                </button>
                            </h3>
                            <div class="text-center d-none" >
                                <div class="d-inline-block text-primary float-right mr-1">
                                    <span style="font-weight: 900!important;font-size: 18px;"><?=$tasks_pending_count ?? 0?></span>
                                    <small>TOTAL PENDING</small>
                                </div>
                                <div class="d-inline-block text-danger float-right mr-3">
                                    <span style="font-weight: 900!important;font-size: 18px;"><?=$tasks_pending_due_count ?? 0?></span>
                                    <small>DUE DATE EXPIRED</small>
                                </div>
                            </div>
                        </div>
        
                        <div class="card-body p-1 task_container" style="overflow-y: scroll!important;height: 80vh">
        
                            <?php
                            if (isset($dates_array) && isset($time_log) && isset($attendance_data) && isset($time_log_type)){
                                ?>
                                <table class="table table-bordered">
                                    <tr style="font-size: 12px!important;color: #777!important;text-align: center; background-color: #e7f6fb">
                                        <th style="font-size: 12px!important;color: #777!important;">DATE</th>
                                        <th style="font-size: 12px!important;color: #777!important;">ATT</th>
                                        <?php
                                        foreach ($time_log_type as $log_type){
                                            if ($log_type['id'] == 1 || $log_type['id'] == 8){
                                                echo "<th style=\"font-size: 12px!important;color: #777!important;\">".strtoupper(get_phrase($log_type['title']))."</th>";
                                            }
                                        }
                                        ?>
                                        <th style="font-size: 12px!important;color: #777!important;background-color: #b8f1ea">HOURS</th>
                                        <th style="font-size: 12px!important;color: #777!important;background-color: #fce7e7">DIFF</th>
                                        <th style="font-size: 12px!important;color: #777!important;background-color: #f1b8b8">BREAK</th>
                                    </tr>
                                    <?php
                                    foreach ($dates_array as $date){
                                        if (empty($attendance_data[$date]['attendance'])){
                                            continue;
                                        }
                                        $attendance = $attendance_data[$date]['attendance'] ?? '';
                                        $remarks = $attendance_data[$date]['remarks'] ?? '';
                                        if (!empty($attendance_data[$date]['off_date'])){
                                            $off_date = DateTime::createFromFormat('Y-m-d', $attendance_data[$date]['off_date'])->format('d-m-Y, l');
                                        }else{
                                            $off_date = '';
                                        }
                                        ?>
                                        <tr>
                                            <td style="width: 100px;text-align: center;font-size: 12px; color: #777; background-color: #fefefe">
                                                <b><?= DateTime::createFromFormat('Y-m-d', $date)->format('d-M-Y')?></b><br>
                                                <small><?= DateTime::createFromFormat('Y-m-d', $date)->format('(l)')?></small>
                                            </td>
                                            <th style="text-align: center; font-size: 15px!important;padding: 10px;color: #444;background-color: #f5fdfc">
                                                <?="<b>{$attendance}</b>"?>
                                            </th>
                                            <?php
                                            if ($attendance != 'P' && $attendance != 'HD'){
                                                echo '<td class="text-center" colspan="5" style="background-color: rgba(239,242,245,0.38)">';
                                                echo "<b>{$attendance}</b>";
                                                if (!empty($remarks)){
                                                    echo " (<small>{$remarks}</small>)";
                                                }
                                                if ($attendance =='OF'){
                                                    echo "<br><small>OFF FOR - {$off_date}</small>";
                                                }
                                                if (empty($remarks) && empty($off_date)){
                                                    echo "-";
                                                }
                                                echo '</td>';
                                            }else{
                                                foreach ($time_log_type as $log_type){
                                                    if ($log_type['id'] != 1 && $log_type['id'] != 8){
                                                        continue;
                                                    }
                                                    ?>
                                                    <td class="text-center"
                                                        style="font-size: 14px;line-height:1.0em!important;color:#666!important;background-color: rgba(243,245,246,0.15);padding: 5px 1px!important;">
                                                        <?php
                                                        if (!empty($time_log[$date][$log_type['id']])){
                                                            echo print_punch_time($time_log[$date][$log_type['id']], $log_type['id']);
                                                        }else{
                                                            echo '-';
                                                        }
                                                        ?>
                                                    </td>
                                                    <?php
                                                }
                                                ?>
                                                <td style="background-color: #e9faf8;font-size: 15px!important;padding: 1px!important;" class="text-center">
                                                    <?php
                                                    if (!empty($time_log[$date]['total_duration']) && duration_to_time($time_log[$date]['total_duration']) != '00:00'){
                                                        echo print_daily_time(duration_to_time($time_log[$date]['total_duration']));
                                                    }else{
                                                        echo '-';
                                                    }
                                                    ?>
                                                </td>
                                                <td class="text-center" style="font-size: 13px!important;background-color: #faf6f6">
                                                    <?php
                                                    if (!empty($time_log[$date]['total_duration']) && duration_to_time($time_log[$date]['total_duration']) != '00:00'){
                                                        echo print_daily_diff(duration_to_time($time_log[$date]['total_duration']));
                                                    }else{
                                                        echo '-';
                                                    }
                                                    ?>
                                                </td>
                                                <td class="text-center" style="font-size: 13px!important;background-color: #faefef">
                                                    <?php
                                                    if (!empty($time_log[$date]['total_break']) && duration_to_time($time_log[$date]['total_break']) != '00:00'){
                                                        echo print_break_time(duration_to_time($time_log[$date]['total_break']));
                                                    }else{
                                                        echo '-';
                                                    }
                                                    ?>
                                                </td>
                                            <?php
                                            }

                                            ?>

                                        </tr>
                                        <?php
                                    }
                                    ?>
                                    <tr style="background-color: #fcfeff; display: none">
                                        <th colspan="4" class="text-right">TOTAL</th>
                                        <th>-</th>
                                        <th>-</th>
                                    </tr>
                                </table>
                                <?php
                            }
                            ?>
        
                        </div>
        
                    </div>
                </div>
            </div>
        </div>
        <!-- /.card-body -->
    </div>
</div>


<style>
    .single_task_item:hover, .single_todo_item:hover{
        opacity: 0.6;
        cursor: pointer;
    }
    .single_task_item h5, .single_todo_item h5{
        font-size: 17px;
    }
    td, th{
        padding: 4px!important;
        font-size: 14px!important;
        border-color: rgba(245, 241, 244, 0.81) !important;
    }
    .task_description{
        font-size: 14px;
        overflow: hidden;
        max-width: 100%;
        text-overflow: ellipsis;
        /*width: fit-content;*/
        /*white-space: nowrap;*/
    }
</style>


