<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>

    </div>
    <div class="card card-primary row mt-3 shadow-pro" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title"><?= strtoupper($page_title ?? '')?></h3>
        </div>
        <!-- /.card-header -->
        <div class="card-body">
            <table id="example3" class="table table-bordered table-striped">
                <thead>
                <tr>
                    <th>Employee</th>
                    <th>Dashboard</th>
                    <th>Tasks Pending</th>
                    <th>Todo Pending</th>
                </tr>
                </thead>
                <tbody>
                <?php
                if (isset($list_items)) {
                    foreach ($list_items as $key => $item) {
                        ?>
                        <tr>
                            <td>
                                <div style="font-size: 17px;font-weight: bold">
                                    <?= $item['name']?>
                                </div>
                            </td>
                            <td>
                                <div class="pt-2">
                                    <a href="<?=base_url('app/dashboard/index/?user_id='.$item['user_id'])?>" class="btn btn-sm btn-outline-info" style="width: 140px">
                                        DASHBOARD
                                    </a>
                                </div>
                            </td>
                            <td style="font-size: 17px;">
                                <?php
                                    if ($item['task_count'] > 0){
                                        echo "<b>(<span style=\"font-size: 17px;\">{$item['task_count']}</span>)</b> <small>TASKS</small>";
                                    
                                    }else{
                                        echo '<span class="text-danger">No Tasks</span>';
                                    }
                                ?>

                            </td>
                            <td style="font-size: 17px;">
                                <?php
                                    if ($item['todo_count'] > 0){
                                        echo "<b>(<span style=\"font-size: 17px;\">{$item['todo_count']}</span>)</b> <small>TODO</small>";
                                    }else{
                                        echo '<span class="text-danger">No Todo</span>';
                                    }
                                ?>
                            </td>
                        </tr>
                        <?php
                    }
                }
                ?>

                </tbody>

            </table>
        </div>
        <!-- /.card-body -->
    </div>
</div>

