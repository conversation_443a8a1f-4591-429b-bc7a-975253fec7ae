<div class="container-fluid">
    <!-- Summary Cards Section -->
    <div class="row mt-3">
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>12</h3>
                    <p>Tasks Completed</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>5</h3>
                    <p>Bugs Reported</p>
                </div>
                <div class="icon">
                    <i class="fas fa-bug"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>2</h3>
                    <p>Bugs Reopened</p>
                </div>
                <div class="icon">
                    <i class="fas fa-redo"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="small-box bg-secondary">
                <div class="inner">
                    <h3>4.5%</h3>
                    <p>Bug Rate</p>
                </div>
                <div class="icon">
                    <i class="fas fa-chart-pie"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>1h 45m</h3>
                    <p>Avg Time/Task</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="small-box bg-primary">
                <div class="inner">
                    <h3>6h 30m</h3>
                    <p>Time Today</p>
                </div>
                <div class="icon">
                    <i class="fas fa-hourglass-half"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Toolbar -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card card-outline card-primary">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div class="card-body p-2">
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#logTimeModal">
                            <i class="fas fa-clock mr-1"></i> Log Manual Time
                        </button>
                        <button type="button" class="btn btn-sm btn-danger" data-toggle="modal" data-target="#reportBugModal">
                            <i class="fas fa-bug mr-1"></i> Report Bug
                        </button>
                        <a href="#" class="btn btn-sm btn-info">
                            <i class="fas fa-tasks mr-1"></i> View All Tasks
                        </a>
                        <button type="button" class="btn btn-sm btn-success" id="exportReport">
                            <i class="fas fa-file-export mr-1"></i> Export Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Start Tabs Section -->
    <div class="row">
        <div class="col-lg-8 col-md-12">
            <div class="card card-tabs">
                <div class="card-header p-0 pt-1 border-bottom-0">
                    <h3 class="card-title pl-3 pt-2">Today's Assigned Tasks</h3>
                    <ul class="nav nav-tabs" id="today-tasks-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="project-tasks-tab" data-toggle="pill" href="#project-tasks" role="tab">Project Tasks</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="support-tasks-tab" data-toggle="pill" href="#support-tasks" role="tab">Support Tasks</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="bugs-tab" data-toggle="pill" href="#bugs" role="tab">Bugs</a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="today-tasks-tabContent">
                        <!-- Project Tasks Tab -->
                        <div class="tab-pane fade show active" id="project-tasks" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Task</th>
                                            <th>Project</th>
                                            <th>Priority</th>
                                            <th>Est. Time</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" class="text-bold">Dashboard UI Cleanup</a></td>
                                            <td>Internal Tools</td>
                                            <td><span class="badge badge-danger">High</span></td>
                                            <td>2h</td>
                                            <td><span class="badge badge-primary">In Progress</span></td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-xs btn-warning"><i class="fas fa-pause"></i></button>
                                                    <button class="btn btn-xs btn-info"><i class="fas fa-check"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" class="text-bold">API Integration</a></td>
                                            <td>Client CRM</td>
                                            <td><span class="badge badge-warning">Medium</span></td>
                                            <td>3h</td>
                                            <td><span class="badge badge-secondary">Pending</span></td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-xs btn-success"><i class="fas fa-play"></i></button>
                                                    <button class="btn btn-xs btn-info"><i class="fas fa-check"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Support Tasks Tab -->
                        <div class="tab-pane fade" id="support-tasks" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Task</th>
                                            <th>Client</th>
                                            <th>Priority</th>
                                            <th>Est. Time</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" class="text-bold">Fix Login Issue</a></td>
                                            <td>Acme Inc</td>
                                            <td><span class="badge badge-danger">High</span></td>
                                            <td>1.5h</td>
                                            <td><span class="badge badge-warning">Paused</span></td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-xs btn-success"><i class="fas fa-play"></i></button>
                                                    <button class="btn btn-xs btn-info"><i class="fas fa-check"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" class="text-bold">Email Template Update</a></td>
                                            <td>BetaSoft</td>
                                            <td><span class="badge badge-info">Low</span></td>
                                            <td>30m</td>
                                            <td><span class="badge badge-secondary">Pending</span></td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-xs btn-success"><i class="fas fa-play"></i></button>
                                                    <button class="btn btn-xs btn-info"><i class="fas fa-check"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Bugs Tab -->
                        <div class="tab-pane fade" id="bugs" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Bug</th>
                                            <th>Project</th>
                                            <th>Severity</th>
                                            <th>Reported By</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" class="text-bold">Crash on Submit</a></td>
                                            <td>Internal Tools</td>
                                            <td><span class="badge badge-danger">Critical</span></td>
                                            <td>John D.</td>
                                            <td><span class="badge badge-primary">In Progress</span></td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-xs btn-warning"><i class="fas fa-pause"></i></button>
                                                    <button class="btn btn-xs btn-info"><i class="fas fa-wrench"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" class="text-bold">Alignment issue on mobile</a></td>
                                            <td>Client CRM</td>
                                            <td><span class="badge badge-info">Minor</span></td>
                                            <td>Jane S.</td>
                                            <td><span class="badge badge-secondary">Open</span></td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-xs btn-success"><i class="fas fa-play"></i></button>
                                                    <button class="btn btn-xs btn-info"><i class="fas fa-wrench"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        </div>
                </div>
            </div>

            <!-- Previous Pending Tasks -->
            <div class="card card-outline card-warning collapsed-card">
                <div class="card-header">
                    <h3 class="card-title">Previous Pending Tasks</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped m-0">
                            <thead>
                                <tr>
                                    <th>Task</th>
                                    <th>Project</th>
                                    <th>Assigned Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><a href="#">Optimize DB Queries</a></td>
                                    <td>Analytics App</td>
                                    <td>Apr 05, 2025</td>
                                    <td><span class="badge badge-warning">Paused</span></td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-xs btn-success"><i class="fas fa-play"></i></button>
                                            <button class="btn btn-xs btn-info"><i class="fas fa-check"></i></button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><a href="#">Refactor Login Logic</a></td>
                                    <td>Website Revamp</td>
                                    <td>Apr 04, 2025</td>
                                    <td><span class="badge badge-secondary">Pending</span></td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-xs btn-success"><i class="fas fa-play"></i></button>
                                            <button class="btn btn-xs btn-info"><i class="fas fa-check"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Project-wise Task Breakdown -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Project-wise Task Breakdown</h3>
                </div>
                <div class="card-body p-0">
                    <div class="accordion" id="projectBreakdown">
                        <div class="card mb-0 rounded-0">
                            <div class="card-header" id="heading0">
                                <h2 class="mb-0">
                                    <button class="btn btn-link btn-block text-left" type="button" data-toggle="collapse" data-target="#collapse0" aria-expanded="true" aria-controls="collapse0">
                                        <div class="d-flex justify-content-between">
                                            <span><i class="fas fa-project-diagram mr-2"></i> CRM Redesign</span>
                                            <span class="badge badge-primary">5 tasks</span>
                                        </div>
                                    </button>
                                </h2>
                            </div>
                            <div id="collapse0" class="collapse show" aria-labelledby="heading0" data-parent="#projectBreakdown">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-info"><i class="fas fa-tasks"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">Tasks</span>
                                                    <span class="info-box-number">5</span>
                                                    <div class="progress">
                                                        <div class="progress-bar bg-info" style="width: 60%"></div>
                                                    </div>
                                                    <span class="progress-description">60% Completed</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-danger"><i class="fas fa-bug"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">Bugs</span>
                                                    <span class="info-box-number">3</span>
                                                    <div class="progress">
                                                        <div class="progress-bar bg-danger" style="width: 33%"></div>
                                                    </div>
                                                    <span class="progress-description">33% Fixed</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <div class="card card-outline card-primary">
                                                <div class="card-header">
                                                    <h3 class="card-title">Time Today</h3>
                                                </div>
                                                <div class="card-body p-2">
                                                    <h4 class="text-center">3h 15m</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card card-outline card-info">
                                                <div class="card-header">
                                                    <h3 class="card-title">Last 7 Days</h3>
                                                </div>
                                                <div class="card-body p-2">
                                                    <h4 class="text-center">16h 40m</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column Starts -->
        <div class="col-lg-4 col-md-12">
            <!-- 7-Day Trend Graph -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">7-Day Trend</h3>
                </div>
                <div class="card-body">
                    <canvas id="trendChart" style="min-height: 250px; height: 250px;"></canvas>
                </div>
            </div>

            <!-- Upcoming Deliverables -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Upcoming Deliverables</h3>
                </div>
                <div class="card-body p-0">
                    <div class="timeline timeline-inverse p-3">
                        <div class="time-label">
                            <span class="bg-danger">09 Apr</span>
                        </div>
                        <div>
                            <i class="fas fa-cube bg-warning"></i>
                            <div class="timeline-item">
                                <span class="time"><i class="fas fa-clock"></i> 10:00 AM</span>
                                <h3 class="timeline-header"><a href="#">Website Redesign</a></h3>
                                <div class="timeline-body">
                                    <strong>Launch Landing Page</strong>
                                    <p class="mb-1 text-muted">Finalize UI and go live with homepage</p>
                                    <span class="badge badge-warning">At Risk</span>
                                </div>
                                <div class="timeline-footer">
                                    <a href="#" class="btn btn-primary btn-sm">View Details</a>
                                </div>
                            </div>
                        </div>
                        <div>
                            <i class="fas fa-clock bg-gray"></i>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Time Tracker -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Time Tracker</h3>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped m-0">
                            <thead>
                                <tr>
                                    <th>Project</th>
                                    <th>Today</th>
                                    <th>Last 7 Days</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>CRM Revamp</td>
                                    <td>
                                        <div class="progress-group">
                                            <span class="text-bold">3h</span>
                                            <div class="progress progress-sm">
                                                <div class="progress-bar bg-primary" style="width: 60%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="progress-group">
                                            <span class="text-bold">12h</span>
                                            <div class="progress progress-sm">
                                                <div class="progress-bar bg-success" style="width: 80%"></div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Internal Dashboard</td>
                                    <td>
                                        <div class="progress-group">
                                            <span class="text-bold">1.5h</span>
                                            <div class="progress progress-sm">
                                                <div class="progress-bar bg-primary" style="width: 30%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="progress-group">
                                            <span class="text-bold">4h</span>
                                            <div class="progress progress-sm">
                                                <div class="progress-bar bg-success" style="width: 40%"></div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Attendance Summary -->
            <div class="card">
                <div class="card-header d-flex">
                    <h3 class="card-title">Attendance Summary</h3>
                    <div class="card-tools ml-auto">
                        <a href="#" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="row p-3">
                        <div class="col-6 col-md-3 text-center">
                            <div class="attendance-stat">
                                <span class="text-bold text-lg text-success">18</span>
                                <p class="text-muted mb-0">Present</p>
                            </div>
                        </div>
                        <div class="col-6 col-md-3 text-center">
                            <div class="attendance-stat">
                                <span class="text-bold text-lg text-warning">2</span>
                                <p class="text-muted mb-0">Late</p>
                            </div>
                        </div>
                        <div class="col-6 col-md-3 text-center">
                            <div class="attendance-stat">
                                <span class="text-bold text-lg text-danger">1</span>
                                <p class="text-muted mb-0">Absent</p>
                            </div>
                        </div>
                        <div class="col-6 col-md-3 text-center">
                            <div class="attendance-stat">
                                <span class="text-bold text-lg text-info">1</span>
                                <p class="text-muted mb-0">Leaves</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-white">
                        <span class="text-muted">Current Month: April 2025</span>
                    </div>
                </div>
            </div>

            <!-- Employee of the Month -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Employee of the Month</h3>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <img class="profile-user-img img-fluid img-circle mb-3" src="https://via.placeholder.com/100" alt="Employee">
                        <h5 class="text-bold">Aisha Thomas</h5>
                        <p class="text-muted">Frontend Developer</p>
                    </div>
                    <div class="text-center mt-3">
                        <div class="d-inline-block p-3">
                            <h4 class="mb-0">95</h4>
                            <span class="text-muted">Performance Score</span>
                        </div>
                        <div class="d-inline-block p-3">
                            <h4 class="mb-0">45</h4>
                            <span class="text-muted">Tasks Completed</span>
                        </div>
                    </div>
                    <hr>
                    <h6 class="text-center">Leaderboard Preview</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><span class="badge badge-primary">#1</span> Aisha Thomas</span>
                            <span class="badge badge-secondary">95</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><span class="badge badge-primary">#2</span> Rahul Nair</span>
                            <span class="badge badge-secondary">89</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><span class="badge badge-primary">#3</span> Zara Khan</span>
                            <span class="badge badge-secondary">86</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div> <!-- End Right Column -->
    </div> <!-- End Row -->
</div> <!-- End Container -->

<!-- Optional: Basic styling -->
<style>
    .attendance-stat {
        padding: 10px;
        border-radius: 5px;
    }
    .timeline-item {
        margin-left: 60px;
        margin-right: 15px;
        margin-bottom: 15px;
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    }
</style>

<!-- Dummy chart script -->
<script>
document.addEventListener("DOMContentLoaded", function () {
    const ctx = document.getElementById("trendChart").getContext("2d");
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [
                {
                    label: 'Tasks Completed',
                    data: [3, 5, 2, 6, 4, 7, 5],
                    borderColor: 'rgba(60,141,188,1)',
                    backgroundColor: 'rgba(60,141,188,0.2)',
                    fill: true
                },
                {
                    label: 'Bugs Reported',
                    data: [1, 0, 3, 1, 2, 1, 0],
                    borderColor: 'rgba(210,214,222,1)',
                    backgroundColor: 'rgba(210,214,222,0.2)',
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});
</script>
