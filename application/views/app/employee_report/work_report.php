<?php
if (isset($start_date) && isset($end_date)){
    $start_date = DateTime::createFromFormat('Y-m-d', $_GET['start_date'])->format('d-m-Y');
    $end_date = DateTime::createFromFormat('Y-m-d', $_GET['end_date'])->format('d-m-Y');
    $user_id = $_GET['user_id'];
    $project_id = $_GET['project_id'];
}

?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.3.3/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>

<div class="p-2">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= strtoupper($page_title ?? '')?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url("app/work_report/overview_report"); ?>">Overview</a></li>
                        <li class="breadcrumb-item active">Employee Report</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Filter Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">Filter Report</h3>
                </div>
                <div class="card-body">
                    <form method="get" action="" class="form-horizontal">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="user_id">Employee</label>
                                    <select name="user_id" id="user_id" class="form-control select2">
                                        <option value="">Select Employee</option>
                                        <?php
                                        if (isset($users)){
                                            foreach ($users as $employee_id => $employee_name){
                                                $selected = $employee_id == $_GET['user_id'] ? 'selected' : '';
                                                echo "<option value='{$employee_id}' {$selected}>{$employee_name}</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="start_date">Start Date</label>
                                    <input type="date" name="start_date" id="start_date" value="<?=$_GET['start_date']?>" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="end_date">End Date</label>
                                    <input type="date" name="end_date" id="end_date" value="<?=$_GET['end_date']?>" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-filter"></i> Generate Report
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <?php if (isset($report) && isset($user_details)): ?>
                <!-- Employee Info Card -->
                <div class="card card-info">
                    <div class="card-header">
                        <h3 class="card-title">Employee Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">Name:</dt>
                                    <dd class="col-sm-8"><?=strtoupper($user_details['name'])?></dd>
                                    <dt class="col-sm-4">Phone:</dt>
                                    <dd class="col-sm-8"><?=strtoupper($user_details['phone'])?></dd>
                                    <dt class="col-sm-4">E-Code:</dt>
                                    <dd class="col-sm-8"><?=strtoupper($user_details['employee_code'])?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Report Card -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Work Report</h3>
                        <div class="card-tools">
                            <button onclick="downloadReport()" class="btn btn-tool">
                                <i class="fas fa-download"></i> Download PDF
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive" id="reportContent">
                            <?php foreach($report as $month => $month_data): ?>
                                <div class="month-section">
                                    <div class="month-header bg-light p-2">
                                        <h5 class="mb-0"><?=DateTime::createFromFormat('Y-m', $month)->format('F, Y')?></h5>
                                    </div>
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th style="width: 25%">Date</th>
                                                <th style="width: 75%">Details</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach($month_data['days'] as $day => $day_data): 
                                                // Convert time_taken to hours for comparison
                                                $time_parts = explode(':', $day_data['time_taken']);
                                                $total_hours = (int)$time_parts[0] + ((int)$time_parts[1] / 60);
                                                $bg_class = $total_hours < 7 ? 'table-danger' : 'table-primary';
                                            ?>
                                                <tr class="<?= $bg_class ?>">
                                                    <td class="align-middle">
                                                        <div class="d-flex align-items-center">
                                                            <i class="far fa-calendar-alt mr-2"></i>
                                                            <strong><?=DateTime::createFromFormat('Y-m-d', $day)->format('d F, Y')?></strong>
                                                        </div>
                                                    </td>
                                                    <td class="align-middle">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div class="d-flex align-items-center">
                                                                <i class="fas fa-clock mr-2"></i>
                                                                <span>Total Duration: <?=$day_data['time_taken']?></span>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php foreach($day_data['tasks'] as $task_id => $task): ?>
                                                    <tr class="table-light">
                                                        <td></td>
                                                        <td>
                                                            <div class="task-title">
                                                                <i class="fas fa-tasks text-primary mr-2"></i>
                                                                <strong><?=$task['title']?></strong>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <?php foreach($task['history'] as $history): ?>
                                                        <tr>
                                                            <td></td>
                                                            <td>
                                                                <div class="task-details">
                                                                    <div class="time-info mb-2">
                                                                        <i class="far fa-clock text-primary mr-2"></i>
                                                                        <span><?=$history['start_time']?> - <?=$history['end_time']?></span>
                                                                        <span class="text-muted ml-2">(<?=$history['time_taken']?>)</span>
                                                                    </div>
                                                                    <?php if(!empty($history['remarks'])): ?>
                                                                        <div class="remarks pl-4 border-left border-primary">
                                                                            <i class="far fa-comment text-primary mr-2"></i>
                                                                            <span><?=$history['remarks']?></span>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                <?php endforeach; ?>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
</div>

<style>
    .month-section {
        margin-bottom: 1rem;
    }

    .month-header {
        border-bottom: 2px solid #007bff;
    }

    .task-title {
        display: flex;
        align-items: center;
    }

    .task-details {
        padding: 0.5rem 0;
    }

    .time-info {
        display: flex;
        align-items: center;
    }

    .remarks {
        display: flex;
        align-items: flex-start;
        padding: 0.5rem 0;
    }

    .remarks i {
        margin-top: 0.25rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .table-responsive {
            margin: 0 -0.75rem;
        }
        
        .table td, .table th {
            padding: 0.5rem;
        }

        .task-details {
            padding: 0.25rem 0;
        }

        .remarks {
            padding-left: 1rem;
        }
    }

    @media print {
        .content-wrapper {
            padding: 0 !important;
        }

        .card {
            box-shadow: none !important;
        }

        .card-header {
            display: none;
        }

        .table-secondary {
            background-color: #f8f9fa !important;
        }

        .table-light {
            background-color: #f8f9fa !important;
        }

        .table td, .table th {
            padding: 0.5rem;
        }
    }

    /* Color adjustments for duration-based backgrounds */
    .table-danger {
        background-color: #fee2e2 !important;
        border-color: #fecaca !important;
    }

    .table-danger td {
        border-color: #fecaca !important;
        color: #7f1d1d !important;
    }

    .table-danger i {
        color: #7f1d1d !important;
    }

    .table-primary {
        background-color: #e3f2fd !important;
        border-color: #bbdefb !important;
    }

    .table-primary td {
        border-color: #bbdefb !important;
        color: #0d47a1 !important;
    }

    .table-primary i {
        color: #0d47a1 !important;
    }

    /* Print styles adjustment */
    @media print {
        .table-danger {
            background-color: #fee2e2 !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }

        .table-primary {
            background-color: #e3f2fd !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }

        .table-danger td,
        .table-danger i {
            color: #7f1d1d !important;
        }

        .table-primary td,
        .table-primary i {
            color: #0d47a1 !important;
        }
    }
</style>

<script type="text/javascript">
    function get_value(){
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        var user_id = $('#user_id').val();
        window.location.href='<?=base_url("app/work_report/employee_report/?")?>user_id=' + user_id +'&start_date=' + start_date + '&end_date=' + end_date;
    }

    function downloadReport() {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('l', 'mm', 'a4');
        const reportContent = document.getElementById('reportContent');
        
        html2canvas(reportContent).then(canvas => {
            const imgData = canvas.toDataURL('image/png');
            const imgWidth = 297; // A4 width in mm
            const imgHeight = canvas.height * imgWidth / canvas.width;
            
            doc.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
            doc.save('employee_report.pdf');
        });
    }
</script>