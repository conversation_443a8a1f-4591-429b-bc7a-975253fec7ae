<?php
if (isset($start_date) && isset($end_date)){
    $start_date = DateTime::createFromFormat('Y-m-d', $_GET['start_date'])->format('d-m-Y');
    $end_date = DateTime::createFromFormat('Y-m-d', $_GET['end_date'])->format('d-m-Y');
    $user_id = $_GET['user_id'];
    $project_id = $_GET['project_id'];
}

?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.3.3/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
<div class="container-fluid p-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/work_report/overview_report"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>


    </div>
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title text-center">
                <?= strtoupper($page_title ?? '')?>
            </h3>
        </div>
        <!-- /.card-header -->
        <div class="card-body p-1" id="download_area">
           <form method="get" action="">
               <div class="shadow_pro row p-1" style="max-width: 1000px;">
                   <div class="col-12 col-lg-3 p-2">
                       <select name="user_id" id="user_id" value="<?=$user_id?>"
                               class="form-control select2">
                           <option value="">Choose Employee</option>
                           <?php
                           if (isset($users)){
                               foreach ($users as $employee_id => $employee_name){
                                   $selected = $employee_id == $user_id ? 'selected' : '';
                                   echo "<option value='{$employee_id}' {$selected}>{$employee_name}</option>";
                               }
                           }
                           ?>
                       </select>
                   </div>
                   <div class="col-6 col-lg-2 p-2">
                       <input type="date" name="start_date" id="start_date" value="<?=$_GET['start_date']?>"
                              class="form-control">
                   </div>
                   <div class="col-6 col-lg-2 p-2">
                       <input type="date" name="end_date" id="end_date" value="<?=$_GET['end_date']?>"
                              class="form-control">
                   </div>
                   <div class="col-6 col-lg-2 p-2">
                       <button type="submit" class="btn btn-primary w-100">
                           Filter
                       </button>
                   </div>

               </div>
           </form>


        </div>
        <!-- /.card-body -->
    </div>
</div>
<div class="p-3">
    <?php
    if (isset($report)) {
        ?>
        <div class="shadow-pro pt-2 pb-2 bg-white p-1" >
            <div class="p-3">
                <div style="font-size: 34px; font-weight: bold">
                    <?=strtoupper($report['employee_report']['name'])?>
                </div>
                <div style="font-size: 20px; color: #282525">
                    <?=strtoupper($report['employee_report']['phone'])?>
                </div>
                <div style="font-size: 19px; color: #296b6b">
                    Employee Code: <?=strtoupper($report['employee_report']['employee_code'])?>
                </div>
            </div>
        </div>
        <div class="shadow-pro pt-2 pb-2 bg-white p-3 mt-2">
            <div class="text-center p-2">
                - Attendance Overview -
            </div>
            <div class="row text-center">
                <div class="col-lg-3 col-sm-6 p-2">
                    <div class="d-block p-2" style="background-color: #bbf5d0;color: rgba(24,140,60); border-radius: 10px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['P'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Present
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 p-2">
                    <div class="d-block p-2" style="background-color: #f3c7ce;color: rgb(180,7,31); border-radius: 10px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['A'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Absent
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-sm-6 p-2">
                    <div class="d-block p-2" style="background-color: #f5e3de;color: rgb(180,53,7); border-radius: 10px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['WH'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            WFH
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-sm-6 p-2">
                    <div class="d-block p-2" style="background-color: #d9e9ea;color: rgb(54,164,243); border-radius: 10px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['OF'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            OFF
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-sm-6 p-2">
                    <div class="d-block p-2" style="background-color: #e6d9ea;color: rgb(101,36,255); border-radius: 10px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['OD'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            On Duty
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-sm-6 p-2">
                    <div class="d-block p-2" style="background-color: #f5e3de;color: rgb(180,53,7); border-radius: 10px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['HD'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Half Day
                        </div>
                    </div>
                </div>

                <div class="col-lg-2 col-sm-6 p-2">
                    <div class="d-block p-2" style="background-color: #f3c7ce;color: rgb(180,7,31); border-radius: 10px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['early_going'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Early Going
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-sm-6 p-2">
                    <div class="d-block p-2" style="background-color: #f5e3de;color: rgb(180,53,7); border-radius: 10px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['late_coming'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Late Coming
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 p-2">
                    <div class="d-block p-2" style="background-color: #e6d9ea;color: rgb(101,36,255); border-radius: 10px">
                        <div class="fact_value">
                            <?=$report['employee_report']['time_log_overview']['work_duration'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Punch Time
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 p-2">
                    <div class="d-block p-2" style="background-color: #bbf5d0;color: rgba(24,140,60); border-radius: 10px">
                        <div class="fact_value">
                            <?=$report['employee_report']['time_log_overview']['job_duration'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Job Time
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <div class="shadow-pro pt-2 pb-2 bg-white p-3 mt-2">
        <div class="text-center p-2">
            - Project wise Report -
        </div>
        <div class="row p-3">
            <?php
                foreach ($report['project_wise_report'] as $key => $project){
                    ?>
                    <div class="col-12 p-3">
                        <div class="row p-3" style="background-color: rgb(238,243,239); border-radius: 4px;">
                            <div class="col-lg-8 col-sm-12">
                                <div style="font-size: 25px;color: #0d2d1b">
                                    <?=($key+1).') '."<b>{$project['project_name']}</b>"?>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-12">
                                <div style="font-size: 30px;text-align: right; font-weight: bold;color: #1b4d31">
                                    <?=round($project['percentage'], 2)?> %
                                </div>
                                <div style="font-size: 22px;text-align: right; font-weight: bold;color: #7d56ff;padding-right: 15px">
                                    <?=$project['duration']?>
                                </div>
                                <a class="btn btn-sm btn-info float-right mt-2" target="_blank" style="width: 140px"
                                   href="<?=base_url("app/work_report/employee_report/?project_id={$project['project_id']}&start_date={$_GET['start_date']}&end_date={$_GET['end_date']}&user_id={$_GET['user_id']}")?>">
                                    View Detailed
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            ?>
        </div>

        <?php
    }
    ?>
</div>

<script type="text/javascript">
    function get_value(){
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        var user_id = $('#user_id').val();
        window.location.href='<?=base_url("app/work_report/employee_report/?")?>user_id=' + user_id +'&start_date=' + start_date + '&end_date=' + end_date;
    }
</script>


<style>
    .fact_value{
        font-size: 40px !important;
        font-weight: bold !important;
    }
    .fact_label{
        font-size: 18px !important;
    }
</style>