<?php
if (isset($start_date) && isset($end_date) && isset($report)){

    $projects = $this->projects_m->get(null, ['id', 'title', 'project_type'])->result_array();
    $projects = array_column($projects, 'title', 'id');

    if ($start_date == $end_date){
        $work_date = DateTime::createFromFormat('Y-m-d', $start_date)->format('d-m-Y (l)');
    }else{
        $start_date = DateTime::createFromFormat('Y-m-d', $start_date)->format('d-m-Y');
        $end_date = DateTime::createFromFormat('Y-m-d', $end_date)->format('d-m-Y');
        $work_date = "{$start_date} <small>to</small> {$end_date}";
    }
    $date_array = array_reverse(get_date_array($start_date, $end_date));
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Employee Report : <?=$work_date?> - <?=$report['employee_report']['name']?></title>
    </head>
    <body style="background-color: #f5faf8">
    <div class="header">
        <div class="col-6">
            <div id="logo_title" style="padding: 10px;padding-left:13px;padding-bottom:5px;font-size: 14px">
                <a href="https://pms.trogon.info" target="_blank" style="text-decoration: none; color: azure">pms.trogon.info</a>
            </div>
            <div id="logo" style="padding: 10px;padding-top: 2px;">
                <a href="https://pms.trogon.info" target="_blank">
                    <img src="<?=base_url('assets/logo/logo.png')?>" alt="Trogon Logo" style="width: 115px;height: auto">
                </a>
            </div>

        </div>

        <div class="col-6" style="text-align: right">
            <div class="header_title"><small>Employee Report:</small> <?=$report['employee_report']['name']?></div>
            <div class="header_date"><?=$work_date?></div>
            <div style="font-size: 10px; padding-right: 10px; color: #bdecec;">
                Generated on: <?= DateTime::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->format('d-m-Y g:i A')?>
            </div>
        </div>
    </div>
    <div class="main">
        <div id="attendance">
            <div style="padding: 5px;padding-top: 10px">
                <div style="background-color: rgb(234,238,248);padding: 7px;font-size: 17px;border-radius: 5px">
                    Attendance Overview
                </div>
            </div>
            <div style="text-align: center;width: 100%">
                <div style="padding: 5px;width: 15.2%;float: left">
                    <div style="background-color: #edf5f0;color: rgb(98,150,143); border-radius: 10px;padding: 8px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['P'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Present
                        </div>
                    </div>
                </div>
                <div style="padding: 5px;width: 15.2%;float: left">
                    <div style="background-color: #fddee4;color: rgb(173,52,68); border-radius: 10px;padding: 8px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['A'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Absent
                        </div>
                    </div>
                </div>
                <div style="padding: 5px;width: 15.2%;float: left">
                    <div style="background-color: #f8edea;color: rgb(199,88,48); border-radius: 10px;padding: 8px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['WH'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            WFH
                        </div>
                    </div>
                </div>
                <div style="padding: 5px;width: 15.2%;float: left">
                    <div style="background-color: #ebf7f8;color: rgb(54,164,243); border-radius: 10px;padding: 8px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['OF'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            OFF
                        </div>
                    </div>
                </div>
                <div style="padding: 5px;width: 15.2%;float: left">
                    <div style="background-color: #f4eff6;color: rgb(137,91,245); border-radius: 10px;padding: 8px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['OD'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            On Duty
                        </div>
                    </div>
                </div>
                <div style="padding: 5px;width: 15.2%;float: left">
                    <div style="background-color: #f5e8e5;color: rgb(204,75,28); border-radius: 10px;padding: 8px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['HD'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Half Day
                        </div>
                    </div>
                </div>
                <div style="padding: 5px;width: 15.2%;float: left">
                    <div style="background-color: #f8e1e4;color: rgb(211,49,72); border-radius: 10px;padding: 8px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['early_going'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Early Going
                        </div>
                    </div>
                </div>
                <div style="padding: 5px;width: 15.2%;float: left">
                    <div style="background-color: #faf0ee;color: rgb(218,87,40); border-radius: 10px;padding: 8px">
                        <div class="fact_value">
                            <?=$report['employee_report']['attendance_overview']['late_coming'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Late Coming
                        </div>
                    </div>
                </div>
                <div style="padding: 5px;width: 31.7%;float: left">
                    <div style="background-color: #f4eff6;color: rgb(163,129,241);  border-radius: 10px;padding: 8px">
                        <div class="fact_value">
                            <?=$report['employee_report']['time_log_overview']['work_duration'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Punch Time
                        </div>
                    </div>
                </div>
                <div style="padding: 5px;width: 31.2%;float: left">
                    <div style="background-color: #edf5f0;color: rgb(126,175,168); border-radius: 10px;padding: 8px">
                        <div class="fact_value">
                            <?=$report['employee_report']['time_log_overview']['job_duration'] ?? 0?>
                        </div>
                        <div class="fact_label">
                            Job Time
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="project_overview" >
            <div style="padding: 5px;padding-top: 10px">
                <div style="background-color: rgb(234,238,248);padding: 7px;font-size: 17px;border-radius: 5px">
                    Project Overview
                </div>
            </div>
            <div class="" style="padding: 8px;">
                <table style="width: 100%">
                    <tr>
                        <th>#</th>
                        <th>Project Name</th>
                        <th>Job Duration</th>
                        <th>Percentage</th>
                    </tr>
                    <?php
                        foreach ($report['project_wise_report'] as $key => $project){
                            ?>
                            <tr>
                                <td><?=$key+1?></td>
                                <td style="font-weight: bold;color: rgb(82,79,79)"><?=strtoupper($project['project_name'])?></td>
                                <td style="text-align: right; color: #5571a4;background-color: rgb(244,246,250)">
                                    <?=$project['duration']?>
                                </td>
                                <td style="text-align: right; font-size: 19px;color: #837a62;font-weight: bold;background-color: rgb(252,252,248)">
                                    <?=round($project['percentage'], 2)?> %
                                </td>
                            </tr>
                            <?php
                        }
                    ?>
                </table>
            </div>
        </div>

        <div id="monthly_report" >
            <div style="padding: 5px;padding-top: 10px">
                <div style="background-color: rgb(234,238,248);padding: 7px;font-size: 17px;border-radius: 5px">
                    Monthly Report
                </div>
            </div>
            <?php
                $months = get_months_from_dates($start_date, $end_date);
                foreach ($months as $month) {
                    $month_report = $report['monthly_report'][$month];
                    ?>
                    <div class="" style="padding: 8px;">
                        <table style="width: 100%">
                            <tr>
                                <th>#</th>
                                <th>Project Name</th>
                                <th>Job Duration</th>
                                <th>Percentage</th>
                            </tr>
                            <?php
                            foreach ($month_report as $project_id => $project_tasks){
                                ?>
                                    <tr>
                                        <td colspan="4"><?=$projects[$project_id]?></td>
                                    </tr>
                                <tr>
                                    <td><?=$key+1?></td>
                                    <td style="font-weight: bold;color: rgb(82,79,79)"><?=strtoupper($project['project_name'])?></td>
                                    <td style="text-align: right; color: #5571a4;background-color: rgb(244,246,250)">
                                        <?=$project['duration']?>
                                    </td>
                                    <td style="text-align: right; font-size: 19px;color: #837a62;font-weight: bold;background-color: rgb(252,252,248)">
                                        <?=round($project['percentage'], 2)?> %
                                    </td>
                                </tr>
                                <?php
                            }
                            ?>
                        </table>
                    </div>
                    <?php
                }
            ?>

        </div>

        <?php
        $users = [];
        foreach ($users as $user){
            ?>
            <div style="padding:7px 2px;">
                <div style="background-color: #ffffff;border-radius: 5px;padding: 10px">
                    <div id="employee_name" style="padding:5px;border-radius:4px;font-size: 17px;font-weight: bold;color: #1a5e60; background-color: rgb(248,241,248);text-align: center">
                        <?=strtoupper($user['name'])?>
                    </div>
                    <div class="">
                        <?php
                        $user_jobs = $work_report[$user['id']]['jobs'];
                        foreach ($date_array as $job_date){
                            $user_attendance = $attendance[$user['id']][$job_date];

                            $user_jobs = $work_report[$user['id']]['jobs'][$job_date] ?? [];
                            $job_date = DateTime::createFromFormat('Y-m-d', $job_date)->format('d-m-Y (l)');
                            ?>
                            <div style='padding: 6px;'>
                                <?php
                                if ($user_attendance == 'off' || $user_attendance == 'absent'){
                                    ?>
                                    <div style='text-align:center; color: #ec2136; padding:10px;font-size: 15px;font-weight: bold;background-color: rgb(250,229,231);display: inline-block;'>
                                        <?=$job_date?>
                                        <span style="color: #ef5e5e; font-size:12px">
                                                                <small> - Attendance: </small><?=strtoupper($user_attendance)?>
                                                            </span>
                                    </div>
                                    <?php
                                }else{
                                    ?>
                                    <div style='color: #7a62e5; font-size: 13px;font-weight: bold;background-color: rgba(211,225,239,0.1);display: inline-block;'>
                                        <?=$job_date?>
                                        <span style="color: #a588ef; font-size:12px">
                                                                <small> - Attendance: </small><?=strtoupper($user_attendance)?>
                                                            </span>
                                    </div>
                                    <?php
                                }
                                ?>

                            </div>
                            <?php
                            if(count($user_jobs)){
                                foreach ($user_jobs as $job){
                                    ?>
                                    <div style="padding: 5px;">

                                        <div style="font-size: 14px;color:#113665;background-color: rgba(239,246,250,0.73);padding: 7px;font-weight: bold">
                                            <?=$job['details']['title']?>

                                            <div style="margin-top:5px;font-size: 12px!important;float: right!important;color: rgb(31,84,225);text-align: right;font-weight: normal!important;">
                                                [TRG-<?=$job['details']['id']?>] -
                                                <?php
                                                if ($job['details']['task_status']=='completed' || $job['details']['task_status']=='testing'){
                                                    $task_status_color = '#069647';
                                                    $task_status = 'COMPLETED';
                                                }elseif($job['details']['task_details'] == 'on_hold'){
                                                    $task_status_color = '#F6B84B';
                                                    $task_status = 'ON HOLD';
                                                }else{
                                                    $task_status_color = '#EF6547';
                                                    $task_status = 'IN PROGRESS';
                                                }
                                                ?>
                                                <span style="font-size: 10px!important;color: <?=$task_status_color?>;">
                                                                        <small>[<?=$task_status?>]</small>
                                                                    </span>
                                            </div>
                                        </div>
                                        <div style="padding-top: 15px;padding-bottom: 15px">
                                            <?php
                                            foreach($job['history'] as $history){
                                                if (!empty($history['start_time']) && !empty($history['end_time'])){
                                                    $start_time = DateTime::createFromFormat('H:i:s', $history['start_time'])->format('g:i A');
                                                    $end_time = DateTime::createFromFormat('H:i:s', $history['end_time'])->format('g:i A');
                                                }else{
                                                    $start_time = '';
                                                    $end_time = '';
                                                }
                                                $updated_on = DateTime::createFromFormat('Y-m-d H:i:s', $history['updated_on'])->format('d-m-Y, g:i A')
                                                ?>
                                                <div style="padding: 2px 0">
                                                    <div style="background-color: rgba(238,248,241,0.05); border: 1px solid rgb(231,246,235)">
                                                        <div style="font-size:13px;background-color:rgba(238,248,241,0.40);color: #234b4d;">
                                                            <div style="padding: 1px 5px">
                                                                <?= "<b>{$history['time_taken']}</b> - <small>[{$start_time} to {$end_time}]</small>"?>
                                                            </div>
                                                            <div style="padding: 3px 5px;font-size: 8px;color: #4e5e86;">
                                                                Updated On: <?=$updated_on?>
                                                            </div>
                                                        </div>
                                                        <div style="padding: -5px 5px; font-size: 12px;">
                                                            <?=$history['remarks']?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php
                                            }
                                            ?>
                                        </div>
                                    </div>
                                    <?php
                                }
                            }elseif($user_attendance!='absent' && $user_attendance!='off'){
                                ?>
                                <div style='font-size: 12px;color: #e50719;text-align: center;padding: 20px;background-color: rgba(254,136,145,0.13)'>
                                    <img width="28" height="28" src="https://img.icons8.com/color/48/error--v1.png" alt="error--v1"/><br><br>
                                    No Jobs Found for <b><?=strtoupper($user['name'])?></b> on <?=$job_date?>
                                </div>
                                <?php
                            }
                        }


                        ?>
                    </div>
                </div>
            </div>
            <?php
        }
        ?>
    </div>
    <style>
        .col-4{
            width: 33%!important;
            float: left!important;
        }
        .col-6{
            width: 50%!important;
            float: left!important;
        }
        body{
            border:1px solid #454545;
            font-family: Arial, sans-serif;
        }
        .header{
            background-image: linear-gradient(90deg, #23384E, #166686)!important;
            color:#efefef;
            padding:5px;
            border-radius: 7px;
        }
        .header_title{
            font-size: 18px;
            padding: 5px 10px;
        }
        .header_date{
            padding: 2px 10px 10px;
            font-weight: bold;
            font-size: 12px;
            color: azure;
        }

        .fact_value{
            font-size: 26px !important;
            font-weight: bold !important;
        }
        .fact_label{
            font-size: 14px !important;
        }
        table, tr, td, th{
            border-collapse: collapse !important;
        }
        td, th{
            border: 1px solid #999999!important;
            border-color: #ececec !important;
            padding: 9px;
            font-size: 16px;
        }
        th{
            background-color: rgb(250, 248, 238);
        }
    </style>
    </body>
    </html>
    <?php
}
?>