<?php
if (isset($start_date) && isset($end_date)){
    $start_date = DateTime::createFromFormat('Y-m-d', $_GET['start_date'])->format('d-m-Y');
    $end_date = DateTime::createFromFormat('Y-m-d', $_GET['end_date'])->format('d-m-Y');
    $user_id = $_GET['user_id'];
    $project_id = $_GET['project_id'];
}

?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.3.3/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>

<div class="report-container">
    <!-- Header Section -->
    <div class="report-header">
        <div class="header-content">
            <div class="back-button">
                <a href="<?= base_url("app/work_report/overview_report"); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-circle-left"></i> Back to Overview
                </a>
            </div>
            <div class="report-title">
                <h1><?= strtoupper($page_title ?? '')?></h1>
                <div class="report-subtitle">Employee Work Report</div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <div class="filter-card">
            <form method="get" action="" class="filter-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="user_id">Employee</label>
                        <select name="user_id" id="user_id" class="form-control select2">
                            <option value="">Select Employee</option>
                            <?php
                            if (isset($users)){
                                foreach ($users as $employee_id => $employee_name){
                                    $selected = $employee_id == $_GET['user_id'] ? 'selected' : '';
                                    echo "<option value='{$employee_id}' {$selected}>{$employee_name}</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="start_date">Start Date</label>
                        <input type="date" name="start_date" id="start_date" value="<?=$_GET['start_date']?>"
                               class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="end_date">End Date</label>
                        <input type="date" name="end_date" id="end_date" value="<?=$_GET['end_date']?>"
                               class="form-control">
                    </div>
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="submit" class="btn btn-primary filter-btn">
                            <i class="fas fa-filter"></i> Generate Report
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <?php if (isset($report) && isset($user_details)): ?>
        <!-- Employee Details Section -->
        <div class="employee-profile">
            <div class="profile-card">
                <div class="profile-header">
                    <div class="profile-info">
                        <h2 class="employee-name"><?=strtoupper($user_details['name'])?></h2>
                        <div class="employee-details">
                            <div class="detail-item">
                                <i class="fas fa-phone"></i>
                                <span><?=strtoupper($user_details['phone'])?></span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-id-card"></i>
                                <span>E-Code: <?=strtoupper($user_details['employee_code'])?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Section -->
        <div class="report-content">
            <?php foreach($report as $month => $days): ?>
                <div class="month-section">
                    <div class="month-header">
                        <div class="month-title">
                            <i class="far fa-calendar-alt"></i>
                            <span><?=DateTime::createFromFormat('Y-m', $month)->format('F, Y')?></span>
                        </div>
                    </div>
                    
                    <?php foreach($days as $day => $day_data): ?>
                        <div class="day-section">
                            <div class="day-header" onclick="toggleTasks(this)">
                                <div class="day-info">
                                    <div class="date">
                                        <i class="far fa-calendar"></i>
                                        <?=DateTime::createFromFormat('Y-m-d', $day)->format('d F, Y')?>
                                    </div>
                                    <div class="duration">
                                        <i class="fas fa-clock"></i>
                                        Total Duration: <?=$day_data['time_taken']?>
                                    </div>
                                    <div class="toggle-icon">
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="tasks-container" style="display: none;">
                                <?php foreach($day_data['tasks'] as $task_id => $task): ?>
                                    <div class="task-card">
                                        <div class="task-header">
                                            <h3 class="task-title">
                                                <i class="fas fa-tasks"></i>
                                                <?=$task['title']?>
                                            </h3>
                                        </div>
                                        <div class="task-timeline">
                                            <?php foreach($task['history'] as $history): ?>
                                                <div class="timeline-item">
                                                    <div class="timeline-content">
                                                        <div class="time-info">
                                                            <div class="time-range">
                                                                <i class="far fa-clock"></i>
                                                                <?=$history['start_time']?> - <?=$history['end_time']?>
                                                            </div>
                                                            <div class="duration">
                                                                <i class="fas fa-hourglass-half"></i>
                                                                <?=$history['time_taken']?>
                                                            </div>
                                                        </div>
                                                        <?php if(!empty($history['remarks'])): ?>
                                                            <div class="remarks">
                                                                <i class="far fa-comment"></i>
                                                                <span><?=substr($history['remarks'], 0, 50)?><?=strlen($history['remarks']) > 50 ? '...' : ''?></span>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<script type="text/javascript">
    function get_value(){
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        var user_id = $('#user_id').val();
        window.location.href='<?=base_url("app/work_report/employee_report/?")?>user_id=' + user_id +'&start_date=' + start_date + '&end_date=' + end_date;
    }

    function toggleTasks(header) {
        const tasksContainer = header.nextElementSibling;
        const isExpanded = header.classList.contains('expanded');
        
        // Toggle the expanded class
        header.classList.toggle('expanded');
        
        // Toggle the visibility of tasks
        if (isExpanded) {
            tasksContainer.style.display = 'none';
        } else {
            tasksContainer.style.display = 'block';
        }
    }
</script>

<style>
    /* General Styles */
    .report-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
        background-color: #f8f9fa;
    }

    /* Header Styles */
    .report-header {
        background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .report-title h1 {
        font-size: 2rem;
        margin: 0;
        font-weight: 600;
    }

    .report-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 0.5rem;
    }

    /* Filter Section */
    .filter-section {
        margin-bottom: 2rem;
    }

    .filter-card {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .filter-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        align-items: end;
    }

    .form-group {
        margin-bottom: 0;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.75rem;
        color: #2c3e50;
        font-weight: 500;
        font-size: 0.95rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .form-control {
        height: 45px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .select2-container--default .select2-selection--single {
        height: 45px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 45px;
        padding-left: 1rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 43px;
    }

    .filter-btn {
        height: 45px;
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        border: none;
        color: white;
        font-weight: 500;
        font-size: 0.95rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border-radius: 8px;
    }

    .filter-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    /* Employee Profile */
    .employee-profile {
        margin-bottom: 2rem;
    }

    .profile-card {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .profile-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .employee-name {
        font-size: 1.8rem;
        color: #2c3e50;
        margin: 0;
        font-weight: 600;
    }

    .employee-details {
        display: flex;
        gap: 1.5rem;
        margin-top: 1rem;
    }

    .detail-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #6c757d;
        font-size: 1rem;
    }

    /* Month Section */
    .month-section {
        margin-bottom: 2.5rem;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .month-header {
        background: #2c3e50;
        padding: 1.25rem 1.5rem;
        color: white;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .month-title {
        font-size: 1.4rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0;
    }

    .month-title i {
        font-size: 1.2rem;
    }

    /* Day Section */
    .day-section {
        background: white;
        border-bottom: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .day-section:last-child {
        border-bottom: none;
    }

    .day-header {
        background: #f8f9fa;
        padding: 1rem 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }

    .day-header:hover {
        background: #e9ecef;
    }

    .day-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .toggle-icon {
        color: #6c757d;
        transition: transform 0.3s ease;
    }

    .day-header.expanded .toggle-icon {
        transform: rotate(180deg);
    }

    .tasks-container {
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .date {
        font-size: 1.1rem;
        color: #2c3e50;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .date i {
        color: #3498db;
    }

    /* Duration Styles */
    .duration {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.95rem;
        color: white;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
    }

    .duration.standard-hours {
        background: #2ecc71; /* Green for 7 hours */
    }

    .duration.below-standard {
        background: #e67e22; /* Orange for less than 7 hours */
    }

    /* Task Card */
    .task-card {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e9ecef;
        background: white;
    }

    .task-card:last-child {
        border-bottom: none;
    }

    .task-header {
        margin-bottom: 0.75rem;
    }

    .task-title {
        font-size: 1rem;
        color: #2c3e50;
        margin: 0;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Timeline */
    .task-timeline {
        margin-left: 1.5rem;
        border-left: 2px solid #e9ecef;
        padding-left: 1.5rem;
    }

    .timeline-item {
        position: relative;
        padding: 0.75rem 0;
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: -1.7rem;
        top: 1.2rem;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #3498db;
        border: 2px solid white;
    }

    .timeline-content {
        background: #f8f9fa;
        padding: 0.75rem 1rem;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .timeline-content:hover {
        background: #e9ecef;
    }

    .time-info {
        display: flex;
        gap: 1rem;
        margin-bottom: 0.5rem;
        flex-wrap: wrap;
    }

    .time-range, .duration {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #2c3e50;
        font-size: 0.9rem;
        background: white;
        padding: 0.4rem 0.75rem;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    }

    .time-range i, .duration i {
        color: #3498db;
    }

    .remarks {
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 0.5rem;
        background: white;
        padding: 0.5rem 0.75rem;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    }

    .remarks i {
        color: #3498db;
        margin-top: 0.2rem;
    }

    /* Calendar Navigation */
    .calendar-nav {
        position: sticky;
        top: 0;
        background: white;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        z-index: 100;
    }

    .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .calendar-header {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .calendar-header span {
        text-align: center;
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
    }

    .calendar-day {
        aspect-ratio: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;
        color: #2c3e50;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .calendar-day:hover {
        background: #e9ecef;
    }

    .calendar-day.has-data {
        background: #3498db;
        color: white;
        border-color: #2980b9;
    }

    .calendar-day.has-data.standard-hours {
        background: #2ecc71;
        border-color: #27ae60;
    }

    .calendar-day.has-data.below-standard {
        background: #e67e22;
        border-color: #d35400;
    }

    .calendar-day.today {
        border: 2px solid #3498db;
    }

    .calendar-day.other-month {
        opacity: 0.5;
    }

    /* Add this JavaScript before the closing </script> tag */
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Create calendar navigation
        function createCalendarNavigation() {
            const calendarNav = document.createElement('div');
            calendarNav.className = 'calendar-nav';
            
            const calendarHeader = document.createElement('div');
            calendarHeader.className = 'calendar-header';
            ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].forEach(day => {
                const span = document.createElement('span');
                span.textContent = day;
                calendarHeader.appendChild(span);
            });
            
            const calendarGrid = document.createElement('div');
            calendarGrid.className = 'calendar-grid';
            
            // Get the first month from the report data
            const firstMonthSection = document.querySelector('.month-section');
            if (!firstMonthSection) return;
            
            const monthTitle = firstMonthSection.querySelector('.month-title span').textContent;
            const [monthName, year] = monthTitle.split(', ');
            const monthIndex = new Date(`${monthName} 1, ${year}`).getMonth();
            
            // Create date for the first day of the month
            const firstDay = new Date(year, monthIndex, 1);
            const lastDay = new Date(year, monthIndex + 1, 0);
            
            // Add previous month's days
            const firstDayOfWeek = firstDay.getDay();
            for (let i = 0; i < firstDayOfWeek; i++) {
                const day = document.createElement('div');
                day.className = 'calendar-day other-month';
                calendarGrid.appendChild(day);
            }
            
            // Add current month's days
            for (let i = 1; i <= lastDay.getDate(); i++) {
                const day = document.createElement('div');
                day.className = 'calendar-day';
                day.textContent = i;
                
                // Check if this day has data
                const dateStr = `${year}-${String(monthIndex + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
                const dayData = document.querySelector(`[data-date="${dateStr}"]`);
                
                if (dayData) {
                    day.classList.add('has-data');
                    const duration = dayData.querySelector('.duration');
                    if (duration) {
                        const hours = parseFloat(duration.textContent.trim().split(' ')[0]);
                        if (hours >= 7) {
                            day.classList.add('standard-hours');
                        } else {
                            day.classList.add('below-standard');
                        }
                    }
                    
                    // Add click event to scroll to the day
                    day.addEventListener('click', () => {
                        dayData.scrollIntoView({ behavior: 'smooth' });
                    });
                }
                
                // Highlight today if it's the current month
                const today = new Date();
                if (i === today.getDate() && 
                    monthIndex === today.getMonth() && 
                    year == today.getFullYear()) {
                    day.classList.add('today');
                }
                
                calendarGrid.appendChild(day);
            }
            
            calendarNav.appendChild(calendarHeader);
            calendarNav.appendChild(calendarGrid);
            
            // Insert calendar before the report content
            document.querySelector('.report-container').insertBefore(calendarNav, document.querySelector('.report-content'));
        }

        // Add data-date attributes to day sections
        document.querySelectorAll('.day-section').forEach(section => {
            const dateText = section.querySelector('.date').textContent.trim();
            const date = new Date(dateText);
            const dateStr = date.toISOString().split('T')[0];
            section.setAttribute('data-date', dateStr);
        });

        // Create calendar navigation
        createCalendarNavigation();

        // Update duration colors
        document.querySelectorAll('.duration').forEach(duration => {
            const durationText = duration.textContent.trim();
            const hours = parseFloat(durationText.split(' ')[0]);
            
            if (hours >= 7) {
                duration.classList.add('standard-hours');
            } else {
                duration.classList.add('below-standard');
            }
        });
    });
    </script>

    <style>
    /* Month Navigation */
    .month-navigation {
        position: sticky;
        top: 0;
        background: white;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        display: flex;
        gap: 0.5rem;
        overflow-x: auto;
        z-index: 100;
    }

    .month-nav-item {
        padding: 0.5rem 1rem;
        background: #f8f9fa;
        border-radius: 6px;
        color: #2c3e50;
        text-decoration: none;
        font-size: 0.9rem;
        white-space: nowrap;
        transition: all 0.3s ease;
    }

    .month-nav-item:hover {
        background: #3498db;
        color: white;
    }

    .month-nav-item.active {
        background: #3498db;
        color: white;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .report-container {
            padding: 1rem;
        }

        .header-content {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .employee-details {
            flex-direction: column;
            gap: 0.5rem;
        }

        .day-info {
            flex-direction: column;
            align-items: flex-start;
        }

        .date, .duration {
            width: 100%;
        }

        .time-info {
            flex-direction: column;
        }

        .time-range, .duration {
            width: 100%;
        }

        .filter-form {
            grid-template-columns: 1fr;
        }

        .month-navigation {
            padding: 0.75rem;
        }

        .month-nav-item {
            padding: 0.4rem 0.75rem;
            font-size: 0.85rem;
        }
    }
    </style>
</style>