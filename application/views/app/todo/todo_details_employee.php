<?php
$item_id = $param1;
$todo_details = $this->todo_m->get(['id' => $item_id])->row_array();
$todo_category = $this->todo_category_m->get(['id' => $todo_details['todo_category_id']], ['id', 'title'])->row()->title;
?>
<div class="shadow-pro">
    <h5 style="background-color: #f4f4fa" class="text-primary p-2 m-0"><?=$todo_details['title']?></h5>
    <div class="text-muted p-2 pb-0" style="font-size: 15px;background-color: rgba(247,247,250,0.35)">
        <?=$todo_details['description']?>
        <table class="table table-bordered mt-1 text-center" style="width: 400px;">
            <tr>
                <th>Priority</th>
                <td>
                    <?= get_todo_priority($todo_details['todo_priority'])?>
                </td>
            </tr>
            <tr>
                <th>Due Date</th>
                <td>
                    <?= get_due_date($todo_details['due_date'])?>
                </td>
            </tr>
            <tr>
                <th>Todo Category</th>
                <td>
                    <?=$todo_category?>
                </td>
            </tr>
        </table>
    </div>
</div>
<?php
    if ($todo_details['todo_status'] == 'assigned' && $todo_details['user_id'] == get_user_id()){
        ?>
        <form class="form-horizontal" action="<?=base_url('app/todo/employee_update_status/'.$todo_details['id'])?>" method="post" enctype="multipart/form-data">
            <div class="row pt-4" style="margin: 0!important;">
                <div class="form-group col-6 p-0">
                    <label for="todo_status" class="col-sm-12 col-form-label text-muted">Todo Status <span class="text-danger">*</span></label>
                    <div class="col-sm-12">
                        <select class="form-control select2" id="todo_status" name="todo_status" required>
                            <option value="assigned" <?=$todo_details['todo_status'] == 'assigned' ? 'assigned' : ''?>>Pending</option>
                            <option value="completed"  <?=$todo_details['todo_status'] == 'completed' ? 'completed' : ''?>>Completed</option>
                        </select>
                    </div>
                </div>
                <div class="form-group col-12 p-0">
                    <label for="remarks" class="col-sm-12 col-form-label text-muted ">Remarks</label>
                    <div class="col-sm-12">
                        <textarea class="form-control ck_editor" id="remarks" name="remarks" placeholder="Enter Remarks"></textarea>
                    </div>
                </div>

            </div>
            <div class="col-12 " >
                <button type="submit" name="add" value="Save" class="btn btn-info btn-mini float-right" style="float: right!important;width: 120px;margin-top: 38px;">
                    <small><i class="fa fa-check"></i></small> Update
                </button>
            </div>


        </form>
        <?php
    }
?>



<script type="text/javascript">
    $('.ck_editor').each(function(){
        CKEDITOR.replace(this, {
            height: '120px'
        });
    });
    $('.select2').select2();
</script>
