<div class="container-fluid mobile_hide">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=todo/add', '<?= get_phrase('add_todo'); ?>')" style="width: 160px;"
                class="btn btn-primary btn-mini float-right">
            <small><i class="fas fa-plus"></i></small> Add <?= $page_title ?? '' ?>
        </button>
    </div>
    <div class="card card-primary row mt-3 shadow-pro" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title"><?= strtoupper($page_title ?? '')?></h3>
        </div>
        <!-- /.card-header -->
        <div class="card-body ">
            <?php
            if (isset($start_date) && isset($end_date) && isset($status_count)){
                ?>
                <form action="" method="get">
                    <div class="row mt-2 " style="max-width: 1000px;">
                        <div class="col-3 form-group p-2">
                            <input type="date" value="<?=$start_date?>" class="form-control" id="start_date" name="start_date" required>
                        </div>
                        <div class="col-3 form-group p-2">
                            <input type="date" value="<?=$end_date?>" class="form-control" id="end_date" name="end_date" required>
                        </div>
                        <div class="col-3 form-group p-2">
                            <select class="form-control select2" id="todo_status" name="todo_status" required>
                                <option value="all" <?=$_GET['todo_status'] == 'all' ? 'selected' : ''?>>All</option>
                                <option value="pending" <?=$_GET['todo_status'] == 'pending' ? 'selected' : ''?>>Pending</option>
                                <option value="assigned" <?=$_GET['todo_status'] == 'assigned' ? 'selected' : ''?>>Assigned</option>
                                <option value="completed" <?=$_GET['todo_status'] == 'completed' ? 'selected' : ''?>>Completed</option>
                            </select>
                        </div>
                        <div class="col-2 form-group p-2">
                            <button type="submit" class="btn btn-secondary btn-block">
                                <small><i class="bi bi-funnel-fill"></i></small> Filter
                            </button>
                        </div>
                    </div>
                </form>
                <div class="p-2" style="margin-bottom: 10px">
                    <div class="row">
                        <div class="d-inline-block mr-2">
                            <a href="<?=base_url("app/todo/index/?start_date={$start_date}&end_date={$end_date}&todo_status=all")?>"
                               class="btn btn-sm <?=$_GET['todo_status'] == 'all' || $_GET['todo_status'] == '' ? 'btn-primary' : 'btn-outline-primary'?>" style="width: 150px; font-size: 16px!important;">
                                ALL (<b><?=$status_count['all']?></b>)
                            </a>
                        </div>
                        <div class="d-inline-block mr-2">
                            <a href="<?=base_url("app/todo/index/?start_date={$start_date}&end_date={$end_date}&todo_status=pending")?>"
                               class="btn btn-sm <?=$_GET['todo_status'] == 'pending' ? 'btn-danger' : 'btn-outline-danger'?>" style="width: 150px;font-size: 16px!important;">
                                PENDING (<b><?=$status_count['pending']?></b>)
                            </a>
                        </div>
                        <div class="d-inline-block mr-2">
                            <a href="<?=base_url("app/todo/index/?start_date={$start_date}&end_date={$end_date}&todo_status=assigned")?>"
                               class="btn btn-sm <?=$_GET['todo_status'] == 'assigned' ? 'btn-info' : 'btn-outline-info'?>" style="width: 150px;font-size: 16px!important;">
                                ASSIGNED (<b><?=$status_count['assigned']?></b>)
                            </a>
                        </div>
                        <div class="d-inline-block mr-2">
                            <a href="<?=base_url("app/todo/index/?start_date={$start_date}&end_date={$end_date}&todo_status=completed")?>"
                               class="btn btn-sm <?=$_GET['todo_status'] == 'completed' ? 'btn-success' : 'btn-outline-success'?>" style="width: 150px;font-size: 16px!important;">
                                COMPLETED (<b><?=$status_count['completed']?></b>)
                            </a>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
            <table id="table_no_btn" class="table table-bordered table-striped1">
                <thead>
                <tr>
                    <th>#</th>
                    <th>TODO</th>
                    <th style="width: 110px!important;">STATUS</th>
                    <th style="width: 110px;">ACTION</th>
                </tr>
                </thead>
                <tbody>
                <?php
                if (isset($list_items) && isset($todo_category) && isset($users)) {
                    foreach ($list_items as $key => $item) {
                        ?>
                        <tr>
                            <td><?= $key + 1 ?></td>
                            <td>
                                <span class="badge bg-primary-lighten mb-1">
                                    <b><?= $todo_category[$item['todo_category_id']]?></b>
                                </span>

                                <div class="p-2 pb-3 float-right">
                                    <span class="due_date">
                                        <?= get_due_date($item['due_date'])?>
                                    </span>
                                </div>
                                <div class="clearfix"></div>

                                <h5 class="p-2 text-muted" style="font-size: 16px!important; font-weight: bold; background-color: rgba(250,247,250,0.81); border-radius: 2px;">
                                    <?= $item['title']?>
                                    <div class="d-inline-block float-right">
                                        <?= get_task_priority($item['todo_priority'])?>
                                    </div>
                                </h5>

                                <div class="p-1 pb-4 ">
                                    <button class="btn_hide_show btn btn-sm btn-outline-info float-left" id="show_btn_<?= $item['id']?>" onclick="toggle_items('<?= $item['id']?>')"><i class="bi bi-arrow-down-circle"></i> Show Details</button>
                                    <button class="btn_hide_show btn btn-sm btn-outline-secondary float-left" id="hide_btn_<?= $item['id']?>" onclick="toggle_items('<?= $item['id']?>')" style="display: none"><i class="bi bi-arrow-up-circle"></i> Hide Details</button>
                                </div>

                                <div class="clearfix"></div>

                                <div id="item_<?= $item['id']?>" style="display: none" class="p-1 mt-3">
                                    <div class="p-1">
                                        <?= $item['description']?>
                                    </div>
                                    <table class="table table-bordered" style="width: 500px;">
                                        <tr>
                                            <th>Priority</th>
                                            <td><?= get_todo_priority($item['todo_priority'])?></td>
                                        </tr>
                                        <tr>
                                            <th>Assigned To</th>
                                            <td><?= $users[$item['user_id']]?></td>
                                        </tr>
                                        <tr>
                                            <th>Due Date</th>
                                            <td><?= get_due_date($item['due_date'])?></td>
                                        </tr>
                                    </table>

                                </div>
                                <?php
                                if(!empty($item['remarks'])){
                                    ?>
                                    <div class="p-1 mt-2">
                                        <div class="p-1 pb-3" style="background-color: rgb(250,246,246);border-radius: 2px;">
                                            <span class="badge badge-danger mb-1">Remarks:</span><br>
                                            <?=$item['remarks']?>
                                        </div>
                                    </div>
                                    <?php
                                }
                                ?>
                                <div class="p-1"></div>
                                <div class="float-right text-muted" style="font-size: 12px;">
                                    Created by: <?=strtoupper($users[$item['created_by']])?> | On: <?= DateTime::createFromFormat('Y-m-d H:i:s', $item['created_on'])->format('d-m-Y g:i A')?>
                                </div>

                            </td>
                            <td>
                                <?= get_todo_status($item['todo_status'])?>
                                <div class="pt-1"></div>
                                <?php
                                if (empty($users[$item['user_id']])){
                                    echo "Not Assigned!";
                                }else{
                                    echo get_employee_name(strtoupper($users[$item['user_id']]));
                                }
                                ?>
                            </td>
                            <td>
                                <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=todo/edit'); ?>', '<?php echo get_phrase('update_todo'); ?>')"
                                        class="btn btn-info btn-sm float-right d-block" style="width: 110px;">
                                    <small><i class="fas fa-pencil-alt"></i></small> Edit
                                </button><br><br>
                                <button onclick="confirm_modal('<?=base_url("app/todo/delete/{$item['id']}/");?>')"
                                        class="btn btn-outline-danger btn-sm float-right d-block" style="width: 110px;">
                                    <small><i class="fas fa-trash"></i> </small> Delete
                                </button>
                            </td>
                        </tr>
                        <?php
                    }
                }
                ?>

                </tbody>

            </table>
        </div>
        <!-- /.card-body -->
    </div>
</div>

<script type="text/javascript">
    function toggle_items(item_id) {
        $('#item_'+item_id).toggle('slow');
        $('#show_btn_'+item_id).toggle();
        $('#hide_btn_'+item_id).toggle();
    }
</script>


<style>
    td, th{
        padding: 6px!important;
    }
    .btn_hide_show{
        padding: 2px 4px!important;
        font-size: 13px!important;
        width: 120px;
    }
    .assigned_to{
        border-radius: 10px;
        padding: 2px 10px;
        font-size: 12px;
        font-weight: bold;
        color: rgb(105, 104, 104);
        background-color: rgba(154, 153, 153, 0.16);
    }
    .due_date{
        border-radius: 10px;
        padding: 2px 10px;
        font-size: 12px!important;
        font-weight: bold;
        background-color: rgba(224, 223, 223, 0.16);
    }
    .due_date span{
        font-size: 14px!important;
    }
</style>




