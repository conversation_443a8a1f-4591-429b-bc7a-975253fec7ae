<?php
$todo_category = array_column($this->todo_category_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
$project_types = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');

if (is_employee() || is_trainee()) {
    $where['id'] = get_user_id();
}else{
    $where = [];
}

$users = $this->users_m->get($where, ['id', 'name'])->result_array();
?>
<h2>Under construction, use tasks instead </h2>
<form class="form-horizontal d-none" action="<?=base_url('app/todo/add/')?>" method="post" enctype="multipart/form-data">
	<div class="row" style="margin: 0!important;">
		<div class="form-group col-12 p-0">
			<label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
			<div class="col-sm-12">
				<input type="text" class="form-control" id="title" name="title" value="" placeholder="Title" required>
			</div>
		</div>
        <div class="form-group col-12 p-0">
            <label for="description" class="col-sm-12 col-form-label text-muted">Description </label>
            <div class="col-sm-12">
                <textarea class="form-control ck_editor" id="description" name="description" placeholder="Description" style="resize: none; height: 120px!important;"></textarea>
            </div>
        </div>
        <div class="form-group col-7 p-0">
            <label for="todo_category_id" class="col-sm-12 col-form-label text-muted">Todo Category <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="todo_category_id" name="todo_category_id" required>
                    <option value="">Choose Type</option>
                    <?php
                    foreach ($todo_category as $category_id => $category_title) {
                        echo "<option value='{$category_id}'>{$category_title}</option>";
                    }
                    ?>
                </select>
            </div>
        </div>
        <div class="form-group col-5 p-0">
			<label for="todo_priority" class="col-sm-12 col-form-label text-muted">Todo Priority <span class="text-danger">*</span></label>
			<div class="col-sm-12">
                <select class="form-control select2" id="todo_priority" name="todo_priority" required>
                    <option value="">Choose Priority</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                </select>
			</div>
		</div>
        <div class="form-group col-7 p-0">
            <label for="user_id" class="col-sm-12 col-form-label text-muted">Assign To <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="user_id" name="user_id" required>
                    <option value="">Choose User</option>
                    <?php
                    $user_id = get_user_id();
                    foreach ($users as $user) {
                        if (!has_permission('todo/add')){
                            if ($user_id!=$user['id']){
                                continue;
                            }
                        }
                        echo "<option value='{$user['id']}'>{$user['name']}</option>";
                    }
                    ?>
                </select>
            </div>
        </div>
        <div class="form-group col-5 p-0">
            <label for="due_date" class="col-sm-12 col-form-label text-muted">Due Date <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="due_date" name="due_date" value="<?=date('Y-m-d')?>" placeholder="Due Date" required>
            </div>
        </div>

        <div class="form-group col-12 p-0 mt-2">
            <hr>
        </div>
        <div class="form-group col-12 p-0 mt-2">
            <label for="remark_files" class="col-sm-12 col-form-label text-muted">Remark files </label>
            <div class="col-sm-12">
                <input type="file" class="form-control" id="remark_files" name="remark_files[]" value=""
                       placeholder="Remark files" style="padding: 5px;cursor: pointer" multiple>
            </div>
        </div>

        <div class="p-2"></div>
	</div>

	<div class="col-12 " >
		<button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
			<small><i class="fa fa-check"></i></small> Save
		</button>
	</div>
</form>


<script type="text/javascript">
    $('.ck_editor').each(function(){
        CKEDITOR.replace(this, {
            height: '120px'
        });
    });
	$('.select2').select2();
</script>
