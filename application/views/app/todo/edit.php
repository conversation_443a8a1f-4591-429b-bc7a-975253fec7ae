<?php
$item_id = $param1;
$edit_data = $this->todo_m->get(['id' => $item_id])->row_array();
$todo_category = array_column($this->todo_category_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');

$users = $this->users_m->get(null, ['id', 'name'])->result_array();
?>
<form class="form-horizontal" action="<?=base_url('app/todo/edit/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">
        <div class="form-group col-12 p-0">
            <label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="title" name="title" value="<?=$edit_data['title']?>" placeholder="Title" required>
            </div>
        </div>
        <div class="form-group col-12 p-0">
            <label for="description" class="col-sm-12 col-form-label text-muted">Description </label>
            <div class="col-sm-12">
                <textarea class="form-control ck_editor" id="description" name="description" placeholder="Description" style="resize: none; height: 150px;"><?=$edit_data['description']?></textarea>
            </div>
        </div>

        <div class="form-group col-6 p-0">
            <label for="todo_category_id" class="col-sm-12 col-form-label text-muted">Todo Category <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="todo_category_id" name="todo_category_id" required>
                    <option value="">Choose Type</option>
                    <?php
                    foreach ($todo_category as $category_id => $category_title) {
                        $selected = $category_id == $edit_data['todo_category_id'] ? 'selected' : '';
                        echo "<option value='{$category_id}' {$selected}>{$category_title}</option>";
                    }
                    ?>
                </select>
            </div>
        </div>
        <div class="form-group col-6 p-0">
            <label for="todo_priority" class="col-sm-12 col-form-label text-muted">Todo Priority <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="todo_priority" name="todo_priority" required>
                    <option value="">Choose Priority</option>
                    <option value="high" <?=$edit_data['todo_priority'] == 'high' ? 'selected' : ''?>>High</option>
                    <option value="medium" <?=$edit_data['todo_priority'] == 'medium' ? 'selected' : ''?>>Medium</option>
                    <option value="low" <?=$edit_data['todo_priority'] == 'low' ? 'selected' : ''?>>Low</option>
                </select>
            </div>
        </div>
        <div class="form-group col-7 p-0">
            <label for="user_id" class="col-sm-12 col-form-label text-muted">Assign To <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="user_id" name="user_id" required>
                    <option value="">Choose User</option>
                    <?php
                    $user_id = get_user_id();
                    foreach ($users as $user) {
                        if (!is_super_admin()){
                            if ($user_id!=$user['id']){
                                continue;
                            }
                        }
                        $selected = $user['id'] == $edit_data['user_id'] ? 'selected' : '';
                        echo "<option value='{$user['id']}' {$selected}>{$user['name']}</option>";
                    }
                    ?>
                </select>
            </div>
        </div>
        <div class="form-group col-5 p-0">
            <label for="due_date" class="col-sm-12 col-form-label text-muted">Due Date <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="due_date" name="due_date" value="<?=$edit_data['due_date'] ?? date('Y-m-d')?>" placeholder="Due Date" required>
            </div>
        </div>
        <div class="form-group col-12 p-0 mt-2">
            <hr>
        </div>
        <div class="form-group col-12 p-0 mt-0">
            <label for="remark_files" class="col-sm-12 col-form-label text-muted">Remark files </label>
            <div class="col-sm-12">
                <input type="file" class="form-control" id="remark_files" name="remark_files[]" value="" placeholder="Remark files" style="padding: 5px;cursor: pointer">
            </div>
            <div class="row p-2">
                <?php
                $remark_files = json_decode($edit_data['remark_files'], true);
                if (is_array($remark_files)){
                    foreach($remark_files as $key => $file){
                        ?>
                        <div class="p-2">
                            <a href="<?=base_url($file)?>" class="btn btn-info btn-sm" target="_blank">View File <?=$key + 1?></a>
                        </div>
                        <?php
                    }
                }
                ?>
            </div>
        </div>
    </div>
	<div class="col-12" >
		<button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
			<small><i class="fa fa-check"></i></small> Save
		</button>
	</div>
</form>


<script type="application/javascript">
    $('.ck_editor').each(function(){
        CKEDITOR.replace(this, {
            height: '120px'
        });
    });
	$('.select2').select2();
</script>
