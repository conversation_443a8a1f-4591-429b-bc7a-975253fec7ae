<?php
$item_id = $param1;
$edit_data = $this->tasks_m->get(['id' => $item_id])->row_array();

?>
<form class="form-horizontal" action="<?=base_url('app/tasks/un_assign/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">

        <div class="form-group col-12 p-0">
            <label for="title" class="col-sm-12 col-form-label text-muted">Task</label>
            <div class="col-sm-12">
                <input type="text" class="form-control" placeholder="" value="<?=$edit_data['title']?>" readonly>
            </div>
        </div>
        <div class="form-group col-12 p-0">
            <label for="title" class="col-sm-12 col-form-label text-muted">Task Description</label>
            <div class="col-sm-12">
                <textarea id="title" class="form-control" placeholder="" readonly><?=$edit_data['title']?></textarea>
            </div>
        </div>
        <div class="form-group col-6 p-0">
            <label for="action_status" class="col-sm-12 col-form-label text-muted">Choose Action <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="action_status" name="action_status" required>
                    <option value="pending">Un Assign</option>
                    <option value="on_hold">Move to Hold</option>
                </select>
            </div>
        </div>


    </div>
    <div class="col-12 pt-2" >
        <button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
            <small><i class="fa fa-check"></i></small> Update
        </button>
    </div>
</form>


<script type="application/javascript">
    $('.select2').select2();
</script>
