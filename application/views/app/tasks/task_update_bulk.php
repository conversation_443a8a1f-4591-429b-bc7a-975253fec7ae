<div class="p-1" style="margin-top: -35px!important;">
    <div class="p-1 bg-white">
        <div class="p-0 pb-2 pt-1 row">
            <div class="col-12">
                <button onclick="window.history.back();" class="btn btn-secondary w-100">
                    <i class="bi bi-arrow-left-circle"></i> Go Back
                </button>
            </div>
        </div>

        <div class="p-0 pt-2">
            <form class="form-horizontal" id="form_update_task" action="<?=base_url('app/tasks/task_update_bulk/')?>" method="post" enctype="multipart/form-data"
                    style="background-color: rgba(248,246,246,0.51); border-radius: 5px;border: 2px solid rgba(72,190,108,0.2)">
                <div class="p-1" style="background-color: rgba(72,190,108,0.2); color: #0f6629; font-size: 15px!important;">
                    <b>UPDATE TASK STATUS</b>
                </div>
                <div class="row pt-1" style="margin: 0!important;">
                    <div class="p-2 pb-3 col-12">
                        <div class="bg-danger-lighten p-2 text-center text-danger" style="font-size: 16px">
                            Provide detailed task information, including what you have done.
                        </div>
                    </div>
                    <div class="form-group col-12 p-0">
                        <label for="job_date" class="col-sm-12 col-form-label text-muted"><b>Job Date</b> <span class="text-danger">*</span></label>
                        <div class="col-sm-12">
                            <input type="date" class="form-control " id="job_date" name="job_date" value="<?=date('Y-m-d')?>" required>
                        </div>
                    </div>
                    <div class="form-group col-12 p-0">
                        <label for="remarks" class="col-sm-12 col-form-label text-muted"><strong>Describe Your Work</strong> <span class="text-danger">*</span></label>
                        <div class="col-sm-12">
                            <textarea class="form-control" id="remarks" name="remarks" placeholder="Enter Remarks" style="height: 280px;"></textarea>
                        </div>
                    </div>

                </div>
                <div class="col-12 pb-2" >
                    <button type="submit" name="add" value="Save" class="btn btn-success btn-mini w-100">
                        <small><i class="fa fa-check"></i></small> Update
                    </button>
                </div>
            </form>
        </div>


        <script type="text/javascript">
            $('.select2').select2();
        </script>

        <script type="text/javascript">
            document.querySelector('.form-horizontal').addEventListener('submit', function(e) {
                var remarks = tinymce.get('remarks').getContent();
                // Check if Remarks is at least 20 characters
                if (remarks.trim().length < 25 || !check_content_words(remarks)) {
                    message_error("Please provide detailed task information!");
                    e.preventDefault(); // Prevent form submission
                }

            });
            
            // check content words
            function check_content_words(str) {
                let tempDiv = document.createElement('div');
                tempDiv.innerHTML = str;
                let text = tempDiv.textContent || tempDiv.innerText || "";
            
                const words = text.match(/[a-zA-Z]+/g);
                return words && words.length >= 4;
            }
        </script>
    </div>
</div>

<style>
    label {
        font-size: 12px!important;
        color: #5555 !important;
        text-transform: uppercase!important;
        margin-bottom: -5px!important;
    }
    input, textarea, select{
        font-size: 15px!important;
        font-weight: 600!important;
    }
</style>