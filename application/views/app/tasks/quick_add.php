<?php
    $project_id = $param1;
    $project = $this->db->get_where('projects', ['id' => $project_id])->row();
?>
<form class="form-horizontal" action="<?=base_url('app/tasks/add/')?>" method="post" enctype="multipart/form-data">
	<div class="row" style="margin: 0!important;">
		<div class="form-group col-12 p-0">
			<label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
			<div class="col-sm-12">
				<input type="text" class="form-control" id="title" name="title" value="[<?=$project->title?>] - " style="font-size:18px;padding:15px!important;" 
				placeholder="Project name + Short description of the job" required autofocus>
			</div>
		</div>
        <div class="form-group col-12 p-0">
            <label for="description" class="col-sm-12 col-form-label text-muted">Description <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <textarea class="form-control" id="description" name="description" placeholder="Description" style="resize: none; height: 200px;font-size:17px;"></textarea>
            </div>
        </div>
        <input type="hidden" name="project_id" value="<?=$project_id?>">


        <div class="p-2"></div>
	</div>
    


	<div class="col-12 " >
		<button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
			<small><i class="fa fa-check"></i></small> Save
		</button>
	</div>
</form>


<script type="text/javascript">
	$('.select2').select2();
	$(document).ready(function() {
        var $input = $('input[name="title"]');
        $input.focus();
    
        // Move the cursor to the end of the text
        var val = $input.val();
        $input.val('').val(val);
    });
</script>
