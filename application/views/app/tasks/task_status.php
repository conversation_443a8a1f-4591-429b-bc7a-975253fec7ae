<?php
$item_id = $param1;
$task = $this->tasks_m->get(['id' => $item_id], ['task_status'])->row();
?>
<form class="form-horizontal" action="<?=base_url('app/tasks/task_status/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">


        <div class="form-group col-12 p-0">
            <label for="task_type" class="col-sm-12 col-form-label text-muted">Task Status <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="task_status" name="task_status" required>
                    <option value="">Choose Status</option>
                    <option value="pending" <?=$task->task_status == 'pending' ? 'selected' : ''?>>Pending</option>
                    <option value="on_hold" <?=$task->task_status == 'on_hold' ? 'selected' : ''?>>On Hold</option>
                    <option value="completed" <?=$task->task_status == 'completed' ? 'selected' : ''?>>Completed</option>
                </select>
            </div>
        </div>

    </div>
    <div class="col-12 pt-2" >
        <button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" >
            <small><i class="fa fa-check"></i></small> Update Status
        </button>
    </div>
</form>
<script type="application/javascript">
    $('.ck_editor').each(function(){
        CKEDITOR.replace(this);
    });
    $('.select2').select2();
</script>
