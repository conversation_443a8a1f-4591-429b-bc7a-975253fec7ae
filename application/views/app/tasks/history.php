<?php
$item_id = $param1;
$task_details = $this->tasks_m->get(['id' => $item_id])->row_array();
$task_history = $this->task_assign_m->get(['task_id' => $item_id])->result_array();
$users = array_column($this->users_m->get(['role_id!=' => 1], ['id', 'name'])->result_array(), 'name', 'id');
?>

<div class="p-1">
    <div class="shadow-pro">
        <table class="table table-bordered table-striped">
            <thead>
                <th>Name</th>
                <th>Remarks</th>
                <th>Duration</th>
                <th>Due Date</th>
            </thead>
            <tbody>
                <?php
                    foreach ($task_history as $history) {
                        ?>
                        <tr>
                            <td>
                                <?=$users[$history['user_id']]?><br>
                                <small>
                                    <?= DateTime::createFromFormat('Y-m-d H:i:s', $history['created_on'])->format('d-m-Y, g:i A')?>
                                </small>
                            </td>
                            <td><?=$history['remarks']?></td>
                            <td><?=$history['time_taken']?></td>
                            <td><?= DateTime::createFromFormat('Y-m-d', $history['due_date'])->format('d-m-Y')?></td>
                        </tr>
                        <?php
                    }
                    $total_duration = add_duration(array_column($task_history, 'time_taken'))
                ?>
            <tr>
                <th colspan="2">Total</th>
                <th><?=$total_duration?></th>
                <th>-</th>
            </tr>
            </tbody>
        </table>
    </div>
</div>
