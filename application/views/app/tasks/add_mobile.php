<div class="p-1" style="margin-top: -35px!important;">
    <div class="p-1 bg-white">
        <div class="p-2 pb-0 pt-1">
            <button onclick="window.history.back();" class="btn btn-secondary w-100">
                <i class="bi bi-arrow-left-circle"></i> Go Back
            </button>
        </div>
        <?php
        $projects = $this->projects_m->get(null, ['id', 'title', 'project_type'], ['key' => 'title', 'direction' => 'ASC'])->result_array();
        $project_types = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $assign_users = $this->users_m->get_task_assign_users();
        ?>
        <form class="form-horizontal" action="<?=base_url('app/tasks/add/')?>" method="post" enctype="multipart/form-data">
            <div class="p-1 pl-2 pr-2">
                <div class="alert bg-danger-lighten text-danger p-2 pb-1" role="alert" style="font-weight:500; font-size: 12px">
                    <b>IMPORTANT:</b> <br>Kindly start with the project name followed by a short description of the task.
                </div>
            </div>
            <div class="row" style="margin: 0!important;">
                <div class="form-group col-12 p-0" style="margin-top: -10px">
                    <label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
                    <div class="col-sm-12">
                        <input type="text" class="form-control" id="title" name="title" value="" placeholder="Project name + Short description of the job" required style="padding: 15px">
                    </div>
                </div>
                <div class="form-group col-12 p-0" style="margin-top: -10px">
                    <label for="description" class="col-sm-12 col-form-label text-muted">Description <span class="text-danger">*</span></label>
                    <div class="col-sm-12">
                        <textarea class="form-control ck_editor" id="description" name="description" placeholder="Description" style="resize: none; height: 200px;font-size:18px;"></textarea>
                    </div>
                </div>
                <div class="col-12 m-0 p-3">
                    <div class="row p-1 pt-2 pb-2" style="background-color: rgba(250,248,248,0.99); border-radius: 5px;">
                        <div class="form-group col-12 p-0" style="margin-top: -10px">
                            <label for="project_id" class="col-sm-12 col-form-label text-muted">Project <span class="text-danger">*</span></label>
                            <div class="col-sm-12">
                                <select class="form-control select21" id="project_id" name="project_id" required>
                                    <option value="">Choose Project</option>
                                    <?php
                                    foreach ($projects as $key => $project) {
                                        echo "<option value='{$project['id']}'>{$project['title']} - [{$project_types[$project['project_type']]}]</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>

                        <div class="form-group col-12 p-0">
                            <label for="task_type" class="col-sm-12 col-form-label text-muted">Task Type <span class="text-danger">*</span></label>
                            <div class="col-sm-12">
                                <select class="form-control form-control-sm" id="task_type" name="task_type" required>
                                    <option value="">Choose Type</option>
                                    <option value="new">New/ Requirement</option>
                                    <option value="bug">Bug</option>
                                    <option value="critical_bug">Critical Bug</option>
                                    <option value="queries">Queries</option>
                                    <option value="meeting">Meeting</option>
                                </select>
                            </div>
                        </div>


                        <div class="form-group col-12 p-0">
                            <label for="task_priority" class="col-sm-12 col-form-label text-muted">Task Priority <span class="text-danger">*</span></label>
                            <div class="col-sm-12">
                                <select class="form-control form-control-sm" id="task_priority" name="task_priority" required>
                                    <option value="">Choose Priority</option>
                                    <option value="high">High</option>
                                    <option value="medium">Medium</option>
                                    <option value="low">Low</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group col-12 p-0">
                            <label for="requested_by" class="col-sm-12 col-form-label text-muted">Requested By </label>
                            <div class="col-sm-12">
                                <input type="text" class="form-control" id="requested_by" name="requested_by" value="" placeholder="Phone/ Name/ Reference" >
                            </div>
                        </div>
                        <div class="form-group col-12 p-0 mt-0">
                            <label for="remark_files" class="col-sm-12 col-form-label text-muted">Remark files </label>
                            <div class="col-sm-12">
                                <input type="file" class="form-control" id="remark_files" name="remark_files[]" value=""
                                       placeholder="Remark files" style="padding: 5px;cursor: pointer" multiple>
                            </div>
                        </div>
                        <div class="form-group col-6 p-0">
                            <label for="due_date" class="col-sm-12 col-form-label text-muted">Due Date <span class="text-danger">*</span> </label>
                            <div class="col-sm-12">
                                <input type="date" class="form-control" id="due_date" name="due_date" value="" placeholder="Due Date" required>
                            </div>
                        </div>
                        <div class="form-group col-6 p-0">
                            <label for="required_time" class="col-sm-12 col-form-label text-muted">Required Time (Hours)</label>
                            <div class="col-sm-12">
                                <input type="text" class="form-control duration_input" onkeyup="duration_input(this)" id="required_time" name="required_time" value="00:00" placeholder="00:00">
                            </div>
                        </div>
                    </div>

                </div>
                
                <?php
                    if(is_tester()){
                        ?>
                        <div class="form-group col-6 p-0">
                            <div class="col-sm-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="mandatory_check" name="mandatory_check" style="width:20px;height:20px" required>
                                    <label class="form-check-label text-muted ps-2" for="mandatory_check">
                                        <span class="text-danger">*</span> Is this task updated in testers WhatsApp group?
                                    </label>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                ?>

                <div class="form-group col-12 p-0">
                    <label for="assign_user_id" class="col-sm-12 col-form-label text-muted">Choose User <span class="text-danger">*</span></label>
                    <div class="col-sm-12">
                        <select class="form-control form-control-sm" id="assign_user_id" name="assign_user_id" required>
                            <option value="">Choose User</option>
                            <?php
                                if (has_permission('tasks/index')){
                                    echo "<option value=\"0\">UN ASSIGNED</option>";
                                }
                            ?>
                            <?php
                            foreach ($assign_users as $assign_user) {
                                $selected = get_user_id() == $assign_user['id'] ? 'selected' : '';
                                echo "<option value='{$assign_user['id']}' {$selected}>{$assign_user['name']}</option>";
                            }
                            ?>
                        </select>
                    </div>
                </div>


            </div>
            <?php
            if (!has_permission('tasks/index')){
                ?>
                <div class="col-12">
                    <div class="alert bg-danger-lighten" style="font-size: 12px">
                        Once task added, only admin can delete or edit the task.
                    </div>
                </div>
                <?php
            }
            ?>


            <div class="col-12 pb-2" >
                <button type="submit" name="add" value="Save" class="btn btn-success w-100" >
                    <small><i class="fa fa-check"></i></small> Save
                </button>
            </div>
        </form>


        <script type="text/javascript">
            $('.ck_editor').each(function(){
                CKEDITOR.replace(this);
            });
            $('.select2').select2();
        </script>
        <script type="text/javascript">
            document.querySelector('.form-horizontal').addEventListener('submit', function(e) {
                var remarks = CKEDITOR.instances.description.getData();

                var title = $('#title').val();

                if (title.trim().length < 20) {
                    message_error("Task Title must be at least 20 characters long");
                    e.preventDefault(); // Prevent form submission
                }

                // Check if Remarks is at least 20 characters
                if (remarks.trim().length < 20) {
                    message_error("Description must be at least 20 characters long");
                    e.preventDefault(); // Prevent form submission
                }

            });
        </script>
    </div>
</div>

<style>
    label {
        font-size: 12px!important;
        color: #5555 !important;
        text-transform: uppercase!important;
        margin-bottom: -5px!important;
    }
    input, textarea, select{
        font-size: 16px!important;
        font-weight: 600!important;
        /*padding: 20px!important;*/
    }
</style>