<?php
$item_id = $param1;
$edit_data = $this->tasks_m->get(['id' => $item_id])->row_array();
$projects = $this->projects_m->get(null, ['id', 'title', 'project_type'])->result_array();
$project_types = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
?>
<form class="form-horizontal" action="" method="post" enctype="multipart/form-data" style="margin-top: 4px">
    <div class="row" style="margin: 0!important;padding: 0!important;">
        <div class="form-group col-12 p-0">
            <div class="row p-0 bg-primary m-0" style="margin-top:-4px!important">
                <div class="col-sm-8" style="padding-left: 14px;">
                    <h6 style="font-weight: 600; color: #f0f6ff; padding: 5px;"><?=$edit_data['title']?></h6>
                </div>
                <div class="col-sm-4">
                    <div class="pb-0" style="font-size: 14px; margin-bottom: 0px; margin-top: 2px;">
                        <div id="editStatus" style="float: right">
                        </div>
                    </div><br>
                    <div style="clear: both!important;"></div>
                </div>
            </div>
            <div class="col-sm-12">
                <textarea class="form-control ck_editor" onchange="update_task_content(this.value)" id="edit_content" name="description" placeholder="Description" style="resize: none; height: 200px;font-size:17px!important;"><?=$edit_data['description']?></textarea>

            </div>
        </div>
    </div>
</form>


<script type="application/javascript">

    tinymce.init({
        selector: '#edit_content',
        height: 500,
        content_css: '<?=base_url('assets/dist/css/ck_editor.css')?>',
        plugins: 'lists advlist pagebreak fullscreen', 
        toolbar: 'bold italic underline | styleselect | bullist numlist | blocks | print | hr | pagebreak | fullscreen | removeformat',
        menubar: false,
        setup: function(editor) {
            editor.on('keyup', function(e) {
                isContentEdited = true;
                showEditStatus();
            });
        },
        fontsize_formats: '18pt'
    });

    $('.select2').select2();

    var isContentEdited = false;

    // Function to show edit status using jQuery
    function showEditStatus() {
        if (isContentEdited) {
            $('#editStatus').html(
                '<span class="p-1" style="border-radius: 2px;color: #b96e0c!important;background-color: #f4efd5">' +
                'Content has been edited!' +
                '</span>'
            );
        }
    }

    // Function to auto-save content using jQuery
    function autoSaveContent() {
        if (isContentEdited) {
            var content = tinymce.get('edit_content').getContent();
            save_content_ajax(content);
            isContentEdited = false;
        }
    }

    // Auto-save when the window is closed using jQuery
    $(window).on('beforeunload', autoSaveContent);

    // Auto-save when a specific modal is closed using jQuery
    $('#extra-large-modal').on('hidden.bs.modal', function() {
        autoSaveContent();
    });

    function update_task_content(content) {
        alert(content);
    }

    function save_content_ajax(content){
        $.ajax({
            url: '<?=base_url('app/tasks/ajax_save_content/')?>', // Replace with your server URL
            type: 'POST',
            data: {
                task_id: <?=$item_id?>,
                content: content
            },
            success: function(response) {
                if (response){
                    // Handle success
                    $('#editStatus').html(
                        '<span class="p-1" style="border-radius: 2px;color: #07734a!important;background-color: #d7fadf">' +
                        'Content has been saved automatically!' +
                        '</span>'
                    );
                }else {
                    $('#editStatus').html(
                        '<span class="p-1" style="border-radius: 2px;color: #b90c0c!important;background-color: #fadbd7">' +
                        'Edit has not saved!' +
                        '</span>'
                    );
                }

            },
            error: function(xhr, status, error) {
                // Handle errors
                $('#editStatus').html(
                    '<span class="p-1" style="border-radius: 2px;color: #b90c0c!important;background-color: #fadbd7">' +
                    'Edit has not saved!' +
                    '</span>'
                );
            }
        });
    }

    setInterval(autoSaveContent, 6000);
</script>


<style>
    .cke_editable{
        font-size:15px!important;
        height:300px!important;
    }
</style>
