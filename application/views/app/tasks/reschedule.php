<?php
$item_id = $param1;
$edit_data = $this->tasks_m->get(['id' => $item_id])->row_array();
$users = $this->users_m->get(['role_id!=' => 1])->result_array();
?>
<form class="form-horizontal" action="<?=base_url('app/tasks/assign/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">

        <div class="form-group col-8 p-0">
            <label for="user_id" class="col-sm-12 col-form-label text-muted">Choose User <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="user_id" name="user_id" required>
                    <option value="">Choose User</option>
                    <?php
                    foreach ($users as $user){
                        $selected = $user['id'] == $edit_data['user_id'] ? 'selected' : '';
                        echo "<option value='{$user['id']}' {$selected}>{$user['name']}</option>";
                    }
                    ?>
                </select>
            </div>
        </div>
        <div class="form-group col-6 p-0">
            <label for="due_date" class="col-sm-12 col-form-label text-muted">Due Date <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="date" class="form-control" value="<?=$edit_data['due_date']?>" id="due_date" name="due_date" required>
            </div>
        </div>
        <div class="form-group col-6 p-0">
            <label for="required_time" class="col-sm-12 col-form-label text-muted">Required Time (Hours) <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" onkeyup="duration_input(this)" value="<?=time_to_duration($edit_data['required_time'])?>" class="form-control" id="required_time" name="required_time" placeholder="Required Time" required>
            </div>
        </div>
        <div class="form-group col-6 p-0">
            <label for="task_priority" class="col-sm-12 col-form-label text-muted">Task Priority <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="task_priority" name="task_priority" required>
                    <option value="">Choose Priority</option>
                    <option value="high" <?=$edit_data['task_priority'] == 'high' ? 'selected' : ''?>>High</option>
                    <option value="medium" <?=$edit_data['task_priority'] == 'medium' ? 'selected' : ''?>>Medium</option>
                    <option value="low" <?=$edit_data['task_priority'] == 'low' ? 'selected' : ''?>>Low</option>
                </select>
            </div>
        </div>
        <div class="form-group col-6 p-0">
            <label for="task_type" class="col-sm-12 col-form-label text-muted">Task Type <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="task_type" name="task_type" required>
                    <option value="">Choose Type</option>
                    <option value="new" <?=$edit_data['task_type'] == 'new' ? 'selected' : ''?>>New/ Requirement</option>
                    <option value="bug" <?=$edit_data['task_type'] == 'bug' ? 'selected' : ''?>>Bug</option>
                    <option value="support" <?=$edit_data['task_type'] == 'support' ? 'selected' : ''?>>Support</option>
                    <option value="quick_support" <?=$edit_data['task_type'] == 'quick_support' ? 'selected' : ''?>>Quick Support</option>
                    <option value="meeting" <?=$edit_data['task_type'] == 'meeting' ? 'selected' : ''?>>Meeting</option>
                </select>
            </div>
        </div>

    </div>
    <div class="col-12 pt-2" >
        <button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
            <small><i class="fa fa-check"></i></small> Assign Task
        </button>
    </div>
</form>


<script type="application/javascript">
    $('.select2').select2();
</script>
