<?php
$item_id = $param1;
$task = $this->tasks_m->get(['id' => $item_id], ['tester_remarks'])->row();
?>
<form class="form-horizontal" action="<?=base_url('app/tasks/test_task/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">


        <div class="form-group col-12 p-0">
            <label for="task_type" class="col-sm-12 col-form-label text-muted">Testing Status <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="task_status" name="task_status" required>
                    <option value="">Choose Status</option>
                    <option value="assigned">Testing Failed</option>
                    <option value="completed">Testing Okay</option>
                </select>
            </div>
        </div>
        <div class="form-group col-12 p-0">
            <label for="remarks" class="col-sm-12 col-form-label text-muted">Remarks <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <textarea class="form-control ck_editor" id="remarks" name="remarks" placeholder="Remarks"><?=$task->tester_remarks?></textarea>
            </div>
        </div>
        <div class="form-group col-12 p-0">
            <label for="job_date" class="col-sm-12 col-form-label text-muted"><b>Job Date</b> <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="date" class="form-control " id="job_date" name="job_date" value="<?=date('Y-m-d')?>" required>
            </div>
        </div>
        <div class="form-group col-4">
            <label for="start_time" class="col-sm-12 col-form-label text-muted"><b>Start Time</b> <span class="text-danger">*</span></label>
            <input type="time" class="form-control" name="start_time" id="start_time" onchange="get_time_taken()" required>
        </div>
        <div class="form-group col-4">
            <label for="end_time" class="col-sm-12 col-form-label text-muted"><b>End Time</b> <span class="text-danger">*</span></label>
            <input type="time" class="form-control" name="end_time" id="end_time" onchange="get_time_taken()" required>
        </div>
        <div class="form-group col-4">
            <label for="time_taken" class="col-sm-12 col-form-label text-muted"><b>Time Taken</b> <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control " id="time_taken" name="time_taken" onchange="duration_input(this)" pattern="^(0[0-9]|1[0-9]|2[0-3]):([0-5][0-9])$" value="00:00" placeholder="00:00" required readonly>
            </div>
        </div>

    </div>
    <div class="col-12 pt-2" >
        <button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" >
            <small><i class="fa fa-check"></i></small> Update Status
        </button>
    </div>
</form>


<script type="application/javascript">
    $('.ck_editor').each(function(){
        CKEDITOR.replace(this);
    });
    $('.select2').select2();

        document.querySelector('.form-horizontal').addEventListener('submit', function(e) {
        var timeTaken = document.getElementById('time_taken').value;
        // var remarks = CKEDITOR.instances.remarks.getData();
        var remarks = tinymce.get('remarks').getContent();

        // Check if Time Taken is "00:00"
        if (timeTaken === "00:00") {
        message_error("Time Taken cannot be 00:00");
        e.preventDefault(); // Prevent form submission
    }

        // Check if Remarks is at least 20 characters
        if (remarks.trim().length < 10) {
        message_error("Remarks must be at least 10 characters long");
        e.preventDefault(); // Prevent form submission
    }

    });

        function get_time_taken(){
        var start_time = $('#start_time').val();
        var end_time = $('#end_time').val();

        if (start_time === '' || end_time === '') {
        $('#time_taken').val('00:00');
        return;
    }

        // Convert time to minutes
        var startTimeMinutes = convertTimeToMinutes(start_time);
        var endTimeMinutes = convertTimeToMinutes(end_time);

        // Ensure start time is less than end time
        if (startTimeMinutes >= endTimeMinutes) {
        $('#time_taken').val('00:00');
        return;
    }

        // Calculate duration
        var diffMinutes = endTimeMinutes - startTimeMinutes;

        if (!(diffMinutes > 0)){
        $('#time_taken').val('00:00');
        return;
    }

        // Format duration
        var duration = formatDuration(diffMinutes);

        $('#time_taken').val(duration);
    }
        function convertTimeToMinutes(time) {
        var [hours, minutes] = time.split(':').map(num => parseInt(num, 10));
        return hours * 60 + minutes;
    }

        function formatDuration(minutes) {
        var hours = Math.floor(minutes / 60);
        var mins = minutes % 60;
        // Leading zero for minutes
        hours = hours < 10 ? '0' + hours : hours;
        mins = mins < 10 ? '0' + mins : mins;
        return `${hours}:${mins}`;
    }

    
</script>
