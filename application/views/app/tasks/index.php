<div class="container-fluid mobile_hide1 pt-0 mt-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add', '<?= get_phrase('add_task'); ?>')" style="width: 160px;"
                class="btn btn-primary btn-mini float-right">
            <small><i class="fas fa-plus"></i></small> Add <?= $page_title ?? '' ?>
        </button>
    </div>
    <div class="mt-3" style="margin:1px;">

        <!-- /.card-header -->
        <div class="d-block">
            <?php
            if (isset($start_date) && isset($end_date) && isset($status_count) && isset($project_id)){
                ?>
                <div class="card shadow-pro">
                    <div class="card-body p-2">
                        <form action="" method="get">
                            <div class="row" style="max-width: 1200px;">
                                <div class="col-6 col-lg-2 form-group p-2 mb-0">
                                    <input type="date" value="<?=$start_date?>" class="form-control mt-0" id="start_date" name="start_date" required>
                                </div>
                                <div class="col-6 col-lg-2 form-group p-2 mb-0">
                                    <input type="date" value="<?=$end_date?>" class="form-control mt-0" id="end_date" name="end_date" required>
                                </div>
                                <div class="col-12 col-lg-2 form-group p-2 mb-0">
                                    <select class="form-control select2 mt-0" id="user_id" name="user_id" >
                                        <option value="" <?=$_GET['user_id'] == '' ? 'selected' : ''?>>All Users</option>
                                        <?php
                                        if (isset($users)){
                                            foreach ($users as $user_id => $user_name){
                                                ?>
                                                <option value="<?=$user_id?>" <?=$_GET['user_id'] == $user_id ? 'selected' : ''?>><?=$user_name?></option>
                                                <?php
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-12 col-lg-2 form-group p-2 mb-0">
                                    <select class="form-control select2 mt-0" id="task_status" name="task_status" required>
                                        <option value="all" <?=$_GET['task_status'] == 'all' ? 'selected' : ''?>>All</option>
                                        <option value="pending" <?=$_GET['task_status'] == 'pending' ? 'selected' : ''?>>Pending</option>
                                        <option value="assigned" <?=$_GET['task_status'] == 'assigned' ? 'selected' : ''?>>Assigned</option>
                                        <option value="testing" <?=$_GET['task_status'] == 'testing' ? 'selected' : ''?>>Testing</option>
                                        <option value="on_hold" <?=$_GET['task_status'] == 'on_hold' ? 'selected' : ''?>>On Hold</option>
                                        <option value="completed" <?=$_GET['task_status'] == 'completed' ? 'selected' : ''?>>Completed</option>
                                    </select>
                                </div>
                                <div class="col-12 col-lg-3 form-group p-2 mb-0">
                                    <select class="form-control select2 mt-0" id="project_id" name="project_id" required>
                                        <option value="all" <?=$_GET['project_id'] == '0' ? 'selected' : ''?>>All Projects</option>
                                        <?php
                                        if (isset($projects_list) && isset($project_type)){
                                            foreach ($projects_list as  $project){
                                                $selected = $project['id'] == $_GET['project_id'] ? 'selected' : '';
                                                echo "<option value='{$project['id']}' {$selected}>{$project['title']} - [{$project_type[$project['project_type']]}]</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-12 col-lg-1 form-group p-2 mb-0">
                                    <button type="submit" class="btn btn-secondary btn-block mt-0">
                                        <small><i class="bi bi-funnel-fill"></i></small> Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <?php
            }
            ?>
            <div class="p-0 row" style="margin-bottom:10px">
                <div class="col-12 col-lg-8 ">
                    <div class=" mb-4 p-2 shadow_pro" style="background-color: #ffffff">
                        <div class="row p-0 text-center m-0">
                            <div class="col-4 col-lg-2 p-1">
                                <div style="background-color: #ffffff">
                                    <a href="<?=base_url("app/tasks/index/?start_date={$start_date}&end_date={$end_date}&task_status=all&project_id={$project_id}&user_id={$_GET['user_id']}")?>"
                                       class="btn btn-sm <?=$_GET['task_status'] == 'all' || $_GET['task_status'] == '' ? 'btn-primary' : 'btn-outline-primary'?> w-100" style="font-size: 14px!important;padding: 6px;">
                                        <div style="font-size: 28px;font-weight: bold">
                                            <?=$status_count['all']?><small><sup>*</sup></small>
                                        </div>
                                        ALL
                                    </a>
                                </div>
                            </div>
                            <div class="col-4 col-lg-2 p-1">
                                <div style="background-color: #ffffff">
                                    <a href="<?=base_url("app/tasks/index/?start_date={$start_date}&end_date={$end_date}&task_status=pending&project_id={$project_id}&user_id={$_GET['user_id']}")?>"
                                       class="btn btn-sm <?=$_GET['task_status'] == 'pending' ? 'btn-danger' : 'btn-outline-danger'?> w-100" style="font-size: 14px!important;padding: 6px;">
                                        <div style="font-size: 28px;font-weight: bold">
                                            <?=$status_count['pending']?><small><sup>*</sup></small>
                                        </div>
                                        PENDING
                                    </a>
                                </div>
                            </div>
                            <div class="col-4 col-lg-2 p-1">
                                <div style="background-color: #ffffff">
                                    <a href="<?=base_url("app/tasks/index/?start_date={$start_date}&end_date={$end_date}&task_status=assigned&project_id={$project_id}&user_id={$_GET['user_id']}")?>"
                                       class="btn btn-sm <?=$_GET['task_status'] == 'assigned' ? 'btn-info' : 'btn-outline-info'?> w-100" style="width: 125px; font-size: 14px!important;padding: 6px;">
                                        <div style="font-size: 28px;font-weight: bold">
                                            <?=$status_count['assigned']?>
                                        </div>
                                        ASSIGNED
                                    </a>
                                </div>
                            </div>
                            <div class="col-4 col-lg-2 p-1">
                                <div style="background-color: #ffffff">
                                    <a href="<?=base_url("app/tasks/index/?start_date={$start_date}&end_date={$end_date}&task_status=testing&project_id={$project_id}&user_id={$_GET['user_id']}")?>"
                                       class="btn btn-sm <?=$_GET['task_status'] == 'testing' ? 'btn-dark' : 'btn-outline-dark'?> w-100" style="width: 125px; font-size: 14px!important;padding: 6px;">
                                        <div style="font-size: 28px;font-weight: bold">
                                            <?=$status_count['testing']?>
                                        </div>
                                        TESTING
                                    </a>
                                </div>
                            </div>
                            <div class="col-4 col-lg-2 p-1">
                                <div style="background-color: #ffffff">
                                    <a href="<?=base_url("app/tasks/index/?start_date={$start_date}&end_date={$end_date}&task_status=on_hold&project_id={$project_id}&user_id={$_GET['user_id']}")?>"
                                       class="btn btn-sm <?=$_GET['task_status'] == 'on_hold' ? 'btn-warning' : 'btn-outline-warning'?> w-100" style="width: 125px; font-size: 14px!important;padding: 6px;">
                                        <div style="font-size: 28px;font-weight: bold">
                                            <?=$status_count['on_hold']?><small><sup>*</sup></small>
                                        </div>
                                        ON HOLD
                                    </a>
                                </div>
                            </div>
                            <div class="col-4 col-lg-2 p-1">
                                <div style="background-color: #ffffff">
                                    <a href="<?=base_url("app/tasks/index/?start_date={$start_date}&end_date={$end_date}&task_status=completed&project_id={$project_id}&user_id={$_GET['user_id']}")?>"
                                       class="btn btn-sm <?=$_GET['task_status'] == 'completed' ? 'btn-success' : 'btn-outline-success'?> w-100" style="width: 125px; font-size: 14px!important;padding: 6px;">
                                        <div style="font-size: 28px;font-weight: bold">
                                            <?=$status_count['completed']?>
                                        </div>
                                        COMPLETED
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="p-1 m-0 pt-2 row">
                            <div class="col-9">
                                <input type="text" class="form-control form-control-lg shadow-pro" id="ta_search_list" placeholder="Search Tasks.." style="padding:25px;font-size:18px">
                            </div>
                            <div class="col-3">
                                <div class="input-group mb-3">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" id="basic-addon1" style="font-size: 20px">TRG -</span>
                                    </div>
                                    <input type="number" class="form-control form-control-lg shadow-pro text-center" id="search_by_job_id" placeholder="JOB ID" style="padding:25px 10px;font-size:20px">
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="">
                        <div class="row p-0">
                            <?php
                            if (isset($list_items) && isset($clients) && isset($projects) && isset($users)) {
                                foreach ($list_items as $key => $item) {
                                    ?>
                                    <div class="col-12 p-1 pl-2 pr-2 ta_search_item pb-4">
                                        <div class="<?=get_task_color($item['task_status'])?> p-0 pl-2 shadow-pro">
                                            <div class="bg-white p-2 pb-1">
                                                <div class="row pb-3">
                                                    <div class="col-3">
                                                        <a href="<?=base_url("app/tasks/index/?start_date=2023-01-01&end_date=".date('Y-m-d')."&project_id={$item['project_id']}")?>">
                                                            <?=get_project_title($projects[$item['project_id']])?>
                                                        </a>
                                                    </div>
                                                    <div class="col-6 text-center">
                                                        <div class="">
                                                            <span class="due_date">
                                                                <?= get_due_date($item['due_date'])?>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="mb-1 job_id_dashboard">
                                                            TRG-<b><?=$item['id']?></b>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div>
                                                    <h5 class="p-2 text-muted" style="font-size: 16px!important; font-weight: bold; background-color: rgba(250,247,250,0.81); border-radius: 2px;">
                                                        <?= $item['title']?>
                                                        <div class="d-inline-block float-right">
                                                            <?= get_task_type($item['task_type'])?>
                                                        </div>
                                                    </h5>

                                                    <div class="p-1 pb-4 ">
                                                        <button class="btn_hide_show btn btn-sm btn-outline-info float-left" id="show_btn_<?= $item['id']?>" onclick="toggle_items('<?= $item['id']?>')"><i class="bi bi-arrow-down-circle"></i> Show Details</button>
                                                        <button class="btn_hide_show btn btn-sm btn-outline-secondary float-left" id="hide_btn_<?= $item['id']?>" onclick="toggle_items('<?= $item['id']?>')" style="display: none"><i class="bi bi-arrow-up-circle"></i> Hide Details</button>
                                                        <?php
                                                        if (is_technical_support()){
                                                        
                                                            if($item['task_status'] != 'pending'){
                                                                ?>
                                                                <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/test_task'); ?>', '<?php echo get_phrase('test_task'); ?>')"
                                                                    class="ml-2 btn_hide_show btn btn-sm btn-secondary float-left" style="width: 120px;">
                                                                    <small><i class="bi bi-person-check"></i></small> Test Task
                                                                </button>
                                                                <?php
                                                            }
                                                        
                                                            ?>
                                                        
                                                            
                                                            <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/task_status'); ?>', '<?php echo get_phrase('change_status'); ?>')"
                                                                    class="ml-2 btn_hide_show btn btn-sm btn-outline-secondary float-left" style="width: 120px;">
                                                                <small><i class="bi bi-person-check"></i></small> Change Status
                                                            </button>
                                                            
                                                            <?php
                                                        }
                                                        ?>
                                                        <?php
                                                        if ((is_project_manager())){
                                                            ?>
                                                            <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/test_task'); ?>', '<?php echo get_phrase('test_task'); ?>')"
                                                                    class="ml-2 btn_hide_show btn btn-sm btn-secondary float-left" style="width: 120px;">
                                                                <small><i class="bi bi-person-check"></i></small> Test Task
                                                            </button>
                                                            <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/task_status'); ?>', '<?php echo get_phrase('change_status'); ?>')"
                                                                    class="ml-2 btn_hide_show btn btn-sm btn-outline-secondary float-left" style="width: 120px;">
                                                                <small><i class="bi bi-person-check"></i></small> Change Status
                                                            </button>
                                                            
                                                            <?php
                                                        }
                                                        ?>
                                                    </div>

                                                    <div class="clearfix"></div>
                                                    <div id="item_<?= $item['id']?>" style="display: none" class="p-1 mt-3">
                                                        <div class="p-1">
                                                            <?= $item['description']?>
                                                        </div>
                                                        <?php
                                                        if(!empty($item['tester_remarks'])){
                                                            ?>
                                                            <div class="p-2 mb-2" style="background-color: #f8f5f5; border-radius: 2px">
                                                                <b>Tester Remarks:</b> <small>[By <?=$users[$item['tested_by']]?>]</small><hr>
                                                                <?= $item['tester_remarks']?>
                                                            </div>
                                                            <?php
                                                        }
                                                        ?>

                                                        <table class="table table-bordered" style="max-width: 550px;">
                                                            <tr>
                                                                <th>Task Priority</th>
                                                                <td><?= get_task_priority($item['task_priority'])?></td>
                                                            </tr>
                                                            <tr>
                                                                <th>Task Status</th>
                                                                <td><?=get_task_status($item['task_status'])?></td>
                                                            </tr>
                                                            <tr>
                                                                <th>Time Required</th>
                                                                <td><?= time_to_duration($item['required_time'])?> Hours</td>
                                                            </tr>
<!--                                                            <tr>-->
<!--                                                                <th>Tester</th>-->
<!--                                                                <td>--><?php //= $users[$item['tester_id']]?><!--</td>-->
<!--                                                            </tr>-->
<!--                                                            <tr>-->
<!--                                                                <th>Project Head</th>-->
<!--                                                                <td>--><?php //= $users[$item['project_lead_id']]?><!--</td>-->
<!--                                                            </tr>-->
                                                            <tr>
                                                                <th>Assign User</th>
                                                                <td>
                                                                    <?php
                                                                    if (has_permission('task_assign')){
                                                                        ?>
                                                                        <?php
                                                                        if ($item['task_status'] != 'completed'){
                                                                            ?>
                                                                            <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/assign'); ?>', '<?php echo get_phrase('assign_task'); ?>')"
                                                                                    class="btn btn-info btn-sm" style="width: 120px;">
                                                                                <small><i class="bi bi-person-check"></i></small> Assign User
                                                                            </button>
                                                                            <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/reschedule'); ?>', '<?php echo get_phrase('reschedule_task'); ?>')"
                                                                                    class="btn btn-outline-primary btn-sm d-none" style="width: 120px;">
                                                                                <small><i class="bi bi-clock"></i></small> Reschedule
                                                                            </button>
                                                                            <?php
                                                                        }
                                                                        if (is_project_manager() || is_super_admin()) {
                                                                            ?>
                                                                            <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/un_assign'); ?>', '<?php echo get_phrase('un_assign'); ?>')"
                                                                                    class="btn btn-outline-warning btn-sm" style="width: 120px;">
                                                                                <small><i class="bi bi-person-check"></i></small> Un Assign
                                                                            </button>
                                                                            <?php
                                                                        }
                                                                        ?>
                                                                        <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/history'); ?>', '<?php echo get_phrase('assign_task'); ?>')"
                                                                                class="btn btn-outline-info btn-sm" style="width: 120px;">
                                                                            <small><i class="bi bi-clock-history"></i></small> History
                                                                        </button>
                                                                        <?php
                                                                    }else{
                                                                        if (is_technical_support()){
                                                                            if ($item['task_status'] != 'testing' && $item['task_status'] != 'completed'){
                                                                                ?>
                                                                                <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/assign'); ?>', '<?php echo get_phrase('assign_task'); ?>')"
                                                                                        class="btn btn-info btn-sm" style="width: 120px;">
                                                                                    <small><i class="bi bi-person-check"></i></small> Assign User
                                                                                </button>
                                                                                <?php
                                                                            }
                                                                        }
                                                                    }
                                                                    ?>

                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div class="p-1"></div>
                                                </div>

                                                <?php
                                                if (has_permission('tasks/edit') || has_permission('tasks/delete')){
                                                    ?>
                                                    <div style="background-color: rgba(239,240,246,0.78); padding: 10px;" class="row m-0">
                                                        <div class="col-6 text-muted pb-2" style="font-size: 12px;">
                                                            <?php
                                                            if ($item['tested_by']>0){
                                                                ?>
                                                                <span>Tested by: <?=strtoupper($users[$item['tested_by']])?></span> |
                                                                <?php
                                                            }
                                                            ?>
                                                            Created by: <?=strtoupper($users[$item['created_by']])?> | On: <?= DateTime::createFromFormat('Y-m-d H:i:s', $item['created_on'])->format('d-m-Y g:i A')?>
                                                        </div>
                                                        <div class="col-6 text-right pb-2">
                                                            <div class="">
                                                                <?php
                                                                if (empty($users[$item['user_id']])){
                                                                    ?>
                                                                    <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/assign'); ?>', '<?php echo get_phrase('assign_task'); ?>')"
                                                                            class="btn btn-info btn-sm p-0" style="padding: 3px 20px!important;">
                                                                        <small><i class="bi bi-person-check"></i></small> Assign
                                                                    </button>
                                                                    <?php
                                                                }else{
                                                                    ?>
                                                                    <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/assign'); ?>', '<?php echo get_phrase('assign_task'); ?>')"
                                                                            class="btn btn-outline-info btn-sm p-0" style="padding: 3px 20px!important;">
                                                                        <small><i class="bi bi-person-check"></i></small> Re-Assign
                                                                    </button>
                                                                    <span class="assigned_to_history p-2"
                                                                          onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/history'); ?>', '<?php echo get_phrase('task_history'); ?>')">
                                                                                <?=get_employee_name(strtoupper($users[$item['user_id']]));?>
                                                                            </span>
                                                                    <?php
                                                                }
                                                                ?>
                                                            </div>
                                                        </div>
                                                        <div class="col-12">
                                                            <div class="row text-center mt-2" style="max-width: 500px;margin: auto">
                                                                <div class="col-3">
                                                                    <button onclick="show_extra_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/edit_content'); ?>', '<?php echo get_phrase('update_task_content'); ?>')"
                                                                            class="btn btn-outline-success btn-sm w-100" style="width: 100px">
                                                                        <small><i class="fas fa-pencil-alt"></i></small> Content
                                                                    </button>
                                                                </div>
                                                                <div class="col-3">
                                                                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/edit'); ?>', '<?php echo get_phrase('update_task'); ?>')"
                                                                            class="btn btn-outline-info btn-sm w-100" style="width: 100px">
                                                                        <b><i class="fas fa-pencil-alt"></i></b>
                                                                    </button>
                                                                </div>
                                                                <div class="col-3">
                                                                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/history'); ?>', '<?php echo get_phrase('assign_task'); ?>')"
                                                                            class="btn btn-outline-secondary btn-sm w-100">
                                                                        <b><i class="bi bi-clock-fill"></i></b>
                                                                    </button>
                                                                </div>
                                                                <?php
                                                                if (get_user_id()==$item['created_by'] || is_super_admin() || is_project_manager()) {
                                                                    ?>
                                                                    <div class="col-3">
                                                                        <button onclick="confirm_modal('<?=base_url("app/tasks/delete/{$item['id']}/");?>')"
                                                                                class="btn btn-outline-danger btn-sm w-100">
                                                                            <b><i class="bi bi-trash"></i></b>
                                                                        </button>
                                                                    </div>
                                                                    <?php
                                                                }
                                                                ?>
                                                            </div>
                                                        </div>


                                                    </div>
                                                    <?php
                                                }
                                                ?>
                                            </div>

                                        </div>
                                    </div>
                                    <?php
                                }
                            }
                            ?>
                        </div>
                        <div id="ta_no_result" class="text-center p-2" style="display: none">
                            <div class="p-4 shadow_pro bg-white col-8" style="">
                                <img width="96" height="96" src="https://img.icons8.com/fluency/96/nothing-found.png" alt="nothing-found"/>
                                <h4 class="text-danger mt-3">No Result Found!</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                </div>

            </div>


        </div>
        <!-- /.card-body -->
    </div>
</div>

<script type="text/javascript">
    function toggle_items(item_id) {
        $('#item_'+item_id).toggle('slow');
        $('#show_btn_'+item_id).toggle();
        $('#hide_btn_'+item_id).toggle();
    }
</script>

<style>
    td, th{
        padding: 6px!important;
    }
    .btn_hide_show{
        padding: 2px 4px!important;
        font-size: 13px!important;
        width: 120px;
    }
    .assigned_to{
        border-radius: 10px;
        padding: 2px 10px;
        font-size: 12px;
        font-weight: bold;
        color: rgb(105, 104, 104);
        background-color: rgba(154, 153, 153, 0.16);
    }
    .due_date{
        border-radius: 10px;
        padding: 2px 10px;
        font-size: 12px!important;
        font-weight: bold;
        background-color: rgba(224, 223, 223, 0.16);
    }
    .due_date span{
        font-size: 14px!important;
    }
    button{
        cursor: pointer;
    }
    .assigned_to_history{
        cursor: pointer;
        font-weight: bold!important;
    }
    .assigned_to_history:hover{
        opacity: 0.7!important;
    }

    .project_title_badge:hover{
        border-left: 5px solid #dfe4e8;
    }
</style>

