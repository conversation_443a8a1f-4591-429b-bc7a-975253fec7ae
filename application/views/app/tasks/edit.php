<script defer>
  tinymce.init({
    selector: '.ck_editor', 
    plugins: 'lists advlist pagebreak fullscreen', 
    toolbar: 'bold italic underline | styleselect | bullist numlist | blocks | print | hr | pagebreak | fullscreen | removeformat',
    menubar: false,
});
</script>
<?php
$item_id = $param1;
$edit_data = $this->tasks_m->get(['id' => $item_id])->row_array();
$projects = $this->projects_m->get(null, ['id', 'title', 'project_type'])->result_array();
$project_types = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
?>
<form class="form-horizontal" action="<?=base_url('app/tasks/edit/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">
        <div class="form-group col-12 p-0">
            <label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="title" name="title" value="<?=$edit_data['title']?>" placeholder="Title" required>
            </div>
        </div>
        <div class="form-group col-12 p-0">
            <label for="description" class="col-sm-12 col-form-label text-muted">Description </label>
            <div class="col-sm-12">
                <textarea class="form-control ck_editor" id="description" name="description" placeholder="Description" style="resize: none; height: 200px;font-size:17px!important;"><?=$edit_data['description']?></textarea>
            </div>
        </div>
        <div class="form-group col-7 p-0">
            <label for="project_id" class="col-sm-12 col-form-label text-muted">Project <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="project_id" name="project_id" required>
                    <option value="">Choose Project</option>
                    <?php
                    foreach ($projects as $key => $project) {
                        $selected = $project['id'] == $edit_data['project_id'] ? 'selected' : '';
                        echo "<option value='{$project['id']}' {$selected}>{$project['title']} - [{$project_types[$project['project_type']]}]</option>";
                    }
                    ?>
                </select>
            </div>
        </div>
        <div class="form-group col-5 p-0">
            <label for="task_type" class="col-sm-12 col-form-label text-muted">Task Type <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="task_type" name="task_type" required>
                    <option value="">Choose Type</option>
                    <option value="new" <?=$edit_data['task_type'] == 'new' ? 'selected' : ''?>>New/ Requirement</option>
                    <option value="bug" <?=$edit_data['task_type'] == 'bug' ? 'selected' : ''?>>Bug</option>
                    <option value="critical_bug" <?=$edit_data['task_type'] == 'critical_bug' ? 'selected' : ''?>>Critical Bug</option>
                    <option value="queries" <?=$edit_data['task_type'] == 'queries' ? 'selected' : ''?>>Queries</option>
                    <option value="meeting" <?=$edit_data['task_type'] == 'meeting' ? 'selected' : ''?>>Meeting</option>
                </select>
            </div>
        </div>
        <div class="col-12 p-2">
        </div>
        <div class="form-group col-4 p-0">
            <label for="task_priority" class="col-sm-12 col-form-label text-muted">Task Priority <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="task_priority" name="task_priority" required>
                    <option value="">Choose Priority</option>
                    <option value="high" <?=$edit_data['task_priority'] == 'high' ? 'selected' : ''?>>High</option>
                    <option value="medium" <?=$edit_data['task_priority'] == 'medium' ? 'selected' : ''?>>Medium</option>
                    <option value="low" <?=$edit_data['task_priority'] == 'low' ? 'selected' : ''?>>Low</option>
                </select>
            </div>
        </div>

        <div class="form-group col-8 p-0">
            <label for="requested_by" class="col-sm-12 col-form-label text-muted">Requested By </label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="requested_by" name="requested_by" value="<?=$edit_data['requested_by']?>" placeholder="Phone/ Name/ Reference" >
            </div>
        </div>
        <div class="form-group col-12 p-0 mt-0">
            <label for="remark_files" class="col-sm-12 col-form-label text-muted">Remark files </label>
            <div class="col-sm-12">
                <input type="file" class="form-control" id="remark_files" name="remark_files[]" value="" placeholder="Remark files" style="padding: 5px;cursor: pointer">
            </div>
            <div class="row p-2">
                <?php
                $remark_files = json_decode($edit_data['remark_files'], true);
                if (is_array($remark_files)){
                    foreach($remark_files as $key => $file){
                        ?>
                        <div class="p-2">
                            <a href="<?=base_url($file)?>" class="btn btn-info btn-sm" target="_blank">View File <?=$key + 1?></a>
                        </div>
                        <?php
                    }
                }
                ?>
            </div>
        </div>
        <div class="col-12">
            <hr>
        </div>
        <div class="form-group col-4 p-0">
            <label for="due_date" class="col-sm-12 col-form-label text-muted">Due Date <span class="text-danger">*</span> </label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="due_date" name="due_date" value="<?=$edit_data['due_date']?>" placeholder="Due Date" <?=!has_permission('tasks/index')?'readonly':''?> required>
            </div>
        </div>
        <div class="form-group col-4 p-0">
            <label for="required_time" class="col-sm-12 col-form-label text-muted">Required Time (Hours)</label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="required_time" name="required_time" onkeyup="duration_input(this)" value="<?=time_to_duration($edit_data['required_time'])?>" placeholder="00:00">
            </div>
        </div>

    </div>
	<div class="col-12" >
		<button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
			<small><i class="fa fa-check"></i></small> Save
		</button>
	</div>
</form>


<script type="application/javascript">
    // $('.ck_editor').each(function(){
    //     CKEDITOR.replace(this, {
    //         fontSize_defaultLabel: '18px',
    //         height: 400,
    //         contentsCss: '<?=base_url('assets/dist/css/ck_editor.css')?>'
    //     });
    // });
	$('.select2').select2();
</script>

<style>
    .cke_editable{
        font-size:15px!important;
        height:300px!important;
    }
</style>
