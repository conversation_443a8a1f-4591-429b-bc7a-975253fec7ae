<div class="container-fluid">
	<div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
		<a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
			<i class="fas fa-arrow-circle-left"></i> Go Back
		</a>
        <?php
            if (has_permission('products/add')){
                ?>
                <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=products/add', '<?= get_phrase('add_products'); ?>')"
                        class="btn btn-primary btn-mini float-right">
                    <small><i class="fas fa-plus"></i></small> Add <?= $page_title ?? '' ?>
                </button>
                <?php
            }
        ?>

	</div>
	<div class="card card-primary row mt-3 shadow-pro" style="margin:14px;">
		<div class="card-header">
			<h3 class="card-title"><?= strtoupper($page_title ?? '')?></h3>
		</div>
		<!-- /.card-header -->
		<div class="card-body">
			<table id="example1" class="table table-bordered table-striped">
				<thead>
				<tr>
					<th>#</th>
					<th>Title</th>
					<th>Description</th>
                    <?php
                    if (is_super_admin()){
                        ?>
                        <th style="width: 55px;">Action</th>
                        <?php
                    }
                    ?>
				</tr>
				</thead>
				<tbody>
				<?php
				if (isset($list_items)) {
					foreach ($list_items as $key => $item) {
						?>
						<tr>
							<td><?= $key + 1 ?></td>
							<th><?= $item['title']?></th>
							<td><?= $item['description']?></td>
                            <?php
                            if (is_super_admin()){
                                ?>
                                <td>
                                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=products/edit'); ?>', '<?php echo get_phrase('update_products'); ?>')"
                                            class="btn btn-info btn-sm">
                                        <small><i class="fas fa-pencil-alt"></i></small>
                                    </button>
                                    <button onclick="confirm_modal('<?=base_url("app/products/delete/{$item['id']}/");?>')" class="btn btn-outline-danger btn-sm">
                                        <small><i class="fas fa-trash"></i> </small>
                                    </button>
                                </td>
                                <?php
                            }
                            ?>

						</tr>
						<?php
					}
				}
				?>

				</tbody>

			</table>
		</div>
		<!-- /.card-body -->
	</div>
</div>

