<?php
$item_id = $param1;
$edit_data = $this->teams_m->get(['id' => $item_id])->row_array();
$team_types = ['php', 'flutter', 'python'];
$users = $this->users_m->get(['status' => 1, 'employee_status' => 1, 'work_assign' => 1])->result_array();

?>
<form class="form-horizontal" action="<?=base_url('app/teams/edit/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">
        <div class="form-group col-12 p-0">
            <label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="title" name="title" value="<?=$edit_data['title']?>" placeholder="Title" required>
            </div>
        </div>
        <div class="form-group col-12 p-0">
			<label for="team_type" class="col-sm-12 col-form-label text-muted">Type <span class="text-danger">*</span></label>
			<div class="col-sm-12">
                <select class="form-control select2" id="team_type" name="team_type" required>
                    <option value="">Choose Type</option>
                    <?php
                        foreach ($team_types as $team_type) {
                            $selected = $edit_data['team_type'] == $team_type ? 'selected' : '';
                            echo "<option value='{$team_type}' {$selected}>{$team_type}</option>";
                        }
                    ?>
                </select>
			</div>
		</div>
        <div class="form-group col-12 p-0">
			<label for="team_lead_id" class="col-sm-12 col-form-label text-muted">Team Lead</label>
			<div class="col-sm-12">
                <select class="form-control select2" id="team_lead_id" name="team_lead_id">
                    <option value="">Choose Team Lead</option>
                    <?php
                        foreach ($users as $user) {
                            $selected = $edit_data['team_lead_id'] == $user['id'] ? 'selected' : '';
                            echo "<option value='{$user['id']}' {$selected}>{$user['name']}</option>";
                        }
                    ?>
                </select>
			</div>
		</div>
        <div class="form-group col-12 p-0 pb-4">
			<label for="team_members" class="col-sm-12 col-form-label text-muted">Team Members <span class="text-danger">*</span></label>
			<div class="col-sm-12">
                <select class="form-control select2" id="team_members" name="team_members[]" multiple required>
                    <?php
                        foreach ($users as $user) {
                            $team_members = json_decode($edit_data['team_members'], true);
                            $selected = in_array($user['id'], $team_members) ? 'selected' : '';
                            echo "<option value='{$user['id']}' {$selected}>{$user['name']}</option>";
                        }
                    ?>
                </select>
			</div>
		</div>


    </div>
	<div class="col-12" >
		<button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
			<small><i class="fa fa-check"></i></small> Save
		</button>
	</div>
</form>


<script type="application/javascript">
	$('.select2').select2();
</script>
