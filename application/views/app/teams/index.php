<style>
    /* Hide buttons when printing */
    @media print {
        .no-print {
            display: none;
        }

        /* Force new page before this element */
        .page-break {
            page-break-before: always;
        }

        /* Avoid breaking inside this element */
        .no-break {
            page-break-inside: avoid;
        }
    }
</style>
<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <?php
        if (has_permission('teams/add')){
            ?>
            <button onclick="show_ajax_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=teams/add', '<?= get_phrase('add_team'); ?>')"
                    class="btn btn-primary btn-mini float-right btn-round">
                <small><i class="fas fa-plus"></i></small> Add <?= $page_title ?? '' ?>
            </button>
            <?php
        }
        ?>

    </div>
    <div class="mt-3" style="margin:14px;">
    <div class="card card-primary row mt-3 shadow-pro" style="margin-bottom:14px;">
		<div class="card-header">
			<h3 class="card-title"><?= strtoupper($page_title ?? '')?></h3>
		</div>
    </div>
        <!-- /.card-header -->
        <div class="" id="print_area">

            <div class="row">
                <?php
                if (isset($teams) && isset($users)) {
                    foreach ($teams as $key => $item) {
                        ?>
                        <div class="col-12 col-lg-12 p-2">
                            <div class="bg-white p-4 pt-0 shadow-pro" style="border-radius: 10px;">
                                <div style="margin-top:-30px;padding-bottom:10px">
                                <?php 
                                if($item['team_type']=='php'){
                                    ?>
                                    <span class="bg-info p-1" style="border-radius:5px;width:120px!important;">PHP</span>
                                    <?php
                                }elseif($item['team_type']=='flutter'){
                                    ?>
                                    <span class="bg-primary p-1" style="border-radius:5px;width:120px!important;">FLUTTER</span>
                                    <?php
                                }
                                ?> 
                                </div>
                                <div class="row">
                                    <div class="col-8">
                                        <h5 style="font-weight: bold; color: #434343;margin-bottom: 0px!important;">
                                            <?= $item['title']?>
                                        </h5>
                                        <small class="text-muted" style="font-size: 10px!important;">Created on: <?= DateTime::createFromFormat('Y-m-d H:i:s', $item['created_on'])->format('d-m-Y g:i A')?></small>
                                    </div>
                                    <div class="col-4">
                                    <?php
                                    if (is_super_admin() || is_project_manager()){
                                        ?>
                                        <div class="row">
                                            <div class="col-6 p-1">
                                                <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=teams/edit'); ?>', '<?php echo get_phrase('update_project'); ?>')"
                                                        class="btn btn-outline-info btn-sm w-100 btn-round">
                                                    <small><i class="fas fa-pencil-alt"></i></small> Edit
                                                </button>
                                            </div>
                                            <div class="col-6 p-1">
                                                <button onclick="confirm_modal('<?=base_url("app/teams/delete/{$item['id']}/");?>')"
                                                        class="btn btn-outline-danger btn-sm w-100 btn-round">
                                                    <small><i class="fas fa-trash"></i> </small> Delete
                                                </button>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                    ?>
                                    </div>
                                </div>
                                <hr>
                                
                                <div class="p-1 row">
                                <?php 
                                    $team_members = json_decode($item['team_members'], true);
                                    foreach ($team_members as $key => $team_member) {
                                        ?>
                                        <div class="d-inline-block p-1">
                                            <span style="border-radius:5px;font-size:18px;border:1px solid #24AE60;background-color:#F2FAF8;padding:10px;font-weight:bold;color:#24AE60">
                                                <?=strtoupper($users[$team_member])?>
                                            </span>
                                        </div>
                                        <?php
                                    }
                                ?>
                                </div>
                               
                                <div class="pt-3">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th colspan="3" style="background-color:#eeefff">Projects</th>
                                        </tr>
                                        <?php 
                                            foreach ($item['projects'] as $key => $project) {
                                                ?>
                                                <tr>
                                                    <td style="width:20px;font-size:18px;"><?=$key+1?></td>
                                                    <td style="font-size:18px;"><?=strtoupper($project['title'])?> <small>[<?=$project_type[$project['project_type']]?>]</small></td>
                                                    <td>
                                                        <button class="btn btn-sm btn-info" onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$project['id'].'/?page_name=projects/edit'); ?>', '<?php echo get_phrase('update_project').' - '.$project['title']; ?>')">Edit</button>
                                                    </td>
                                                </tr>
                                                <?php
                                            }
                                        ?>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="page-break"></div>
                        <?php
                    }
                }
                ?>
            </div>
            <div id="ta_no_result" class="text-center p-2" style="display: none">
                <div class="p-4 shadow_pro bg-white mx-auto" style="">
                    <img width="96" height="96" src="https://img.icons8.com/fluency/96/nothing-found.png" alt="nothing-found"/>
                    <h4 class="text-danger mt-3">No Result Found!</h4>
                </div>
            </div>
        </div>

        <button class="no-print btn btn-primary" style="width:110px" onclick="printDiv('print_area')">Print</button>
        <!-- /.card-body -->
    </div>

</div>

<script>
    function printDiv(divId) {
        var divContents = document.getElementById(divId).innerHTML;
        var originalContents = document.body.innerHTML;

        document.body.innerHTML = divContents;
        window.print();
        document.body.innerHTML = originalContents;
        location.reload(); // Reload page to restore original content
    }
</script>