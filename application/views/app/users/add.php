<?php
$roles = array_column($this->roles_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
?>
<form class="form-horizontal" action="<?=base_url('app/users/add/')?>" method="post" enctype="multipart/form-data">
	<div class="row" style="margin: 0!important;">
        <div class="form-group col-6">
            <label for="name" class="col-sm-12 col-form-label text-muted">Name <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="name" name="name" value="" placeholder="Name" required>
            </div>
        </div>
        <div class="form-group col-6">
            <label for="designation" class="col-sm-12 col-form-label text-muted">Designation <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="designation" name="designation" value="" placeholder="Designation" required>
            </div>
        </div>
        <div class="form-group col-6">
            <label for="phone" class="col-sm-12 col-form-label text-muted">Phone <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="number" class="form-control" id="phone" name="phone" value="" placeholder="Phone" onchange="check_phone_duplication(this.value, 0)" required>
            </div>
        </div>
        <div class="form-group col-6">
            <label for="email" class="col-sm-12 col-form-label text-muted">Email <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="email" class="form-control" id="email" name="email" value="" placeholder="Email" onchange="check_email_duplication(this.value, 0)" required>
            </div>
        </div>
        <div class="form-group col-6">
            <label for="role_id" class="col-sm-12 col-form-label text-muted">Role <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="role_id" name="role_id" required>
                    <option value="">Choose Type</option>
                    <?php
                    foreach ($roles as $role_id => $role) {
                        echo "<option value='{$role_id}'>{$role}</option>";
                    }
                    ?>
                </select>
            </div>
        </div>
        <div class="form-group col-6">
            <label for="employee_code" class="col-sm-12 col-form-label text-muted">Employee Code </label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="employee_code" name="employee_code" value="" placeholder="Employee Code">
            </div>
        </div>
        <div class="form-group col-6">
            <label for="hid_number" class="col-sm-12 col-form-label text-muted">HID Number </label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="hid_number" name="hid_number" value="" placeholder="HID Number">
            </div>
        </div>
        <div class="form-group col-6">
            <label for="id_card_no" class="col-sm-12 col-form-label text-muted">ID Card Number </label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="id_card_no" name="id_card_no" value="" placeholder="ID Card Number">
            </div>
        </div>

        <div class="form-group col-12">
            <label for="address" class="col-sm-12 col-form-label text-muted">Address </label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="address" name="address" value="" placeholder="Address">
            </div>
        </div>
        <div class="form-group col-4">
            <label for="secondary_phone" class="col-sm-12 col-form-label text-muted">Secondary Phone </label>
            <div class="col-sm-12">
                <input type="number" class="form-control" id="secondary_phone" name="secondary_phone" value="" placeholder="Secondary Phone" >
            </div>
        </div>
        <div class="form-group col-4">
            <label for="parent_name" class="col-sm-12 col-form-label text-muted">Parent Name </label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="parent_name" name="parent_name" value="" placeholder="Parent Name" >
            </div>
        </div>
        <div class="form-group col-4">
            <label for="parent_phone" class="col-sm-12 col-form-label text-muted">Parent Phone </label>
            <div class="col-sm-12">
                <input type="number" class="form-control" id="parent_phone" name="parent_phone" value="" placeholder="Parent Phone" >
            </div>
        </div>
        <div class="col-12">
            <hr>
        </div>
        <div class="form-group col-4">
            <label for="dob" class="col-sm-12 col-form-label text-muted">DOB </label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="dob" name="dob" value="" placeholder="DOB" >
            </div>
        </div>
        <div class="form-group col-4">
            <label for="join_date" class="col-sm-12 col-form-label text-muted">Join Date </label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="join_date" name="join_date" value="" placeholder="Join Date" >
            </div>
        </div>
        <div class="form-group col-4">
            <label for="leave_date" class="col-sm-12 col-form-label text-muted">Leave Date </label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="leave_date" name="leave_date" value="" placeholder="Leave Date" >
            </div>
        </div>
        <div class="col-12 mt-3">
        </div>
        <div class="form-group col-7">
            <label for="aadhaar_no" class="col-sm-12 col-form-label text-muted">Aadhaar no </label>
            <div class="col-sm-12">
                <input type="number" class="form-control" id="aadhaar_no" name="aadhaar_no" value="" placeholder="Aadhaar no" >
            </div>
        </div>
        <div class="form-group col-5">
            <label for="password" class="col-sm-12 col-form-label text-muted">Password <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="password" name="password" value="" placeholder="Password" required>
            </div>
        </div>

	</div>

	<div class="col-12" >
		<button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
			<small><i class="fa fa-check"></i></small> Save
		</button>
	</div>
</form>

<script type="text/javascript">
    $('.select2').select2();
</script>

