<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */
$item_id = $param1;
$roles = array_column($this->roles_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
$edit_data = $this->users_m->get(['id' => $item_id])->row_array();
?>
<form class="form-horizontal" action="<?=base_url('app/users/edit/'.$item_id)?>" method="post" enctype="multipart/form-data">
	<div class="row" style="margin: 0!important;">

        <div class="form-group col-12 text-center">
            <div class="profile-image-container">
                <?php
                $photo_url = is_file($edit_data['photo']) ? base_url($edit_data['photo']) : base_url('assets/images/default-user.png');
                ?>
                <div class="profile-image-wrapper" id="profile-image-wrapper">
                    <img src="<?= $photo_url ?>" id="current-profile-image" class="profile-image">
                    <div class="profile-image-overlay">
                        <i class="fas fa-camera"></i>
                        <div>Update Photo</div>
                    </div>
                </div>
                <input type="file" id="photo-input" accept="image/*" style="display: none;">
                <input type="hidden" id="photo" name="photo" value="">
                <div class="text-muted mt-2" style="font-size: 17px">
                    <strong><?=strtoupper($edit_data['name'])?></strong>
                </div>
            </div>

            <!-- Cropping Modal -->
            <div class="modal fade" id="cropModal" tabindex="-1" role="dialog" aria-labelledby="cropModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="cropModalLabel">Crop Profile Picture</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="img-container" style="max-height: 400px; overflow: hidden;">
                                <img id="image-preview" src="" style="max-width: 100%;">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="button" id="crop-button" class="btn btn-primary">Crop & Save</button>
                        </div>
                    </div>
                </div>
            </div>
            <hr>
        </div>
        <div class="form-group col-6">
            <label for="name" class="col-sm-12 col-form-label text-muted">Name <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="name" name="name" value="<?=$edit_data['name']?>" placeholder="Name" required>
            </div>
        </div>
        <div class="form-group col-6">
            <label for="designation" class="col-sm-12 col-form-label text-muted">Designation <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="designation" name="designation" value="<?=$edit_data['designation']?>" placeholder="Designation" required>
            </div>
        </div>
        <div class="form-group col-6">
            <label for="phone" class="col-sm-12 col-form-label text-muted">Phone <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="number" class="form-control" id="phone" name="phone" value="<?=$edit_data['phone']?>" placeholder="Phone" onchange="check_phone_duplication(this.value, 0)" required>
            </div>
        </div>
        <div class="form-group col-6">
            <label for="email" class="col-sm-12 col-form-label text-muted">Email <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="email" class="form-control" id="email" name="email" value="<?=$edit_data['email']?>" placeholder="Email" onchange="check_email_duplication(this.value, 0)" required>
            </div>
        </div>
        <div class="form-group col-6">
            <label for="role_id" class="col-sm-12 col-form-label text-muted">Role <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="role_id" name="role_id" required>
                    <option value="">Choose Type</option>
                    <?php
                    foreach ($roles as $role_id => $role) {
                        $selected = $role_id == $edit_data['role_id'] ? 'selected' : '';
                        echo "<option value='{$role_id}' {$selected}>{$role}</option>";
                    }
                    ?>
                </select>
            </div>
        </div>
        <div class="form-group col-6">
            <label for="employee_code" class="col-sm-12 col-form-label text-muted">Employee Code</label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="employee_code" name="employee_code" value="<?=$edit_data['employee_code']?>" placeholder="Employee Code">
            </div>
        </div>
        <div class="form-group col-6">
            <label for="hid_number" class="col-sm-12 col-form-label text-muted">HID Number </label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="hid_number" name="hid_number" value="<?=$edit_data['hid_number']?>" placeholder="HID Number">
            </div>
        </div>
        <div class="form-group col-6">
            <label for="id_card_no" class="col-sm-12 col-form-label text-muted">ID Card Number </label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="id_card_no" name="id_card_no" value="<?=$edit_data['id_card_no']?>" placeholder="IC Card Number">
            </div>
        </div>

        <div class="form-group col-12">
            <label for="address" class="col-sm-12 col-form-label text-muted">Address</label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="address" name="address" value="<?=$edit_data['address']?>" placeholder="Address">
            </div>
        </div>
        <div class="form-group col-4">
            <label for="secondary_phone" class="col-sm-12 col-form-label text-muted">Secondary Phone </label>
            <div class="col-sm-12">
                <input type="number" class="form-control" id="secondary_phone" name="secondary_phone" value="<?=$edit_data['secondary_phone']?>" placeholder="Secondary Phone">
            </div>
        </div>
        <div class="form-group col-4">
            <label for="parent_name" class="col-sm-12 col-form-label text-muted">Parent Name </label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="parent_name" name="parent_name" value="<?=$edit_data['parent_name']?>" placeholder="Parent Name">
            </div>
        </div>
        <div class="form-group col-4">
            <label for="parent_phone" class="col-sm-12 col-form-label text-muted">Parent Phone </label>
            <div class="col-sm-12">
                <input type="number" class="form-control" id="parent_phone" name="parent_phone" value="<?=$edit_data['parent_phone']?>" placeholder="Parent Phone">
            </div>
        </div>
        <div class="col-12">
            <hr>
        </div>
        <div class="form-group col-4">
            <label for="dob" class="col-sm-12 col-form-label text-muted">DOB <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="dob" name="dob" value="<?=$edit_data['dob']?>" placeholder="DOB">
            </div>
        </div>
        <div class="form-group col-4">
            <label for="join_date" class="col-sm-12 col-form-label text-muted">Join Date </label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="join_date" name="join_date" value="<?=$edit_data['join_date']?>" placeholder="Join Date" >
            </div>
        </div>
        <div class="form-group col-4">
            <label for="leave_date" class="col-sm-12 col-form-label text-muted">Leave Date </label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="leave_date" name="leave_date" value="<?=$edit_data['leave_date']?>" placeholder="Leave Date" >
            </div>
        </div>
        <!-- Photo input section moved to the top of the form -->
        <div class="col-12 mt-3">
        </div>
        <div class="form-group col-7">
            <label for="aadhaar_no" class="col-sm-12 col-form-label text-muted">Aadhaar no</label>
            <div class="col-sm-12">
                <input type="number" class="form-control" id="aadhaar_no" name="aadhaar_no" value="<?=$edit_data['aadhaar_no']?>" placeholder="Aadhaar no">
            </div>
        </div>
		<div class="col-12" >
			<button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
				<small><i class="fa fa-check"></i></small> Save
			</button>
		</div>
	</div>
</form>


<!-- Include Cropper.js CSS and JS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>

<style>
/* Profile Image Styles */
.profile-image-container {
    display: inline-block;
    margin-bottom: 15px;
}

.profile-image-wrapper {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
    transition: all 0.3s ease;
}

.profile-image-wrapper:hover .profile-image-overlay {
    opacity: 1;
}

.profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: white;
}

.profile-image-overlay i {
    font-size: 24px;
    margin-bottom: 5px;
}

.profile-image-overlay div {
    font-size: 12px;
    text-align: center;
}
</style>

<script type="application/javascript">
	$('.select2').select2();

	// Image cropping functionality
	let cropper;
	const photoInput = document.getElementById('photo-input');
	const imagePreview = document.getElementById('image-preview');
	const cropButton = document.getElementById('crop-button');
	const photoField = document.getElementById('photo');
	const profileImageWrapper = document.getElementById('profile-image-wrapper');
	const currentProfileImage = document.getElementById('current-profile-image');

	// Click on profile image to trigger file input
	profileImageWrapper.addEventListener('click', function() {
		photoInput.click();
	});

	// Initialize file input change event
	photoInput.addEventListener('change', function(e) {
		const files = e.target.files;
		if (!files || !files.length) return;

		const file = files[0];
		const reader = new FileReader();

		reader.onload = function(e) {
			// Set the image preview source
			imagePreview.src = e.target.result;

			// Show the crop modal
			$('#cropModal').modal('show');

			// Initialize cropper after modal is shown
			$('#cropModal').on('shown.bs.modal', function() {
				if (cropper) {
					cropper.destroy();
				}

				cropper = new Cropper(imagePreview, {
					aspectRatio: 1, // Square aspect ratio for profile picture
					viewMode: 1,    // Restrict the crop box to not exceed the size of the canvas
					guides: true,   // Show the dashed lines for guiding
					minContainerWidth: 300,
					minContainerHeight: 300
				});
			});
		};

		reader.readAsDataURL(file);
	});

	// Crop button click event
	cropButton.addEventListener('click', function() {
		if (!cropper) return;

		// Get the cropped canvas
		const canvas = cropper.getCroppedCanvas({
			width: 300,
			height: 300,
			imageSmoothingEnabled: true,
			imageSmoothingQuality: 'high'
		});

		// Convert canvas to blob
		canvas.toBlob(function(blob) {
			// Create a new FormData
			const formData = new FormData();

			// Generate a unique filename with proper extension
			const filename = 'cropped_' + new Date().getTime() + '.jpg';

			// Append the blob to the formData with the name 'photo' to match the global upload function
			formData.append('photo', blob, filename);
            formData.append('item_id', '<?=$edit_data['id']?>');

			// Send the cropped image to the server using the global upload function
			$.ajax({
				url: '<?= base_url("app/users/upload_cropped_image") ?>',
				method: 'POST',
				data: formData,
				processData: false, // Don't process the data
				contentType: false, // Don't set content type
				success: function(response) {
					const data = JSON.parse(response);
					if (data.success) {
						// Update the profile image with the cropped version
						currentProfileImage.src = canvas.toDataURL();

						// Set the hidden input value to the uploaded file path
						photoField.value = data.file_path;

						// Hide the modal
						$('#cropModal').modal('hide');

						// Show success message
						alert('Profile picture updated successfully!');
                        window.location.reload();
					} else {
						alert('Error uploading cropped image: ' + data.message);
					}
				},
				error: function() {
					alert('Error uploading cropped image. Please try again.');
				}
			});
		}, 'image/jpeg', 0.95); // JPEG format with 95% quality
	});

	// Reset cropper when modal is hidden
	$('#cropModal').on('hidden.bs.modal', function() {
		if (cropper) {
			cropper.destroy();
			cropper = null;
		}
	});
</script>
