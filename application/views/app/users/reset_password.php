<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */
$item_id = $param1;
$roles = array_column($this->roles_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
$edit_data = $this->users_m->get(['id' => $item_id])->row_array();
?>
<form class="form-horizontal" action="<?=base_url('app/users/reset_password/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="row p-0  m-0" style="margin: 0!important;">
        <div class=" col-12">
            <label for="name" class="col-sm-12 col-form-label text-muted">Name <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="name" name="name" value="<?=$edit_data['name']?>" placeholder="Name" disabled>
            </div>
        </div>
        <div class="col-12">
            <label for="password" class="col-sm-12 col-form-label text-muted">Password</label>
            <div class="col-sm-12">
                <input type="password" class="form-control" id="password" name="password" value="" placeholder="Password">
            </div>
        </div>
        <div class="col-6 p-3 mt-2" >
            <button class="btn btn-outline-danger w-100" data-dismiss="modal">
                <small><i class="fa fa-times"></i></small> Cancel
            </button>
        </div>
        <div class="col-6 p-3 mt-2" >
            <button type="submit" name="add" value="Save" class="btn btn-success w-100">
                <small><i class="fa fa-check"></i></small> Change Password
            </button>
        </div>
    </div>
</form>


<script type="application/javascript">
    $('.select2').select2();
</script>
