<?php
$item_id = $param1;
$roles = array_column($this->roles_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
$edit_data = $this->users_m->get(['id' => $item_id])->row_array();

// Format dates for display
$dob = !empty($edit_data['dob']) ? DateTime::createFromFormat('Y-m-d', $edit_data['dob'])->format('d M Y') : 'N/A';
$join_date = !empty($edit_data['join_date']) ? DateTime::createFromFormat('Y-m-d', $edit_data['join_date'])->format('d M Y') : 'N/A';
$leave_date = !empty($edit_data['leave_date']) ? DateTime::createFromFormat('Y-m-d', $edit_data['leave_date'])->format('d M Y') : 'N/A';

// Get photo URL
$photo_url = is_file($edit_data['photo']) ? base_url($edit_data['photo']) : base_url('assets/images/default-user.png');
?>

<div class="user-profile-container shadow-pro">
    <!-- Header Section with Profile Image and Basic Info -->
    <div class="profile-header">
        <div class="profile-image-container">
            <img src="<?= $photo_url ?>" alt="<?= $edit_data['name'] ?>" class="profile-image">
            <div class="status-indicator <?= $edit_data['employee_status'] == 1 ? 'active' : 'inactive' ?>"></div>
        </div>

        <div class="profile-basic-info">
            <h3 class="user-name"><?= strtoupper($edit_data['name']) ?></h3>
            <div class="user-role"><?= $roles[$edit_data['role_id']] ?></div>
            <div class="user-designation"><?= $edit_data['designation'] ?></div>
            <div class="user-code"><?= $edit_data['employee_code'] ?></div>
        </div>

        <div class="profile-actions">
            <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$edit_data['id'].'/?page_name=users/edit'); ?>', '<?php echo get_phrase('update_users'); ?>')"
                    class="btn btn-info btn-sm">
                <i class="fas fa-pencil-alt"></i> Edit User
            </button>
        </div>
    </div>

    <!-- Important Dates Section -->
    <div class="profile-dates-section">
        <h4 class="section-title">Important Dates</h4>
        <div class="dates-container">
            <div class="date-box">
                <div class="date-icon">
                    <i class="far fa-calendar-alt"></i>
                </div>
                <div class="date-content">
                    <div class="date-label">DOB</div>
                    <div class="date-value"><?= $dob ?></div>
                </div>
            </div>

            <div class="date-box">
                <div class="date-icon">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
                <div class="date-content">
                    <div class="date-label">JOD</div>
                    <div class="date-value"><?= $join_date ?></div>
                </div>
            </div>

            <?php if(!empty($edit_data['leave_date'])): ?>
            <div class="date-box leave-date">
                <div class="date-icon">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <div class="date-content">
                    <div class="date-label">DOL</div>
                    <div class="date-value"><?= $leave_date ?></div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Contact Information Section -->
    <div class="profile-contact-section">
        <h4 class="section-title">Contact Information</h4>
        <div class="contact-grid">
            <div class="id-item">
                <div class="id-label">Phone</div>
                <div class="id-value">
                    <i class="fas fa-phone"></i>
                    <span><?= $edit_data['phone'] ?></span>
                </div>
            </div>

            <div class="id-item">
                <div class="id-label">Email</div>
                <div class="id-value">
                    <i class="fas fa-envelope"></i>
                    <span><?= $edit_data['email'] ?></span>
                </div>
            </div>

            <div class="id-item">
                <div class="id-label">Secondary Phone</div>
                <div class="id-value">
                    <i class="fas fa-phone-alt"></i>
                    <span><?= !empty($edit_data['secondary_phone']) ? $edit_data['secondary_phone'] : 'N/A' ?></span>
                </div>
            </div>

            <div class="id-item">
                <div class="id-label">Address</div>
                <div class="id-value">
                    <i class="fas fa-map-marker-alt"></i>
                    <span><?= !empty($edit_data['address']) ? $edit_data['address'] : 'N/A' ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- ID Information Section -->
    <div class="profile-id-section">
        <h4 class="section-title">ID Information</h4>
        <div class="id-grid">
            <div class="id-item">
                <div class="id-label">HID Number</div>
                <div class="id-value">
                    <i class="fas fa-id-card"></i>
                    <span><?= !empty($edit_data['hid_number']) ? $edit_data['hid_number'] : 'N/A' ?></span>
                </div>
            </div>

            <div class="id-item">
                <div class="id-label">ID Card Number</div>
                <div class="id-value">
                    <i class="fas fa-id-badge"></i>
                    <span><?= !empty($edit_data['id_card_no']) ? $edit_data['id_card_no'] : 'N/A' ?></span>
                </div>
            </div>

            <div class="id-item">
                <div class="id-label">Aadhaar Number</div>
                <div class="id-value">
                    <i class="fas fa-fingerprint"></i>
                    <span><?= !empty($edit_data['aadhaar_no']) ? $edit_data['aadhaar_no'] : 'N/A' ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Emergency Contact Section -->
    <div class="profile-emergency-section">
        <h4 class="section-title">Emergency Contact</h4>
        <div class="emergency-grid">
            <div class="emergency-item">
                <div class="emergency-label">Parent Name</div>
                <div class="emergency-value">
                    <i class="fas fa-user"></i>
                    <span><?= !empty($edit_data['parent_name']) ? $edit_data['parent_name'] : 'N/A' ?></span>
                </div>
            </div>

            <div class="emergency-item">
                <div class="emergency-label">Parent Phone</div>
                <div class="emergency-value">
                    <i class="fas fa-phone"></i>
                    <span><?= !empty($edit_data['parent_phone']) ? $edit_data['parent_phone'] : 'N/A' ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* User Profile Container */
.user-profile-container {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
}

/* Profile Header Section */
.profile-header {
    display: flex;
    flex-wrap: wrap;
    padding: 20px;
    background: linear-gradient(to right, #f5f7fa, #e4e8f0);
    position: relative;
    align-items: center;
}

.profile-image-container {
    position: relative;
    margin-right: 25px;
}

.profile-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.status-indicator {
    position: absolute;
    bottom: 10px;
    right: 5px;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.status-indicator.active {
    background-color: #28a745;
}

.status-indicator.inactive {
    background-color: #dc3545;
}

.profile-basic-info {
    flex: 1;
}

.user-name {
    margin: 0 0 5px 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.user-role {
    display: inline-block;
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 8px;
}

.user-designation {
    color: #555;
    font-size: 1rem;
    margin-bottom: 5px;
}

.user-code {
    display: inline-block;
    background: #f0f0f0;
    color: #444;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
}

.profile-actions {
    margin-left: auto;
    align-self: flex-start;
}

/* Section Styling */
.profile-dates-section,
.profile-contact-section,
.profile-id-section,
.profile-emergency-section {
    padding: 20px;
    border-top: 1px solid #eee;
}

.section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

/* Dates Section */
.dates-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.date-box {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    min-width: 150px;
    flex: 1;
}

.date-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: #e9ecef;
    border-radius: 6px;
    margin-right: 12px;
}

.date-icon i {
    color: #495057;
    font-size: 1rem;
}

.date-content {
    display: flex;
    flex-direction: column;
}

.date-label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.8rem;
    text-transform: uppercase;
    margin-bottom: 3px;
}

.date-value {
    color: #212529;
    font-size: 0.95rem;
    font-weight: 500;
}

.leave-date .date-icon {
    background: #f8d7da;
}

.leave-date .date-icon i {
    color: #dc3545;
}

/* Contact and ID Grids */
.contact-grid,
.id-grid,
.emergency-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.contact-item,
.id-item,
.emergency-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.contact-label,
.id-label,
.emergency-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 5px;
    font-weight: 600;
    text-transform: uppercase;
}

.contact-value,
.id-value,
.emergency-value {
    font-size: 1rem;
    color: #333;
}

.contact-value i,
.id-value i,
.emergency-value i {
    width: 20px;
    color: #6c757d;
    margin-right: 5px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
    }

    .profile-image-container {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .profile-actions {
        margin-left: 0;
        margin-top: 15px;
        align-self: center;
    }

    .contact-grid,
    .id-grid,
    .emergency-grid {
        grid-template-columns: 1fr;
    }
}
</style>
