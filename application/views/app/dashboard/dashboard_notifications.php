<?php
if(isset($notifications)){
    if(count($notifications['jod_users']) || count($notifications['dob_users'])){
        ?>
        <div class="p-1 pb-0 pt-0">
            <div class="p-2 pt-0 pb-0 mb-0 row">
                <?php
                foreach($notifications['dob_users'] as $user){
                    ?>
                    <div class="col-lg-4 col-sm-12 p-1">
                        <div class="shadow_pro bg-danger-lighten text-danger pl-2" style="border-radius: 3px;">
                            <div class="p-2">
                                <b><?=strtoupper($user['name'])?> - BIRTHDAY</b>
                                <div style="font-weight: bold; font-size: 25px;"><?=DateTime::createFromFormat('Y-m-d', $user['dob'])->format('M d');?></div>
                            </div>
                        </div>
                    </div>
                    <?php
                }
    
                foreach($notifications['jod_users'] as $user){
                    ?>
                    <div class="col-lg-4 col-sm-12 p-1">
                        <div class="shadow_pro bg-danger-lighten text-danger pl-2" style="border-radius: 3px;">
                            <div class="p-2">
                                <b><?=strtoupper($user['name'])?> - WORK ANNIVERSARY</b>
                                <div style="font-weight: bold; font-size:25px">
                                    <?=DateTime::createFromFormat('Y-m-d', $user['join_date'])->format('M d');?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                }
                ?>
            </div>
        </div>
        <?php
    }
}
?>