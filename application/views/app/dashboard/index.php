<div class="max-width-1500 p-2 pt-0">
    <div class="row p-2 pt-0 mobile_hide" style="margin-top: -25px">
        <div class="col-7 p-2">
            <div class="card card-primary shadow-pro ">
                <div class="card-header p-2" style="background-color: #e1e7f5; border:0!important;color: #2a5bb6; ">
                    <h3 class="card-title" style="font-weight: 600!important;font-size: 15px;margin-top: 7px;">
                        <i class="bi bi-view-list"></i>
                        TASKS
                    </h3>
                    <div class="text-center" >
                        <div class="d-inline-block text-primary float-right mr-1">
                            <span style="font-weight: 900!important;font-size: 18px;"><?=$tasks_pending_count ?? 0?></span>
                            <small>TOTAL PENDING</small>
                        </div>
                        <div class="d-inline-block text-danger float-right mr-3">
                            <span style="font-weight: 900!important;font-size: 18px;"><?=$tasks_pending_due_count ?? 0?></span>
                            <small>DUE DATE EXPIRED</small>
                        </div>
                    </div>
                </div>

                <div class="card-body p-2 task_container" style="overflow-y: scroll!important;height: 80vh">

                    <?php
                        if (isset($tasks) && isset($projects) && isset($users)){
                            foreach ($tasks as $task){
                                $bg_color = get_task_bg_employee($task['task_status'], $task['due_date']);
                                if ($bg_color == 'bg-success-lighten' || $bg_color == 'bg-dark-lighten'){
                                    $style = 'style="opacity: 0.7"';
                                }else{
                                    $style = '';
                                }
                                ?>
                                <div class="p-1">
                                    <div class="single_task_item shadow-sm pl-2 <?=$bg_color?>"  <?=$style?> onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/task_details_employee'); ?>', '<?php echo get_phrase('update_task_status'); ?>')">
                                        <div class="bg-white p-2 pb-0">
                                            <?php
                                            if ($task['task_status'] == 'assigned'){
                                                ?>
                                                <div class="row pb-2">
                                                    <div class="col-6 text-left">
                                                        <?= get_task_priority($task['task_priority'])?>
                                                    </div>
                                                    <div class="col-6 text-right">
                                                        <?= get_due_date($task['due_date'])?>
                                                    </div>
                                                </div>
                                                <?php
                                            }
                                            ?>

                                            <div class="pb-1 float-right">
                                                <div class="pb-1 pr-1 pt-1">
                                                    <?=get_project_title($projects[$task['project_id']])?>
                                                </div>
                                            </div>

                                            <h5 style="background-color: #f4f4fa" class="text-primary p-2 m-0"><?=$task['title']?></h5>
                                            <?php
                                            if ($task['task_status'] == 'assigned'){
                                                ?>
                                                <div class="text-muted p-2 pb-3">
                                                    <div class="task_description">
                                                        <?=$task['description']?>
                                                    </div>
                                                </div>
                                                <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/task_details_employee'); ?>', '<?php echo get_phrase('update_task_status'); ?>')"
                                                        class="btn btn-outline-info btn-sm float-right" style="width: 100px;margin-top: -20px;">
                                                     VIEW TASK <small><i class="bi bi-arrow-right-circle"></i></small>
                                                </button>
                                                <?php
                                            }
                                            ?>
                                            <div class="float-left text-muted mt-2" style="font-size: 12px;">
                                                Added by: <?=strtoupper($users[$task['created_by']])?> | Updated on: <?= DateTime::createFromFormat('Y-m-d H:i:s', $task['updated_on'])->format('d-m-Y g:i A')?>
                                            </div>
                                            <div class="clearfix"></div>

                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                        }
                    ?>

                </div>

            </div>
        </div>
        <div class="col-5 p-2">
            <div class="card card-primary shadow-pro">
                <div class="card-header p-2" style="background-color: #d2f6ec; border:0!important;color: #2ab685; ">
                    <h3 class="card-title" style="font-weight: 600!important;font-size: 15px;margin-top: 7px;">
                        <i class="bi bi-check2-square"></i>
                        TODO
                    </h3>
                    <div class="text-center" >
                        <div class="d-inline-block text-primary float-right mr-1">
                            <span style="font-weight: 900!important;font-size: 18px;"><?=$todo_pending_count ?? 0?></span>
                            <small>TOTAL PENDING</small>
                        </div>
                        <div class="d-inline-block text-danger float-right mr-3">
                            <span style="font-weight: 900!important;font-size: 18px;"><?=$todo_pending_due_count ?? 0?></span>
                            <small>DUE DATE EXPIRED</small>
                        </div>
                    </div>
                </div>

                <div class="card-body p-2 task_container" style="overflow-y: scroll!important;height: 80vh">
                    <?php
                    if (isset($todo) && isset($users)){
                        foreach ($todo as $todo_item){
                            $bg_color = get_todo_bg_employee($todo_item['todo_status'], $todo_item['due_date']);
                            if ($bg_color == 'bg-success-lighten' || $bg_color == 'bg-dark-lighten'){
                                $style = 'style="opacity: 0.7"';
                            }else{
                                $style = '';
                            }
                            ?>
                            <div class="p-1">
                                <div class="single_todo_item shadow-sm pl-2  <?=$bg_color?>"  <?=$style?> onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$todo_item['id'].'/?page_name=todo/todo_details_employee'); ?>', '<?php echo get_phrase('update_todo_status'); ?>')">
                                    <div class="bg-white p-2 pb-0">
                                        <?php
                                        if ($todo_item['todo_status'] == 'assigned'){
                                            ?>
                                            <div class="row pb-2">
                                                <div class="col-6 text-left">
                                                    <?= get_todo_priority($todo_item['todo_priority'])?>
                                                </div>
                                                <div class="col-6 text-right">
                                                    <?= get_due_date($todo_item['due_date'])?>
                                                </div>
                                            </div>
                                            <?php
                                        }
                                        ?>

                                        <h5 style="background-color: #f4f4fa; font-size: 16px;" class="text-primary p-1 m-0"><?=$todo_item['title']?></h5>
                                        <?php
                                            if ($todo_item['todo_status'] == 'assigned'){
                                                ?>
                                                <div class="text-muted p-2 pb-3">
                                                    <div class="task_description">
                                                        <?=$todo_item['description']?>
                                                    </div>
                                                </div>
                                                <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$todo_item['id'].'/?page_name=tasks/todo_details_employee'); ?>', '<?php echo get_phrase('update_task_status'); ?>')"
                                                        class="btn btn-outline-info btn-sm float-right" style="width: 100px;margin-top: -35px">
                                                    VIEW TODO <small><i class="bi bi-arrow-right-circle"></i></small>
                                                </button>
                                                <?php
                                            }
                                        ?>
                                        <div class="float-right text-muted mt-2" style="font-size: 12px;">
                                            Added by: <?=strtoupper($users[$todo_item['created_by']])?>
                                        </div>

                                        <div class="clearfix"></div>
                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                    }
                    ?>
                </div>

            </div>
        </div>
    </div>
</div>


<style>
    .single_task_item:hover, .single_todo_item:hover{
        opacity: 0.6;
        cursor: pointer;
    }
    .single_task_item h5, .single_todo_item h5{
        font-size: 17px;
    }
    td, th{
        padding: 4px!important;
        font-size: 14px!important;
        border-color: rgba(245, 241, 244, 0.81) !important;
    }
    .task_description{
        font-size: 14px;
        overflow: hidden;
        max-width: 100%;
        text-overflow: ellipsis;
        /*width: fit-content;*/
        /*white-space: nowrap;*/
    }
</style>


