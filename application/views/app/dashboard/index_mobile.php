<div class="max-width-1500 pt-0" style="margin-top: -35px!important;">
    <?php
    include_once 'dashboard_timeline.php';
    
    if(!is_project_manager()){
        include_once 'dashboard_notifications.php';
    }
    
    
    ?>
    <div class="mb-1 mt-1 bg-white" style="margin-top: 5px!important;">
        <?php
                if (isset($performance)){
                    include_once 'awards.php';
                }
                ?>
        <?php 
        include_once 'work-calendar.php'; 
        include_once 'dashboard_warning.php';
        ?>
    </div>

    <div class="row p-0 pt-0 m-0" >
        <div class="col-12">

            <div class="tab-content" id="custom-tabs-three-tabContent">
                <div class="tab-pane fade active show" id="tabs_content_tasks" role="tabpanel" aria-labelledby="tabs_nav_tasks">
                    <div class="bg-white" style="background-color: #ffffff!important;">
                        <div class="card-header p-1 d-none1">
                            <div class="row">
                                <div class="col-9">
                                    <input type="text" class="form-control form-control-lg shadow-pro" id="ta_search_list" placeholder="Search Tasks.." style="padding:20px;font-size:16px">
                                </div>
                                <div class="col-3 pr-2 pl-0 m-0">
                                    <a href="<?=base_url('app/tasks/add_mobile')?>" class="btn btn-success w-100" style="padding: 7px">ADD &nbsp;<span><i class="bi bi-plus-circle"></i></span></a>
                                </div>
                            </div>
                        </div>
                        <?php 
                            if(is_project_manager() || get_user_id() == 10){
                                ?>
                                <div class="p-2">
                                    <a href="<?=base_url('app/tasks/task_update_bulk')?>" class="btn btn-primary w-100" style="padding: 7px">Bulk Update &nbsp;<span><i class="bi bi-plus-circle"></i></span></a>
                                </div>
                                <?php
                            }
                        ?>

                        <div class="card-body p-0 pt-2 task_container" style="overflow-y: scroll!important;height: 80vh">

                            <?php
                            if (isset($tasks) && isset($projects) && isset($users)){
                                foreach ($tasks as $task){
                                    $bg_color = get_task_bg_employee($task['task_status'], $task['due_date']);
                                    if ($bg_color == 'bg-success-lighten' || $bg_color == 'bg-dark-lighten'){
                                        $style = 'style="opacity: 0.7"';
                                    }else{
                                        $style = '';
                                    }
                                    ?>
                                    <div class="p-1 pb-2 pt-2 ta_search_item">
                                        <div onclick="window.location.href = '<?=base_url("app/tasks/task_details_employee_mobile/{$task['id']}/")?>'" class="single_task_item shadow-sm <?=$bg_color == 'bg-info' ? 'pl-2' : 'pl-1'?> <?=$bg_color?>"  <?=$style?>>
                                            <div class="bg-white p-2 pb-0">
                                                <div class="row pb-3">
                                                    <div class="col-5 text-left">
                                                        <div style="margin-top: -6px;">
                                                            <?=get_project_title($projects[$task['project_id']])?>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">

                                                    </div>
                                                    <div class="col-3">
                                                        <div class="mb-1 job_id_dashboard" style="font-size: 16px!important;width: 90px">
                                                            TRG-<b><?=$task['id']?></b>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="clearfix"></div>
                                                <h5 style="background-color: #f4f4fa;font-size: 16px!important;font-weight: bold;" class="text-primary p-2 m-0"><?=$task['title']?></h5>
                                                <?php
                                                if ($task['task_status'] == 'assigned'){
                                                    ?>
                                                    <div class="text-muted p-2 pb-3 d-none">
                                                        <div class="task_description">
                                                            <?=$task['description']?>
                                                        </div>
                                                    </div>
                                                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/task_details_employee'); ?>', '<?php echo get_phrase('update_task_status'); ?>')"
                                                            class="btn btn-outline-info btn-sm float-right d-none" style="width: 150px;margin-top: -20px;">
                                                        VIEW TASK <small><i class="bi bi-arrow-right-circle"></i></small>
                                                    </button>

                                                    <?php
                                                }
                                                ?>
                                                <div class="row pt-2">
                                                    <div class="col-6">
                                                        <?php
                                                        if ($task['task_status'] == 'assigned'){
                                                            echo get_due_date($task['due_date']);
                                                        }
                                                        ?>
                                                    </div>
                                                    <div class="col-6" style="text-align: right!important;">
                                                        <div class="pb-1 pr-1 pt-1" style="font-size: 12px!important;">
                                                            <?= get_task_type($task['task_type'])?>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class=" text-muted mt-2" style="font-size: 11px;">
                                                    Added by: <?=strtoupper($users[$task['created_by']])?> | Updated on: <?= DateTime::createFromFormat('Y-m-d H:i:s', $task['updated_on'])->format('d-m-Y g:i A')?>
                                                </div>

                                                <div class="clearfix"></div>

                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                }
                            }
                            ?>

                            <div id="ta_no_result" class="text-center p-2" style="display: none">
                                <div class="p-4 shadow_pro bg-white" style="">
                                    <img width="96" height="96" src="https://img.icons8.com/fluency/96/nothing-found.png" alt="nothing-found"/>
                                    <h6 class="text-danger mt-3">No Result Found!</h6>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            </div>





        </div>
        <div class="col-12 d-none">
            <div class="card card-primary shadow-pro mt-3">
                <div class="card-header p-1" style="background-image: linear-gradient(90deg, #23384E, #166686)!important;">
                    <h3 class="card-title" style="font-weight: 400!important;font-size: 14px;">
                        <i class="bi bi-calendar-fill"></i>
                        ATTENDANCE - <?=strtoupper(date('M'))?>
                        <button class="btn btn-primary btn-sm pull-right" style="margin-left:20px; padding: 1px 3px!important;font-size: 12px!important;"
                                onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=dashboard/employee_time_log_report', '<?= get_phrase('time_log_report'); ?>')">
                            View Detailed Report
                        </button>
                    </h3>
                </div>

                <div class="card-body p-1 task_container" style="overflow-y: scroll!important;height: 80vh">

                    <?php
                    if (isset($dates_array) && isset($time_log) && isset($attendance_data) && isset($time_log_type)){
                        if (is_project_manager()){
                            ?>
                            <div class="row">
                                <div class="col-4">

                                </div>
                                <div class="col-4">

                                </div>
                                <div class="col-4">

                                </div>
                            </div>
                            <?php
                        }
                        ?>
                        <table class="table table-bordered">
                            <tr style="font-size: 11px!important;color: #777!important;text-align: center; background-color: #e7f6fb">
                                <th style="font-size: 11px!important;color: #777!important;">ATT</th>
                                <?php
                                echo "<th style=\"font-size: 11px!important;color: #777!important;\">IN</th>";
                                echo "<th style=\"font-size: 11px!important;color: #777!important;\">OUT</th>";
                                ?>
                                <th style="font-size: 11px!important;color: #777!important;background-color: #b8f1ea">HOURS</th>
                                <th style="font-size: 11px!important;color: #777!important;background-color: #fce7e7">DIFF</th>
                                <th style="font-size: 11px!important;color: #777!important;background-color: #f1b8b8">BREAK</th>
                            </tr>
                            <?php
                            foreach ($dates_array as $date){
                                if (empty($attendance_data[$date]['attendance'])){
                                    continue;
                                }
                                $attendance = $attendance_data[$date]['attendance'] ?? '';
                                $remarks = $attendance_data[$date]['remarks'] ?? '';
                                if (!empty($attendance_data[$date]['off_date'])){
                                    $off_date = DateTime::createFromFormat('Y-m-d', $attendance_data[$date]['off_date'])->format('d-m-Y, l');
                                }else{
                                    $off_date = '';
                                }
                                ?>
                                <tr>
                                    <td style="padding: 20px!important;text-align: center;font-size: 13px; color: #777; background-color: #fefefe" colspan="6">
                                        <b><?= DateTime::createFromFormat('Y-m-d', $date)->format('d-M-Y')?></b>
                                        <small><?= DateTime::createFromFormat('Y-m-d', $date)->format('(l)')?></small>
                                    </td>
                                </tr>
                                <tr>
                                    <th style="text-align: center; font-size: 17px!important;padding: 10px;color: #444;background-color: #f5fdfc">
                                        <?="<b>{$attendance}</b>"?>
                                    </th>
                                    <?php
                                    if ($attendance != 'P' && $attendance != 'HD'){
                                        echo '<td class="text-center" colspan="5" style="background-color: rgba(239,242,245,0.38)">';
                                        echo "<b>{$attendance}</b>";
                                        if (!empty($remarks)){
                                            echo " (<small>{$remarks}</small>)";
                                        }
                                        if ($attendance =='OF'){
                                            echo "<br><small>OFF FOR - {$off_date}</small>";
                                        }
                                        if (empty($remarks) && empty($off_date)){
                                            echo "-";
                                        }
                                        echo '</td>';
                                    }else{
                                        foreach ($time_log_type as $log_type){
                                            if ($log_type['id'] != 1 && $log_type['id'] != 8){
                                                continue;
                                            }
                                            ?>
                                            <td class="text-center"
                                                style="font-size: 14px;line-height:1.0em!important;color:#666!important;background-color: rgba(243,245,246,0.15);padding: 5px 1px!important;">
                                                <?php
                                                if (!empty($time_log[$date][$log_type['id']])){
                                                    echo print_punch_time($time_log[$date][$log_type['id']], $log_type['id']);
                                                }else{
                                                    echo '-';
                                                }
                                                ?>
                                            </td>
                                            <?php
                                        }
                                        ?>
                                        <td style="background-color: #e9faf8;font-size: 15px!important;padding: 1px!important;" class="text-center">
                                            <?php
                                            if (!empty($time_log[$date]['total_duration']) && duration_to_time($time_log[$date]['total_duration']) != '00:00'){
                                                echo print_daily_time(duration_to_time($time_log[$date]['total_duration']));
                                            }else{
                                                echo '-';
                                            }
                                            ?>
                                        </td>
                                        <td class="text-center" style="font-size: 13px!important;background-color: #faf6f6">
                                            <?php
                                            if (!empty($time_log[$date]['total_duration']) && duration_to_time($time_log[$date]['total_duration']) != '00:00'){
                                                echo print_daily_diff(duration_to_time($time_log[$date]['total_duration']));
                                            }else{
                                                echo '-';
                                            }
                                            ?>
                                        </td>
                                        <td class="text-center" style="font-size: 13px!important;background-color: #faefef">
                                            <?php
                                            if (!empty($time_log[$date]['total_break']) && duration_to_time($time_log[$date]['total_break']) != '00:00'){
                                                echo print_break_time(duration_to_time($time_log[$date]['total_break']));
                                            }else{
                                                echo '-';
                                            }
                                            ?>
                                        </td>
                                        <?php
                                    }

                                    ?>

                                </tr>
                                <?php
                            }
                            ?>
                            <tr style="background-color: #fcfeff; display: none">
                                <th colspan="4" class="text-right">TOTAL</th>
                                <th>-</th>
                                <th>-</th>
                            </tr>
                        </table>
                        <?php
                    }
                    ?>

                </div>

            </div>
        </div>
    </div>
</div>


<style>
    .nav_switch_tasks, .nav_switch_todo{
        /*border: 0!important;*/
    }
    .nav_switch_tasks.active{
        border: 0!important;
        /*border-top: 4px solid #182738 !important;*/
    }
    .nav_switch_todo.active{
        border: 0!important;
        border-top: 4px solid #182738 !important;
    }
    .single_task_item:hover, .single_todo_item:hover{
        /*opacity: 0.8;*/
        cursor: pointer;
        padding-right: 1px!important;
        padding-top: 1px!important;
        padding-bottom: 1px!important;
    }
    .single_task_item h5, .single_todo_item h5{
        font-size: 17px;
    }
    td, th{
        padding: 4px!important;
        font-size: 14px!important;
        border-color: rgba(245, 241, 244, 0.81) !important;
    }
    .task_description{
        font-size: 14px;
        overflow: hidden;
        max-width: 100%;
        text-overflow: ellipsis;
        /*width: fit-content;*/
        /*white-space: nowrap;*/
    }

    .project_title_badge{
        padding: 2px !important;
    }
</style>


