<?php
    $user_name = $this->db->get_where('users', ['id' => get_user_id()])->row()->name;
    $month = date('Y-m');
    $this->db->like('job_date', $month);
    $this->db->where('user_id', get_user_id());
    $dashboard_lock = $this->db->get('dashboard_lock')->result_array();
    $warning_count = count($dashboard_lock);
    
    if($warning_count > 2){
        ?>
        <div class="p-2">
            <div class="alert bg-danger-lighten text-danger" role="alert" style="font-size:18px;">
              Dear <?=$user_name?>, you have <u>missed updating your work <b><?=$warning_count?></b> times</u> this month. You must take this matter seriously.
            </div>
        </div>
        <?php
    }
?>