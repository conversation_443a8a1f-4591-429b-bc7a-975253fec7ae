<?php
$job_date_item = new DateTime($job_date);
$previous_date = clone $job_date_item;
$next_date = clone $job_date_item;

$previous_date->modify('-1 day');
$next_date->modify('+1 day');

$previous_date = $previous_date->format('Y-m-d');
$next_date = $next_date->format('Y-m-d');

?>
<div class="p-2" style="width: 100%;overflow-x: auto;">

    <div class="time-bar-wrapper" style="min-width: 800px;width: 100%;">
        <div style="padding: 5px 10px; font-size: 16px;color:rgba(15,102,41);background-color: rgba(24,140,60,0.04);margin-bottom: 10px;border-radius: 5px;">
            <div class="row">
                <div class="col-4 d-flex">
                    <div class="p-1">
                        <a href="<?=base_url('app/dashboard/index/'.$previous_date.'/?user_id='.$_GET['user_id'])?>" class="btn btn-outline-primary" style="padding: 5px 15px;border-radius:150px"><i class="bi bi-arrow-left-circle"></i></a>
                    </div>
                    <div class="p-1">
                        <input type="date" class="form-control" value="<?=$job_date ?? ''?>" onchange="get_report(this.value)">
                    </div>
                    <div class="p-1">
                        <a href="<?=base_url('app/dashboard/index/'.$next_date.'/?user_id='.$_GET['user_id'])?>" class="btn btn-outline-primary" style="padding: 5px 15px;border-radius:150px"><i class="bi bi-arrow-right-circle"></i></a>
                    </div>
                </div>
                <div class="col-5">
                    <?php
                        if (isset($user_details)){
                            if (get_user_id()!=$user_details['id']){
                                ?>
                                <a href="<?=base_url('app/pending_report_employee/index/')?>" class="btn btn-outline-secondary btn-sm btn-round mr-3">Back to Users</a>
                                <?php
                            }
                            ?>
                            <span style="font-size: 18px;padding: 4px;font-weight: bold">
                                <?=strtoupper($user_details['name'])?>
                            </span>
                            <?php
                        }
                    ?>
                </div>
                <div class="col-3 p-2" style="text-align: right">
                    Time Sheet: <b><?= DateTime::createFromFormat('Y-m-d', $job_date)->format('d-m-Y (l)')?></b>
                </div>
            </div>
        </div>
        <div class="time-bar" id="time-bar"></div>
        <div class="time-labels" id="time-labels"></div>
        <button class="btn btn-outline-dark" id="show_details_btn" onclick="show_time_sheet()">
            Show Time Sheet <i class="bi bi-arrow-down-circle"></i>
        </button>
        <div id="time_sheet_table" style="display: none">
            <table class="time-log" id="time-log">
                <thead>
                <tr>
                    <th>Start</th>
                    <th>End</th>
<!--                    <th>Worked</th>-->
                    <th>Project</th>
                    <th>Type</th>
                    <th>Action</th>
                </tr>
                </thead>
                <tbody>
                <!-- Log entries will be generated by JavaScript -->
                </tbody>
            </table>
        </div>
        <div class="timeline_tooltip" id="timeline_tooltip"></div>
    </div>

</div>

<style>
    /*body {*/
    /*    font-family: Arial, sans-serif;*/
    /*    !* display: flex; *!*/
    /*    justify-content: center;*/
    /*    align-items: center;*/
    /*    height: 100vh;*/
    /*    margin: 0;*/
    /*    background-color: #f0f0f0;*/
    /*}*/

    .time-bar-wrapper {
        background-color: #fff;
        border-radius: 5px;
        padding: 10px;
        padding-bottom: 3px;
        /*box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);*/
        overflow: hidden;
        /*width: 600px;*/
    }

    .time-bar {
        position: relative;
        height: 28px;
        background-color: #e0e0e0;
        /*background-color: #f5e2e0;*/
        border-radius: 35px;
        overflow: hidden;
        margin-bottom: 10px;
    }

    .time-segment {
        position: absolute;
        height: 100%;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .time-segment.work {
        background-color: #04c16c;
    }

    .time-segment.break {
        background-color: #db5549;
    }

    .time-segment:hover {
        /*background-color: #333;*/
        color: #fff;
        opacity: 0.6;
    }

    .time-labels {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .time-label {
        flex: 1;
        text-align: center;
        font-size: 12px;
    }

    .time-log {
        margin-top: 10px;
        width: 100%;
        /*max-width: 900px;*/
        border-collapse: collapse;
    }

    .time-log th, .time-log td {
        text-align: left;
        padding: 10px 7px;
        border-bottom: 1px solid #f2eded;
        font-size: 13px;
    }

    .time-log th {
        background-color: #f9f9f9;
    }

    .timeline_tooltip {
        position: absolute;
        background-color: rgba(0, 0, 0, 0.7);
        color: #fff;
        padding: 5px;
        border-radius: 3px;
        display: none;
        pointer-events: none;
        z-index: 10;
        font-size: 12px!important;
    }
    #show_details_btn{
        width: 150px;
        padding: 4px 20px; font-size: 12px;
        margin-top: -20px!important;
        border-radius: 30px;
    }

</style>

<script>
    function show_time_sheet() {
        var element = document.getElementById('time_sheet_table');
        var element_btn = document.getElementById('show_details_btn');
        if (element.style.display === "none") {
            element.style.display = "block";
            element.style.width = "100%!important";
            element_btn.innerHTML = "Hide Time Sheet <i class='bi bi-arrow-up-circle'></i>";
        } else {
            element.style.display = "none";
            element_btn.innerHTML = "Show Time Sheet <i class='bi bi-arrow-down-circle'></i>";
        }
    }



    $(document).ready(function() {
        const timeSegments = <?=json_encode($data_sheet ?? [])?>;

        const minDefaultHour = 9.5; // 9:30 AM
        const maxDefaultHour = 17.5; // 5:30 PM

        const timeInHours = time => {
            const [hour, period] = time.split(' ');
            let [hours, minutes] = hour.split(':').map(Number);
            if (period === 'PM' && hours !== 12) hours += 12;
            if (period === 'AM' && hours === 12) hours = 0;
            return hours + minutes / 60;
        };

        const minHour = Math.min(...timeSegments.map(seg => timeInHours(seg.start)));
        const maxHour = Math.max(...timeSegments.map(seg => timeInHours(seg.end)));

        const startHour = minHour < minDefaultHour ? minHour : minDefaultHour;
        const endHour = maxHour > maxDefaultHour ? maxHour : maxDefaultHour;

        const roundedStartHour = Math.floor(startHour * 2) / 2; // Round down to the nearest 30 minutes
        const roundedEndHour = Math.ceil(endHour * 2) / 2; // Round up to the nearest 30 minutes

        const timeBar = $('#time-bar');
        const timeLabels = $('#time-labels');
        const timeLog = $('#time-log tbody');

        const totalHours = roundedEndHour - roundedStartHour;
        const hourWidthPercent = 100 / totalHours;

        let totalWorkingHours = 0;

        timeSegments.forEach(segment => {
            const startHourInDay = timeInHours(segment.start);
            const endHourInDay = timeInHours(segment.end);
            const start = Math.max(startHourInDay, roundedStartHour);
            const end = Math.min(endHourInDay, roundedEndHour);
            const duration = end - start;
            const left = (start - roundedStartHour) * hourWidthPercent;
            const width = duration * hourWidthPercent;

            if (segment.type === 'work') {
                totalWorkingHours += duration;
            }

            const segmentDiv = $('<div>')
                .addClass('time-segment')
                .addClass(segment.type)
                .css({ left: `${left}%`, width: `${width}%` })
                .data('info', `${segment.start} - ${segment.end}: ${segment.type.charAt(0).toUpperCase() + segment.type.slice(1)}`)
                .data('job-id', segment.jobId)
                .data('project', segment.project)
                .hover(function(event) {
                    const timeline_tooltip = $('#timeline_tooltip');
                    const info = $(this).data('info');
                    const jobId = $(this).data('job-id');
                    const project = $(this).data('project');
                    const timeline_tooltipText = `${info}\nJob ID: ${jobId}\nProject: ${project}`;
                    timeline_tooltip.text(timeline_tooltipText).css({
                        top: event.pageY + 10,
                        left: event.pageX + 10
                    }).show();
                }, function() {
                    $('#timeline_tooltip').hide();
                })
                .mousemove(function(event) {
                    $('#timeline_tooltip').css({
                        top: event.pageY + 10,
                        left: event.pageX + 10
                    });
                });

            timeBar.append(segmentDiv);
            var delete_url = '<?=base_url("app/task_assign/delete/")?>' + segment.item_id;

            const logRow = $('<tr>')
                .append($('<td>').text(segment.start))
                .append($('<td>').text(segment.end))
                // .append($('<td>').text(`${duration.toFixed(2)}h`))
                .append($('<td>').text(segment.project))
                .append($('<td>').text(`[${segment.type.charAt(0).toUpperCase() + segment.type.slice(1)}]`))
                .append($('<td>').html(`<button onclick="confirm_modal('${delete_url}')" class="btn btn-sm btn-round btn-outline-danger"><i class="bi bi-trash"></i></button>`));
            timeLog.append(logRow);
        });

        for (let i = roundedStartHour; i <= roundedEndHour; i += 0.5) {
            const hourLabel = $('<div>').addClass('time-label').text(formatMinuteLabel(i));
            timeLabels.append(hourLabel);
        }

        function formatMinuteLabel(hour) {
            const period = hour >= 12 ? 'PM' : 'AM';
            const wholeHour = Math.floor(hour);
            const minutes = (hour - wholeHour) * 60;
            const formattedHour = wholeHour % 12 === 0 ? 12 : wholeHour % 12;
            return `${formattedHour}:${minutes === 0 ? '00' : minutes} ${period}`;
        }

        // Display total working hours
        $('.time-bar-wrapper').append(`<p class="p-1 pb-0"><small>Total Working Hours:</small> ${totalWorkingHours.toFixed(2)} hours</p>`);
    });




</script>
<script>
    function get_report(date){
        <?php
            if (isset($_GET['user_id'])){
                ?>
        window.location.href = '<?=base_url('app/dashboard/index/')?>' + date + '/?user_id=<?=$_GET['user_id']?>';
                <?php
            }else{
                ?>
        window.location.href = '<?=base_url('app/dashboard/index/')?>' + date + '/';
                <?php
            }
        ?>

    }
</script>