<div class="max-width-1500 p-2 pt-0" style="margin-top: -30px!important;">
    <?php
    include_once 'dashboard_timeline.php';

    include_once 'dashboard_notifications.php';
    
    include_once 'dashboard_warning.php';
    ?>
    
    

    <div class="row p-2 pt-0 mobile_hide">
        <div class="col-7 p-2">
            <div class="tab-content" id="custom-tabs-three-tabContent">
                <div class="tab-pane fade active show" id="tabs_content_tasks" role="tabpanel" aria-labelledby="tabs_nav_tasks">
                    <div class="bg-white" style="background-color: #ffffff!important;">
                        <div class="card-header p-1 d-none1" style="background-color: #fff">
                            <div class="row p-2">
                                <div class="col-6 p-1 pb-0">
                                    <div class="p-2" style="background-color: #fcedef;border-radius: 5px;">
                                        <div class="row">
                                            <div class="col-6">
                                                <h2 class="text-danger" style="font-weight: bold;"><?=$tasks_pending_count ?? 0?></h2>
                                            </div>
                                            <div class="col-6 p-2">
                                                <button class="btn btn-outline-danger btn-round float-right d-none"
                                                        style="font-size: 13px; width: 130px;">VIEW DETAILED <i class="bi bi-arrow-right-circle"></i></button>
                                            </div>
                                            <div class="col-12">
                                                <span class="text-danger">PENDING TASKS</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 p-1 pb-0 d-none">
                                    <div class="p-2" style="background-color: #f1f2f5;border-radius: 5px;">
                                        <div class="row">
                                            <div class="col-6">
                                                <h2 class="text-info" style="font-weight: bold;">0</h2>
                                            </div>
                                            <div class="col-6 p-2">
                                                <button class="btn btn-outline-info btn-round float-right d-none"
                                                        style="font-size: 13px; width: 130px;">VIEW PROJECTS <i class="bi bi-arrow-right-circle"></i></button>
                                            </div>
                                            <div class="col-12">
                                                <span class="text-info">YOUR PROJECTS</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 p-1 pb-0">
                                    <div class="p-2" style="background-color: #f1f2f5;border-radius: 5px;">
                                        <div class="row">
                                            <div class="col-6">
                                                <h2 class="text-info" style="font-weight: bold;">
                                                    <?php
                                                        if($log_in_time){
                                                           echo $log_in_time['print_time'];
                                                        }
                                                    ?>
                                                </h2>
                                            </div>
                                            <div class="col-6 p-2">
                                                <?php 
                                                if($log_in_time['is_late']){
                                                    ?>
                                                    <h5 class="text-danger d-none" style="font-weight:bold">(LATE)</h5>
                                                    <?php
                                                }
                                                ?>
                                            </div>
                                            <div class="col-12">
                                                <span class="text-info">LOG-IN TIME</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="p-0 pr-1 pl-1">
                                <input type="text" class="form-control form-control-lg shadow-pro" id="ta_search_list" placeholder="Search Tasks/ Bugs.." style="padding:18px;font-size:16px">
                            </div>
                        </div>

                        <div class="card-body p-0 pt-2 task_container" style="overflow-y: scroll!important;height: 80vh">

                            <?php
                            if (isset($tasks) && isset($projects) && isset($users)){
                                foreach ($tasks as $task){
                                    $bg_color = get_task_bg_employee($task['task_status'], $task['due_date']);
                                    if ($bg_color == 'bg-success-lighten' || $bg_color == 'bg-dark-lighten'){
                                        $style = 'style="opacity: 0.7"';
                                    }else{
                                        $style = '';
                                    }
                                    ?>
                                    <div class="p-2 ta_search_item" id="tasks_list_items">
                                        <div class="single_task_item shadow-sm <?=$bg_color == 'bg-primary' ? 'pl-3' : 'pl-2'?> <?=$bg_color?>"  <?=$style?> onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/task_details_employee'); ?>', '<?php echo get_phrase('update_task_status'); ?>')">
                                            <div class="bg-white p-2 pb-0">
                                                <div class="row pb-3">
                                                    <div class="col-4 text-left">
                                                        <div>
                                                            <?=get_project_title($projects[$task['project_id']])?>
                                                        </div>
                                                    </div>
                                                    <div class="col-4 text-center">
                                                        <?php
                                                        if ($task['task_status'] == 'assigned'){
                                                            echo get_due_date($task['due_date']);
                                                        }
                                                        ?>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="mb-1 job_id_dashboard" style="font-size: 16px!important;">
                                                            TRG-<b><?=$task['id']?></b>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="clearfix"></div>


                                                <div class="pb-1 float-right">
                                                    <div class="pb-1 pr-1 pt-1">
                                                        <?= get_task_type($task['task_type'])?>
                                                    </div>
                                                </div>

                                                <h5 style="background-color: #f4f4fa; font-size: 16px" class="text-primary p-2 m-0"><?=$task['title']?></h5>

                                                <div class="float-left text-muted mt-2" style="font-size: 10px;">
                                                    Added by: <?=strtoupper($users[$task['created_by']])?> | Updated on: <?= DateTime::createFromFormat('Y-m-d H:i:s', $task['updated_on'])->format('d-m-Y g:i A')?>
                                                </div>
                                                <div class="clearfix"></div>

                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                }
                            }
                            ?>

                            <div id="ta_no_result" class="text-center p-2" style="display: none">
                                <div class="p-4 shadow_pro bg-white" style="">
                                    <img width="96" height="96" src="https://img.icons8.com/fluency/96/nothing-found.png" alt="nothing-found"/>
                                    <h6 class="text-danger mt-3">No Result Found!</h6>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            </div>





        </div>
        <div class="col-5 p-2 d-none1">
            <div class="card card-primaryn shadow-pro d-none1" style="max-width: 480px">
                <?php
                if (isset($performance)){
                    include_once 'awards.php';
                }
                ?>
                <?php
                    include_once 'work-calendar.php';
                ?>
                
                <?php
                $kpi_score = $kpi_score ?? 0;
                    if ($kpi_score==100){
                        $kpi_color = 'rgb(33,159,72)';
                        $kpi_btn_color = 'btn-outline-success';
                        $kpi_bg_color = '#e6f6e9';
                    }elseif ($kpi_score<100 && $kpi_score >= 90){
                        $kpi_color = 'rgb(52, 152, 219)';
                        $kpi_btn_color = 'btn-outline-info';
                        $kpi_bg_color = '#F1F2F5';
                    }elseif ($kpi_score<90 && $kpi_score >= 80){
                        $kpi_color = 'rgb(255,125,86)';
                        $kpi_btn_color = 'btn-outline-warning';
                        $kpi_bg_color = '#fcf6ed';
                    }else{
                        $kpi_color = 'rgb(189,16,43)';
                        $kpi_btn_color = 'btn-outline-danger';
                        $kpi_bg_color = '#FBECEF';
                    }
                ?>
                <div class="row p-3 pt-0 pb-0 <?=is_project_manager() && 5>6 ? '' : 'd-none'?>">
                    <div class="col-12 p-0 pb-0">
                        <div class="p-3" style="background-color: <?=$kpi_bg_color?>;border-radius: 5px;">
                            <div class="row">
                                <div class="col-6">
                                    <h2 style="color: <?=$kpi_color?>;font-weight: bold;font-size:40px"><?=$kpi_score ?? '0'?>%</h2>
                                </div>
                                <div class="col-6 p-2">
                                    <button class="btn <?=$kpi_btn_color.'_' ?> btn-info btn-round float-right" onclick="show_large_modal('<?=base_url('app/modal/popup/get/?page_name=dashboard/kpi_working')?>', 'How KPI Works?')"
                                            style="font-size: 14px; width: 165px;font-weight: bold">HOW KPI WORKS <i class="bi bi-question-circle"></i></button>
                                </div>
                                <div class="col-4">
                                    <span style="color: <?=$kpi_color?>;font-weight:bold;">KPI SCORE</span>
                                </div>
                                <div class="col-12">
                                    <span style="color: <?=$kpi_color?>;font-weight:bold;">
                                        <small>YOU MUST KEEP YOUR KPI SCORE 100%</small>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-header p-2 d-none1 pt-0 mt-0" style="">
                    <div class="p-2" style1="background-color:#F1F2F5!important;">
                        <h3 class="card-title text-info" style="font-weight: 600!important;font-size: 15px;margin-top: 4px;">
                            <i class="bi bi-calendar-fill"></i>
                            ATTENDANCE - <?=strtoupper(date('M'))?>
                            
                        </h3>
                    </div>
                    
                </div>

                <div class="card-body p-1 task_container d-none1" style="overflow-y: scroll!important;height: 80vh">

                    <?php
                    if (isset($dates_array) && isset($time_log) && isset($attendance_data) && isset($time_log_type)){
                        if (is_project_manager()){
                            ?>
                            <div class="row">
                                <div class="col-4">

                                </div>
                                <div class="col-4">

                                </div>
                                <div class="col-4">

                                </div>
                            </div>
                            <?php
                        }
                        ?>
                        <table class="table table-bordered">
                            <tr style="font-size: 12px!important;color: #777!important;text-align: center; background-color: #e7f6fb">
                                <th style="font-size: 12px!important;color: #777!important;">DATE</th>
                                <th style="font-size: 12px!important;color: #777!important;">ATT</th>
                                <?php
                                foreach ($time_log_type as $log_type){
                                    if ($log_type['id'] == 1 || $log_type['id'] == 8){
                                        echo "<th style=\"font-size: 12px!important;color: #777!important;\">".strtoupper(get_phrase($log_type['title']))."</th>";
                                    }
                                }
                                ?>
                                <th style="font-size: 12px!important;color: #777!important;background-color: #b8f1ea">HOURS</th>
                                <th style="font-size: 12px!important;color: #777!important;background-color: #fce7e7">DIFF</th>
                                <th style="font-size: 12px!important;color: #777!important;background-color: #f1b8b8">BREAK</th>
                            </tr>
                            <?php
                            foreach ($dates_array as $date){
                                if (empty($attendance_data[$date]['attendance'])){
                                    continue;
                                }
                                $attendance = $attendance_data[$date]['attendance'] ?? '';
                                $remarks = $attendance_data[$date]['remarks'] ?? '';
                                if (!empty($attendance_data[$date]['off_date'])){
                                    $off_date = DateTime::createFromFormat('Y-m-d', $attendance_data[$date]['off_date'])->format('d-m-Y, l');
                                }else{
                                    $off_date = '';
                                }
                                ?>
                                <tr>
                                    <td style="width: 100px;text-align: center;font-size: 12px; color: #777; background-color: #fefefe">
                                        <b><?= DateTime::createFromFormat('Y-m-d', $date)->format('d-M-Y')?></b><br>
                                        <small><?= DateTime::createFromFormat('Y-m-d', $date)->format('(l)')?></small>
                                    </td>
                                    <th style="text-align: center; font-size: 15px!important;padding: 10px;color: #444;background-color: #f5fdfc">
                                        <?="<b>{$attendance}</b>"?>
                                    </th>
                                    <?php
                                    if ($attendance != 'P' && $attendance != 'HD'){
                                        echo '<td class="text-center" colspan="5" style="background-color: rgba(239,242,245,0.38)">';
                                        echo "<b>{$attendance}</b>";
                                        if (!empty($remarks)){
                                            echo " (<small>{$remarks}</small>)";
                                        }
                                        if ($attendance =='OF'){
                                            echo "<br><small>OFF FOR - {$off_date}</small>";
                                        }
                                        if (empty($remarks) && empty($off_date)){
                                            echo "-";
                                        }
                                        echo '</td>';
                                    }else{
                                        foreach ($time_log_type as $log_type){
                                            if ($log_type['id'] != 1 && $log_type['id'] != 8){
                                                continue;
                                            }
                                            ?>
                                            <td class="text-center"
                                                style="font-size: 14px;line-height:1.0em!important;color:#666!important;background-color: rgba(243,245,246,0.15);padding: 5px 1px!important;">
                                                <?php
                                                if (!empty($time_log[$date][$log_type['id']])){
                                                    echo print_punch_time($time_log[$date][$log_type['id']], $log_type['id']);
                                                }else{
                                                    echo '-';
                                                }
                                                ?>
                                            </td>
                                            <?php
                                        }
                                        ?>
                                        <td style="background-color: #e9faf8;font-size: 15px!important;padding: 1px!important;" class="text-center">
                                            <?php
                                            if (!empty($time_log[$date]['total_duration']) && duration_to_time($time_log[$date]['total_duration']) != '00:00'){
                                                echo print_daily_time(duration_to_time($time_log[$date]['total_duration']));
                                            }else{
                                                echo '-';
                                            }
                                            ?>
                                        </td>
                                        <td class="text-center" style="font-size: 13px!important;background-color: #faf6f6">
                                            <?php
                                            if (!empty($time_log[$date]['total_duration']) && duration_to_time($time_log[$date]['total_duration']) != '00:00'){
                                                echo print_daily_diff(duration_to_time($time_log[$date]['total_duration']));
                                            }else{
                                                echo '-';
                                            }
                                            ?>
                                        </td>
                                        <td class="text-center" style="font-size: 13px!important;background-color: #faefef">
                                            <?php
                                            if (!empty($time_log[$date]['total_break']) && duration_to_time($time_log[$date]['total_break']) != '00:00'){
                                                echo print_break_time(duration_to_time($time_log[$date]['total_break']));
                                            }else{
                                                echo '-';
                                            }
                                            ?>
                                        </td>
                                    <?php
                                    }

                                    ?>

                                </tr>
                                <?php
                            }
                            ?>
                            <tr style="background-color: #fcfeff; display: none">
                                <th colspan="4" class="text-right">TOTAL</th>
                                <th>-</th>
                                <th>-</th>
                            </tr>
                        </table>
                        <?php
                    }
                    ?>

                </div>

            </div>
        </div>
    </div>
</div>


<style>
    .nav_switch_tasks, .nav_switch_todo{
        /*border: 0!important;*/
    }
    .nav_switch_tasks.active{
        border: 0!important;
        /*border-top: 4px solid #182738 !important;*/
    }
    .nav_switch_todo.active{
        border: 0!important;
        /*border-top: 4px solid #182738 !important;*/
    }
    .single_task_item:hover, .single_todo_item:hover{
        /*opacity: 0.8;*/
        cursor: pointer;
        padding-right: 1px!important;
        padding-top: 1px!important;
        padding-bottom: 1px!important;
    }
    .single_task_item h5, .single_todo_item h5{
        font-size: 17px;
    }
    td, th{
        padding: 4px!important;
        font-size: 14px!important;
        border-color: rgba(245, 241, 244, 0.81) !important;
    }
    .task_description{
        font-size: 14px;
        overflow: hidden;
        max-width: 100%;
        text-overflow: ellipsis;
        /*width: fit-content;*/
        /*white-space: nowrap;*/
    }
</style>


