<?php
$work_month_year = $work_calendar_month ?? date('m-Y');
$month_details = get_work_calendar_details($work_month_year);

?>
<style>
    .work-calendar {
        width: 100%;
        /*min-width: 300px; !* Limit calendar size *!*/
    }
    .day-cell {
        position: relative;
        padding: 14px 0!important;
        font-size: 16px;
        text-align: center;
        flex: 0 0 14.28%; /* Ensures 7 items per row */
    }
    .day-cell:hover {
        background-color: aliceblue;
        border-radius: 10px;
        cursor: pointer;
        font-weight: bold;
    }
    .day_work_status {
        height: 4px; /* Height of the rounded bar */
        width: 50%; /* Relative width to make it look like a pill */
        position: absolute;
        bottom: 5px; /* Distance from the bottom of the day cell */
        left: 25%; /* Centering the status bar */
        border-radius: 10px; /* Rounded corners */
    }
    .work-updated {
        background-color: rgb(146, 212, 146); /* Green for updated */
    }
    .work-not-updated {
        background-color: rgb(243, 144, 144); /* Light red for not updated */
    }
    .work-partially-updated {
        background-color: rgb(243, 197, 144); /* Light red for not updated */
    }
    .work-not-applicable {
        background-color: rgb(235, 231, 231); /* <PERSON> for not applicable */
    }
    .weekdays{
        font-size: 14px!important;
    }
    .text-prev-day, .text-next-day{
        color: rgb(173, 181, 188);
    }
    .work_status_hint{
        padding:3px;
        border-radius:10px;
        margin:10px;
    }
</style>
<div class="container p-2 pt-0 pb-0">
    <div class="work-calendar shadow-pro">
        <div class="card-header bg-primary text-white text-center row p-2 m-0">
            <span class="col-12 month-name">Job Report - <?= DateTime::createFromFormat('m-Y', $work_month_year)->format('F Y')?></span>
        </div>
        <div class="card-body p-0">
            <div class="weekdays d-flex border-bottom">
                <div class="col p-2 text-center text-danger">Sun</div>
                <div class="col p-2 text-center">Mon</div>
                <div class="col p-2 text-center">Tue</div>
                <div class="col p-2 text-center">Wed</div>
                <div class="col p-2 text-center">Thu</div>
                <div class="col p-2 text-center">Fri</div>
                <div class="col p-2 text-center">Sat</div>
            </div>
            <div class="days d-flex flex-wrap pb-2" id="calendarDays">
                <!-- Days will be populated by JavaScript -->
            </div>
            <div class="p-1 pb-2 pt-2 border-bottom" style="font-size:13px">
                <div class="p-1 d-inline-block">
                    <span class="work_status_hint work-updated"></span> Work Updated
                </div>
                <div class="p-1 d-inline-block">
                    <span class="work_status_hint work-not-updated"></span> Not Updated
                </div>
                <div class="p-1 d-inline-block">
                    <span class="work_status_hint work-partially-updated"></span> Partially Updated
                </div>
                <div class="p-1 d-inline-block">
                    <span class="work_status_hint work-not-applicable"></span> Not Applicable/ Leave
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {

        const daysContainer = document.getElementById('calendarDays');
        const daysInMonth = <?=$month_details['days_in_month']?>; // Total days in the current month (September)
        const startDay = <?=$month_details['start_day']?>; // Sunday = 0, Monday = 1, ..., Saturday = 6
        const previousMonthDays = <?=$month_details['days_in_previous_month']?>; // Number of days in the previous month (August)

        // Example data: Days with updates in the current month
        <?php
            if (isset($work_calendar)){
                ?>
                const updatedDays = <?=json_encode($work_calendar['updated'])?>;
                const notUpdatedDays = <?=json_encode($work_calendar['not_updated'])?>;
                const partialUpdatedDays = <?=json_encode($work_calendar['partially'])?>;
                const notApplicableDays = <?=json_encode($work_calendar['not_applicable'])?>;

                // const updatedDays = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27];
                // const notUpdatedDays = [2, 4, 6, 8, 10, 12, 14, 16];
                // const partialUpdatedDays = [18, 20, 22, 24, 26];
                // const notApplicableDays = [28, 29, 30];
                <?php
            }
        ?>


        // Add days from the previous month to fill the first week
        for (let i = startDay - 1; i >= 0; i--) {
            const dayDiv = document.createElement('div');
            dayDiv.className = 'day-cell col p-2 text-prev-day';
            dayDiv.textContent = previousMonthDays - i;
            daysContainer.appendChild(dayDiv);
        }

        // Add days for the current month
        for (let day = 1; day <= daysInMonth; day++) {
            const dayDiv = document.createElement('div');
            dayDiv.className = 'day-cell col p-2';
            dayDiv.textContent = day;

            const statusSpan = document.createElement('span');
            statusSpan.className = 'day_work_status';

            if (updatedDays.includes(day)) {
                statusSpan.classList.add('work-updated');
            } else if (notUpdatedDays.includes(day)) {
                statusSpan.classList.add('work-not-updated');
            } else if (partialUpdatedDays.includes(day)) {
                statusSpan.classList.add('work-partially-updated');
            }else if (notApplicableDays.includes(day)) {
                statusSpan.classList.add('work-not-applicable');
            }

            dayDiv.appendChild(statusSpan);
            daysContainer.appendChild(dayDiv);
        }

        // Add days from the next month to fill the last week
        const totalCells = daysContainer.children.length;
        const extraCellsNeeded = (7 - (totalCells % 7)) % 7;
        for (let i = 1; i <= extraCellsNeeded; i++) {
            const dayDiv = document.createElement('div');
            dayDiv.className = 'day-cell col p-2 text-next-day';
            dayDiv.textContent = i;
            daysContainer.appendChild(dayDiv);
        }
    });
</script>