<div class="p-4">
    <div class="card bg-white" style="width: 600px">
        <div class="p-3">
            <form>
                <?php
                $tasks = array(
                    "Review and update the comprehensive project plan to ensure all milestones and deliverables are accurately reflected.",
                    "Conduct a thorough analysis of the latest market trends relevant to the project and prepare a detailed report.",
                    "Coordinate with the IT department to resolve any outstanding technical issues affecting project progress.",
                    "Organize a cross-departmental meeting to discuss resource allocation and project timelines.",
                    "Prepare and present a detailed client proposal, including project objectives, timelines, and budget estimates.",
                    "Conduct a risk assessment to identify potential challenges and develop mitigation strategies.",
                    "Review all project-related contracts and agreements to ensure compliance with legal standards and company policies.",
                    "Compile and analyze project performance data to prepare a comprehensive progress report for stakeholders.",
                    "Plan and facilitate a team-building activity to enhance collaboration and morale within the project team.",
                    "Research and propose new tools or technologies that could improve efficiency and effectiveness in project execution."
                );

                for ($i=0;$i<10;$i++){
                    ?>
                    <div class="task_item form-group d-flex align-items-start shadow-pro">
                        <input type="checkbox" class="form-control check_box" name="" id="check_box_<?=$i?>">
                        <label class="form-check-label ml-2" for="check_box_<?=$i?>"><?=$tasks[$i]?></label>
                    </div>
                    <div class="task_splitter"></div>
                    <?php
                }
                ?>
            </form>
        </div>
    </div>
</div>

<style>
    .check_box {
        width: 25px;
        height: 25px;
        margin-top: 2px;
        margin-right: 5px;
    }
    .task_item {
        margin-bottom: 0!important;
        /*margin-top: 15px!important;*/
        background-color: rgba(234, 236, 239, 0.59);
        padding: 5px;
        border-radius: 4px;
        cursor: pointer;
    }
    .task_item:hover{

    }
    .form-check-label {
        margin-left: 5px;
        font-size: 17px;
        line-height: 1.5; /* Adjust the line height to ensure alignment */
        cursor: pointer;
    }
    .d-flex.align-items-start {
        align-items: flex-start; /* Align items to the start of the flex container */
    }
    .task_splitter{
        /*border-bottom: 1px solid #efefef;*/
        margin: 20px;
    }
</style>


