<?php

$user_id = $param1 > 0 ? $param1 : get_user_id();

// get attendance data
$from_date = empty($param2) ? date('2023-09-01') : $param2;
$to_date = empty($param3) ? date('Y-m-d') : $param3;

$dates_array = array_reverse(get_date_array($from_date, $to_date));
$time_log = $this->time_log_m->get_employee_time_log_data($user_id, $from_date, $to_date);
$attendance_data  = $this->attendance_m->get_employee_attendance_data($user_id, $from_date, $to_date);
$time_log_type   = $this->time_log_type_m->get(['status' => 1])->result_array();

$user = $this->users_m->get(['id' => $user_id])->row();
?>
<div class="p-0">
    <div id="download_area_popup">
        <div class="p-3 text-center" style="background-color: #eafef3">
            <?php
            $start_date = DateTime::createFromFormat('Y-m-d', $from_date)->format('d-m-Y');
            $end_date = DateTime::createFromFormat('Y-m-d', $to_date)->format('d-m-Y');
            ?>
            <div style="font-size: 22px; font-weight: bold">
                Time Log Report
                <div style="font-size: 15px" class="text-info"><?=$user->name?> (<?=$user->phone?>)</div>

                <div style="font-size: 13px" class="text-muted">(<?="<b>{$start_date}</b> to <b>{$end_date}</b>"?>)</div>
            </div>
        </div>
        <?php
        if (isset($dates_array) && isset($time_log) && isset($attendance_data) && isset($time_log_type)){
            ?>
            <table class="table table-bordered pop_up_table">
                <tr style="font-size: 12px!important;color: #777!important;text-align: center; background-color: #e7f6fb">
                    <th style="font-size: 11px!important;color: #777!important;">DATE</th>
                    <th style="font-size: 11px!important;color: #777!important;">ATT</th>
                    <?php
                    echo "<th style=\"font-size: 10px!important;color: #777!important;\">".strtoupper('MORNING IN')."</th>";
                    echo "<th style=\"font-size: 10px!important;color: #777!important;\">".strtoupper('PUNCH RECORDS')."</th>";
                    echo "<th style=\"font-size: 10px!important;color: #777!important;\">".strtoupper('EVENING OUT')."</th>";
                    ?>
                    <th style="font-size: 11px!important;color: #777!important;background-color: #b8f1ea">HOURS</th>
                    <th style="font-size: 11px!important;color: #777!important;background-color: #fce7e7">DIFF</th>
                    <th style="font-size: 11px!important;color: #777!important;background-color: #f1b8b8">BREAK</th>
                </tr>
                <?php
                foreach ($dates_array as $date){
                    if (empty($attendance_data[$date]['attendance'])){
                        continue;
                    }
                    $attendance = $attendance_data[$date]['attendance'] ?? '';
                    $remarks = $attendance_data[$date]['remarks'] ?? '';
                    if (!empty($attendance_data[$date]['off_date'])){
                        $off_date = DateTime::createFromFormat('Y-m-d', $attendance_data[$date]['off_date'])->format('d-m-Y, l');
                    }else{
                        $off_date = '';
                    }
                    ?>
                    <tr>
                        <td style="width: 100px;text-align: center;font-size: 12px; color: #777; background-color: #fefefe">
                            <b><?= DateTime::createFromFormat('Y-m-d', $date)->format('d-M-Y')?></b><br>
                            <small><?= DateTime::createFromFormat('Y-m-d', $date)->format('(l)')?></small>
                        </td>
                        <th style="text-align: center; font-size: 15px!important;padding: 10px;color: #444;background-color: #f5fdfc">
                            <?="<b>{$attendance}</b>"?>
                        </th>
                        <?php
                        if ($attendance != 'P' && $attendance != 'HD'){
                            echo '<td class="text-center" colspan="11" style="background-color: rgba(239,242,245,0.38)">';
                            echo "<b>{$attendance}</b>";
                            if (!empty($remarks)){
                                echo " (<small>{$remarks}</small>)";
                            }
                            if ($attendance =='OF'){
                                echo "<br><small>OFF FOR - {$off_date}</small>";
                            }
                            if (empty($remarks) && empty($off_date)){
                                echo "-";
                            }
                            echo '</td>';
                        }else{
                            ?>
                            <td class="text-center text-muted" style="background-color: #f6f7fa; font-weight: bold">
                                <?=print_punch_time($time_log[$date][1], 1);?>
                            </td>
                            <td class="text-center1" style="padding-left: 10px!important;padding-right: 10px!important;font-size: 15px!important;">
                                <?php
                                if (is_array($time_log[$date]['records'])){
                                    foreach ($time_log[$date]['records'] as $time){
                                        if ($time['log_type_id'] != 1 && $time['log_type_id'] != 8 && $time['log_time'] != '00:00:00'){
                                            echo print_punch_time($time['log_time'], $time['log_type_id']).',&nbsp;&nbsp;';
                                        }
                                    }
                                }
                                ?>
                            </td>
                            <td class="text-center text-muted" style="background-color: #f6f7fa; font-weight: bold">
                                <?=print_punch_time($time_log[$date][8], 8);?>
                            </td>
                            <?php

//                            foreach ($time_log_type as $log_type){
//                                if ($log_type['id'] != 1 && $log_type['id'] != 8){
////                                    continue;
//                                }
//                                ?>
<!--                                <td class="text-center"-->
<!--                                    style="font-size: 12px;line-height:1.0em!important;color:#666!important;background-color: rgba(243,245,246,0.15);padding: 5px 1px!important;">-->
<!--                                    --><?php
//                                    if (!empty($time_log[$date][$log_type['id']])){
//                                        echo print_punch_time($time_log[$date][$log_type['id']], $log_type['id']);
//                                    }else{
//                                        echo '-';
//                                    }
//                                    ?>
<!--                                </td>-->
<!--                                --><?php
//                            }
                            ?>
                            <td style="background-color: #e9faf8;font-size: 13px!important;padding: 1px!important;" class="text-center">
                                <?php
                                if (!empty($time_log[$date]['total_duration']) && duration_to_time($time_log[$date]['total_duration']) != '00:00'){
                                    echo print_daily_time(duration_to_time($time_log[$date]['total_duration']));
                                }else{
                                    echo '-';
                                }
                                ?>
                            </td>
                            <td class="text-center" style="font-size: 12px!important;background-color: #faf6f6">
                                <?php
                                if (!empty($time_log[$date]['total_duration']) && duration_to_time($time_log[$date]['total_duration']) != '00:00'){
                                    echo print_daily_diff(duration_to_time($time_log[$date]['total_duration']));
                                }else{
                                    echo '-';
                                }
                                ?>
                            </td>
                            <td class="text-center" style="font-size: 12px!important;background-color: #faefef">
                                <?php
                                if (!empty($time_log[$date]['total_break']) && duration_to_time($time_log[$date]['total_break']) != '00:00'){
                                    echo print_break_time(duration_to_time($time_log[$date]['total_break']));
                                }else{
                                    echo '-';
                                }
                                ?>
                            </td>
                            <?php
                        }

                        ?>

                    </tr>
                    <?php
                }
                ?>
                <tr style="background-color: #fcfeff; display: none">
                    <th colspan="4" class="text-right">TOTAL</th>
                    <th>-</th>
                    <th>-</th>
                </tr>
            </table>
            <?php
        }
        ?>

    </div>
    <div class="text-center p-2">
        <button id="download_image_popup" class="btn btn-primary">Download Image</button>
    </div>
    <div id="canvasContainer">
        <canvas id="canvas_popup" style="display: none"></canvas>
    </div>
    <div id="imageContainer_popup" style="display: none"></div>
</div>

<script>
    $(document).ready(function() {

        $('#download_image_popup').on('click', function() {
            // Select the table you want to convert
            var tableToConvert = document.getElementById('download_area_popup');

            // Convert the table to a canvas
            html2canvas(tableToConvert, {
                width: tableToConvert.offsetWidth,
                height: tableToConvert.offsetHeight
            }).then(function(canvas) {
                // Hide the canvas
                document.getElementById('canvas_popup').style.display = 'none';

                // Convert the canvas to an image
                var image = new Image();
                image.src = canvas.toDataURL('image/png');

                // Append the image to a container
                var imageContainer = document.getElementById('imageContainer_popup');
                imageContainer.innerHTML = ''; // Clear previous content
                imageContainer.appendChild(image);

                // Create a hidden link to trigger automatic download
                var downloadLink = document.createElement('a');
                downloadLink.href = image.src;
                downloadLink.download = 'attendance_report_<?=$_GET['log_date']?>.png'; // Set the desired download filename
                downloadLink.style.display = 'none'; // Hide the link
                imageContainer.appendChild(downloadLink);

                // Simulate a click on the download link
                downloadLink.click();
            });
        });
    });
</script>

<style>
    .nav_switch_tasks{
        border: 0!important;
    }
    .nav_switch_tasks.active{
        border: 0!important;
        border-top: 3px solid #7d9ade !important;
    }
    .nav_switch_todo.active{
        border: 0!important;
        border-top: 3px solid #3bc99f !important;
    }
    .single_task_item:hover, .single_todo_item:hover{
        opacity: 0.6;
        cursor: pointer;
    }
    .single_task_item h5, .single_todo_item h5{
        font-size: 17px;
    }
    .pop_up_table td, .pop_up_table th{
        padding: 4px!important;
        font-size: 12px!important;
        border-color: rgba(245, 241, 244, 0.81) !important;
    }
    .task_description{
        font-size: 14px;
        overflow: hidden;
        max-width: 100%;
        text-overflow: ellipsis;
        /*width: fit-content;*/
        /*white-space: nowrap;*/
    }
</style>