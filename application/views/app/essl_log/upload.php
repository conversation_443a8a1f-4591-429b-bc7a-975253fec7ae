<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>

    </div>
    <div class="mt-3" style="margin:14px;">
        <!-- /.card-header -->
        <div class="">
            <form action="" method="post" enctype="multipart/form-data">
                <div class="p-2">
                    <div class="row text-center bg-white p-4">
                        <div class="col-3">
                            <input type="date" class="form-control" name="start_date" id="start_date">
                        </div>
                        <div class="col-3">
                            <input type="date" class="form-control" name="end_date" id="end_date">
                        </div>
                        <div class="col-3">
                            <input type="file" name="file" class="form-control" style="width: 100%!important; padding:3px;font-size:16px">
                        </div>
                        <div class="col-3">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-check"></i> Upload
                            </button>
                        </div>
                    </div>
                </div>
            </form>


            <div class="row">
                <?php
                if (isset($list_items) && isset($client_list) && isset($project_type) && isset($testers)) {
                    foreach ($list_items as $key => $item) {
                        ?>
                        <div class="col-12 col-lg-4 p-2 ta_search_item">
                            <div class="ribbon-wrapper">
                                <?php
                                if($item['is_delivered']==1){
                                    ?>
                                    <div class="ribbon">
                                        <small>DELIVERED</small>
                                    </div>
                                    <?php
                                }else{
                                    ?>
                                    <div class="ribbon bg-primary">
                                        NEW
                                    </div>
                                    <?php
                                }
                                ?>

                            </div>
                            <div class="bg-white p-4 pt-0 shadow-pro">
                                <h5 style="font-weight: bold; color: #434343"><?= $item['title']?></h5>
                                <small class="text-muted">Created on: <?= DateTime::createFromFormat('Y-m-d H:i:s', $item['created_on'])->format('d-m-Y g:i A')?></small>
                                <div class="">
                                    <small class="text-muted">Client: <b><?= $client_list[$item['client_id']]?></b></small>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-6 text-success">
                                        <small class="text-muted">Project type:</small><br>
                                        <?= $project_type[$item['project_type']]?>
                                    </div>
                                    <div class="col-6 text-info">
                                        <small class="text-muted">Tester:</small><br>
                                        <?= $testers[$item['tester_id']] ?? 'n/a'?>
                                    </div>
                                </div>
                                <hr>
                                <div>
                                    <div class="pt-0 pb-3">
                                        <div class="progress" style="height: 16px;">
                                            <?php
                                            if ($item['tasks']['progress'] == '100'){
                                                $bg_color = 'bg-success';
                                            }elseif ($item['tasks']['progress'] < '75'){
                                                $bg_color = 'bg-danger';
                                            }else{
                                                $bg_color = 'bg-primary';
                                            }
                                            ?>
                                            <div class="progress-bar <?=$bg_color?>" role="progressbar" style="width: <?=$item['tasks']['progress']?>%;" aria-valuenow="<?=$item['tasks']['progress']?>" aria-valuemin="0" aria-valuemax="100"><?=$item['tasks']['progress']?>%</div>
                                        </div>
                                        <div class="row text-center p-2">
                                            <div class="col-3 text-primary" style="background-color: #eaf3fd">
                                                <span style="font-size: 18px; font-weight: bold"><?=$item['tasks']['total']?></span>
                                                <div class="mt-0 pt-0" style="margin-top: -9px!important;">
                                                    <small>Total</small>
                                                </div>
                                            </div>
                                            <div class="col-3 text-danger" style="background-color: #f6e5e6">
                                                <span style="font-size: 18px; font-weight: bold"><?=$item['tasks']['pending']?></span>
                                                <div class="mt-0 pt-0" style="margin-top: -9px!important;">
                                                    <small>Pending</small>
                                                </div>
                                            </div>
                                            <div class="col-3 text-warning" style="background-color: #f8f4ec">
                                                <span style="font-size: 18px; font-weight: bold"><?=$item['tasks']['assigned']?></span>
                                                <div class="mt-0 pt-0" style="margin-top: -9px!important;">
                                                    <small>Assigned</small>
                                                </div>
                                            </div>
                                            <div class="col-3 text-success" style="background-color: #e7faec">
                                                <span style="font-size: 18px; font-weight: bold"><?=$item['tasks']['completed']?></span>
                                                <div class="mt-0 pt-0" style="margin-top: -9px!important;">
                                                    <small>Completed</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                    if (is_super_admin() || is_project_manager()){
                                        ?>
                                        <div class="row">
                                            <div class="col-6 p-1">
                                                <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=projects/edit'); ?>', '<?php echo get_phrase('update_project'); ?>')"
                                                        class="btn btn-outline-info btn-sm w-100">
                                                    <small><i class="fas fa-pencil-alt"></i></small> Edit
                                                </button>
                                            </div>
                                            <div class="col-6 p-1">
                                                <button onclick="confirm_modal('<?=base_url("app/projects/delete/{$item['id']}/");?>')"
                                                        class="btn btn-outline-danger btn-sm w-100">
                                                    <small><i class="fas fa-trash"></i> </small> Delete
                                                </button>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                }
                ?>
            </div>
            <div id="ta_no_result" class="text-center p-2" style="display: none">
                <div class="p-4 shadow_pro bg-white mx-auto" style="">
                    <img width="96" height="96" src="https://img.icons8.com/fluency/96/nothing-found.png" alt="nothing-found"/>
                    <h4 class="text-danger mt-3">No Result Found!</h4>
                </div>
            </div>
        </div>
        <!-- /.card-body -->
    </div>

</div>

