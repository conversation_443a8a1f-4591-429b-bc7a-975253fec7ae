<?php
    $clients = array_column($this->clients_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
    $project_types = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
    
    $testers = $this->users_m->get(['role_id' => 5, 'employee_status' => 1])->result_array();

    $project_leads = $this->users_m->get(['is_project_lead' => 1])->result_array();
    $users = $this->users_m->get(['employee_status' => 1, 'work_assign' => 1])->result_array();

    $teams = $this->teams_m->get()->result_array();
?>
<form class="form-horizontal" action="<?=base_url('app/projects/add/')?>" method="post" enctype="multipart/form-data">
	<div class="row" style="margin: 0!important;">
		<div class="form-group col-6 p-0">
			<label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
			<div class="col-sm-12">
				<input type="text" class="form-control" id="title" name="title" value="" placeholder="Title" required>
			</div>
		</div>
        <div class="form-group col-6 p-0">
			<label for="client_id" class="col-sm-12 col-form-label text-muted">Client <span class="text-danger">*</span></label>
			<div class="col-sm-12">
                <select class="form-control select2" id="client_id" name="client_id" required>
                    <option value="">Choose Client</option>
                    <?php
                        foreach ($clients as $client_id => $client) {
                            echo "<option value='{$client_id}'>{$client}</option>";
                        }
                    ?>
                </select>
			</div>
		</div>
		<?php 
            if(has_permission('projects/edit')){
                ?>
                <div class="form-group col-6 p-0">
                    <label for="tester_id" class="col-sm-12 col-form-label text-muted">Tester <span class="text-danger">*</span></label>
                    <div class="col-sm-12">
                        <select class="form-control select2" id="tester_id" name="tester_id" required>
                            <option value="">Choose Tester</option>
                            <?php
                                foreach ($testers as $tester) {
                                    echo "<option value='{$tester['id']}'>{$tester['name']}</option>";
                                }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="form-group col-6 p-0">
                    <label for="project_lead_id" class="col-sm-12 col-form-label text-muted">Project Head <span class="text-danger">*</span></label>
                    <div class="col-sm-12">
                        <select class="form-control select2" id="project_lead_id" name="project_lead_id" required>
                            <option value="">Choose Project Head</option>
                            <?php
                                foreach ($project_leads as $project_lead) {
                                    echo "<option value='{$project_lead['id']}'>{$project_lead['name']}</option>";
                                }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="form-group col-12 p-0">
                    <label for="project_teams" class="col-sm-12 col-form-label text-danger">Project Teams</label>
                    <div class="col-sm-12">
                        <select class="form-control select2" id="project_teams" name="project_teams[]" multiple>
                            <?php
                            foreach ($teams as $team) {
                                echo "<option value='{$team['id']}'>{$team['title']}</option>";
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="form-group col-6 p-0">
                    <label for="project_type" class="col-sm-12 col-form-label text-muted">Project Type <span class="text-danger">*</span></label>
                    <div class="col-sm-12">
                        <select class="form-control select2" id="project_type" name="project_type" required>
                            <option value="">Choose Type</option>
                            <?php
                            foreach ($project_types as $project_types_id => $project_type) {
                                echo "<option value='{$project_types_id}'>{$project_type}</option>";
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="col-12 p-2">
                    <hr>
                </div>
                <div class="form-group col-4 p-0">
                    <label for="start_date" class="col-sm-12 col-form-label text-muted">Start Date</label>
                    <div class="col-sm-12">
                        <input type="date" class="form-control" id="start_date" name="start_date" value="" placeholder="Start Date">
                    </div>
                </div>
                <div class="form-group col-4 p-0">
                    <label for="project_date" class="col-sm-12 col-form-label text-muted">Project Date</label>
                    <div class="col-sm-12">
                        <input type="date" class="form-control" id="project_date" name="project_date" value="" placeholder="Project Date">
                    </div>
                </div>
                <div class="form-group col-12 p-0 pt-1">
                    <div class="col-sm-12">
                        <input type="checkbox" class="" id="is_delivered" name="is_delivered" value="1" style="width:19px!important; height:19px!important">
                        <label for="is_delivered" class="text-muted">Is Delivered?</label>
                    </div>
                </div>
                <?php
            }
        ?>
        <div class="form-group col-12 p-0 mt-2">
			<label for="description" class="col-sm-12 col-form-label text-muted">Description </label>
			<div class="col-sm-12">
				<input type="text" class="form-control" id="description" name="description" value="" placeholder="Description" >
			</div>
		</div>
        <div class="form-group col-6 p-0">
            <label for="phone" class="col-sm-12 col-form-label text-muted">Phone</label>
            <div class="col-sm-12">
                <input type="number" class="form-control" id="phone" name="phone" value="" placeholder="Phone">
            </div>
        </div>
        <div class="form-group col-6 p-0">
            <label for="email" class="col-sm-12 col-form-label text-muted">Email</label>
            <div class="col-sm-12">
                <input type="email" class="form-control" id="email" name="email" value="" placeholder="Email">
            </div>
        </div>
        <div class="col-12">
            <hr>
        </div>
        <div class="form-group col-12 p-0 row">
            <div class="col-sm-3">
                <label for="url_web" class="col-sm-12 col-form-label text-muted">URL WEB</label>
            </div>
            <div class="col-sm-9">
                <input type="url" class="form-control" id="url_web" name="url_web" value="" placeholder="URL WEB">
            </div>
        </div>
        <div class="form-group col-12 p-0 row">
            <div class="col-sm-3">
                <label for="url_android" class="col-sm-12 col-form-label text-muted">URL ANDROID</label>
            </div>
            <div class="col-sm-9">
                <input type="url" class="form-control" id="url_android" name="url_android" value="" placeholder="URL ANDROID">
            </div>
        </div>
        <div class="form-group col-12 p-0 row">
            <div class="col-sm-3">
                <label for="url_ios" class="col-sm-12 col-form-label text-muted">URL IOS</label>
            </div>
            <div class="col-sm-9">
                <input type="url" class="form-control" id="url_ios" name="url_ios" value="" placeholder="URL IOS">
            </div>
        </div>


	</div>

	<div class="col-12" >
		<button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
			<small><i class="fa fa-check"></i></small> Save
		</button>
	</div>
</form>


<script type="application/javascript">
	$('.select2').select2();

</script>
