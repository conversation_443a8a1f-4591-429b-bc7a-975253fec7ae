<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <?php
        if (has_permission('projects/add')){
            ?>
            <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=projects/add', '<?= get_phrase('add_project'); ?>')"
                    class="btn btn-primary btn-mini float-right btn-round">
                <small><i class="fas fa-plus"></i></small> Add <?= $page_title ?? '' ?>
            </button>
            <?php
        }
        ?>

    </div>
    <div class="mt-3" style="margin:14px;">
        <!-- /.card-header -->
        <div class="">
            <div class="p-2">
                <div class="row text-center bg-white p-2" style="border-radius: 10px">
                    <?php
                    if (isset($status_count)){
                        ?>
                        <div class="col-6 col-lg-3 p-1 pb-0">
                            <div class="shadow_pro bg-white p-2 pb-0">
                                <div class="text-primary p-2" style="background-color: #eaf3fd;border-radius: 8px;">
                                    <span style="font-size: 33px; font-weight: bold"><?=count($list_items ?? [])?></span>
                                    <div class="mt-0 pt-0" style="margin-top: -5px!important;font-size:18px;">
                                        TOTAL
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 col-lg-3 p-1 pb-0">
                            <div class="shadow_pro bg-white p-2 pb-0">
                                <div class="text-danger p-2" style="background-color: #f6e5e6;border-radius: 8px;">
                                    <span style="font-size: 33px; font-weight: bold"><?=$status_count['pending_count']?></span>
                                    <div class="mt-0 pt-0" style="margin-top: -5px!important;font-size:18px;">
                                        EXPIRED
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 col-lg-3 p-1 pb-0">
                            <div class="shadow_pro bg-white p-2 pb-0">
                                <div class="text-warning p-2" style="background-color: #f8f4ec;border-radius: 8px;">
                                    <span style="font-size: 33px; font-weight: bold"><?=$status_count['assigned_count']?></span>
                                    <div class="mt-0 pt-0" style="margin-top: -5px!important;font-size:18px;">
                                        THIS WEEK
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 col-lg-3 p-1 pb-0">
                            <div class="shadow_pro bg-white p-2 pb-0">
                                <div class="text-success p-2" style="background-color: #e7faec;border-radius: 8px;">
                                    <span style="font-size: 33px; font-weight: bold"><?=$status_count['completed_count']?></span>
                                    <div class="mt-0 pt-0" style="margin-top: -5px!important;font-size:18px;">
                                        UPCOMING
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                    ?>
                    <div class="p-2 m-0 pb-2 pt-0 col-12">
                        <input type="text" class="form-control form-control-lg" id="ta_search_list" placeholder="Search Projects.." style="width: 100%!important; padding:25px;font-size:18px;border-radius: 40px;">
                    </div>
                </div>
            </div>


            <div class="row">
                <?php
                if (isset($list_items) && isset($client_list) && isset($project_type) && isset($users) && isset($teams)) {
                    foreach ($list_items as $key => $item) {
                        ?>
                        <div class="col-12 col-lg-4 p-2 ta_search_item">
                            <div class="ribbon-wrapper">
                                <?php
                                if($item['is_delivered']==1){
                                    ?>
                                    <div class="ribbon">
                                        <small>DELIVERED</small>
                                    </div>
                                    <?php
                                }else{
                                    ?>
                                    <div class="ribbon bg-info">
                                        NEW
                                    </div>
                                    <?php
                                }
                                ?>

                            </div>
                            <div class="bg-white p-4 pt-0 shadow-pro" style="border-radius: 10px;">
                                <h5 style="font-weight: bold; color: #434343;margin-bottom: 0px!important;"><?= $item['title']?></h5>
                                <small class="text-muted" style="font-size: 10px!important;">Created on: <?= DateTime::createFromFormat('Y-m-d H:i:s', $item['created_on'])->format('d-m-Y g:i A')?></small>
                                <div class="">
                                    <small class="text-muted"><b><?= $client_list[$item['client_id']]?> [<?= $project_type[$item['project_type']]?>]</b></small>
                                </div>
                                <div class="">
                                    <small class="text-muted">Tester:</small>
                                    <span class="text-info">
                                        <?= $users[$item['tester_id']] ?? 'n/a'?>
                                    </span>
                                </div>
                                <div class="pt-3 pb-3">
                                    <span class="text-info"><b>
                                        <?php 
                                        $project_teams = json_decode($item['project_teams'], true);
                                        if(is_array($project_teams)){
                                            
                                            foreach ($project_teams as $team) {
                                                echo "<div class=\"bg-info-lighten text-info text-center p-1\" style=\"height:30px\">";
                                                echo $teams[$team].'<br>';
                                                echo "</div>";
                                            }
                                        }else{
                                            ?>
                                            <div class="bg-danger-lighten text-danger text-center p-2" style="height:60px">NO TEAMS ASSIGNED</div>
                                            <?php
                                        }
                                            
                                        ?>
                                        </b>
                                    </span>
                                </div>
                                <div>
                                    <div class="pt-0 pb-3">
                                        <div class="progress" style="height: 12px;border-radius: 40px;">
                                            <?php
                                            if ($item['tasks']['progress'] < '75'){
                                                $bg_color = 'bg-danger';
                                            }else{
                                                $bg_color = 'bg-success';
                                            }
                                            ?>
                                            <div class="progress-bar <?=$bg_color?>" role="progressbar" style="width: <?=$item['tasks']['progress']?>%;" aria-valuenow="<?=$item['tasks']['progress']?>" aria-valuemin="0" aria-valuemax="100"><?=$item['tasks']['progress']?>%</div>
                                        </div>
                                        <div class="row text-center p-2">
                                            <div class="col-4 p-1">
                                                <div class="text-danger" style="background-color: #f6e5e6;border-radius: 5px;">
                                                    <span style="font-size: 16px; font-weight: bold"><?=$item['tasks']['pending']?></span>
                                                    <div class="mt-0 pt-0" style="margin-top: -9px!important;">
                                                        <small style="font-size: 12px;">Pending</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-4 p-1">
                                                <div class="text-warning" style="background-color: #f8f4ec; border-radius: 5px;">
                                                    <span style="font-size: 16px; font-weight: bold"><?=$item['tasks']['assigned']?></span>
                                                    <div class="mt-0 pt-0" style="margin-top: -9px!important;">
                                                        <small style="font-size: 12px;">Assigned</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-4 p-1">
                                                <div class="text-success" style="background-color: #e7faec; border-radius: 5px;">
                                                    <span style="font-size: 16px; font-weight: bold"><?=$item['tasks']['completed']?></span>
                                                    <div class="mt-0 pt-0" style="margin-top: -9px!important;">
                                                        <small style="font-size: 12px;">Completed</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                    if (is_super_admin() || is_project_manager()){
                                        ?>
                                        <div class="row">
                                            <div class="col-4 p-1">
                                                <a href="<?=base_url('app/project_schedule/?project_id='.$item['id'])?>"
                                                   class="btn btn-outline-primary btn-sm w-100 btn-round">
                                                    <small><i class="fas fa-calendar"></i></small> Schedule
                                                </a>
                                            </div>
                                            <div class="col-4 p-1">
                                                <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=projects/edit'); ?>', '<?php echo get_phrase('update_project'); ?>')"
                                                        class="btn btn-outline-info btn-sm w-100 btn-round">
                                                    <small><i class="fas fa-pencil-alt"></i></small> Edit
                                                </button>
                                            </div>
                                            <div class="col-4 p-1">
                                                <button onclick="confirm_modal('<?=base_url("app/projects/delete/{$item['id']}/");?>')"
                                                        class="btn btn-outline-danger btn-sm w-100 btn-round">
                                                    <small><i class="fas fa-trash"></i> </small> Delete
                                                </button>
                                            </div>
                                        </div>
                                        <?php
                                    }else{
                                        ?>
                                        <div class="row">
                                            <div class="col-12 p-1">
                                                <a href="<?=base_url('app/project_schedule/?project_id='.$item['id'])?>"
                                                   class="btn btn-outline-primary btn-sm w-100 btn-round">
                                                    <small><i class="fas fa-calendar"></i></small> Project Schedule
                                                </a>
                                            </div>
                                        </div>
                                            <?php
                                            if(is_technical_support()){
                                                ?>
                                                <div class="col-4 p-1">
                                                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=projects/edit'); ?>', '<?php echo get_phrase('update_project'); ?>')"
                                                            class="btn btn-outline-info btn-sm w-100 btn-round">
                                                        <small><i class="fas fa-pencil-alt"></i></small> Edit
                                                    </button>
                                                </div>
                                                <?php
                                            }
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                }
                ?>
            </div>
            <div id="ta_no_result" class="text-center p-2" style="display: none">
                <div class="p-4 shadow_pro bg-white mx-auto" style="">
                    <img width="96" height="96" src="https://img.icons8.com/fluency/96/nothing-found.png" alt="nothing-found"/>
                    <h4 class="text-danger mt-3">No Result Found!</h4>
                </div>
            </div>
        </div>
        <!-- /.card-body -->
    </div>

</div>

