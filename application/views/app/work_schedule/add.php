<?php
$projects = $this->db->select(['id', 'title'])->get('projects')->result_array();
$users = $this->db->select(['id', 'name'])->order_by('name', 'asc')->get_where('users', ['work_assign' => 1])->result_array();
?>
<form class="form-horizontal p-0 m-0" action="<?=base_url('app/work_schedule/add/')?>" method="post" enctype="multipart/form-data">
    <div class="row p-0" style="margin: 0!important;">
        <div class="form-group col-12">
            <label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="title" name="title" value="" placeholder="Title" required>
            </div>
        </div>
        <div class="form-group col-12">
            <label for="description" class="col-sm-12 col-form-label text-muted">Description</label>
            <div class="col-sm-12">
                <textarea class="form-control" id="description" name="description" placeholder="Description"></textarea>
            </div>
        </div>
        <div class="form-group col-12">
            <label for="project_id" class="col-sm-12 col-form-label text-muted">Project</label>
            <div class="col-sm-12">
                <select class="form-control select2" id="project_id" name="project_id" required>
                    <option value="">Choose Project</option>
                    <?php
                        foreach($projects as $project){
                            echo "<option value=\"{$project['id']}\">{$project['title']}</option>";
                        }
                    ?>
                </select>
            </div>
        </div>
        <div class="form-group col-12">
            <label for="user_id" class="col-sm-12 col-form-label text-muted">Users</label>
            <div class="col-sm-12">
                <select class="form-control select2" id="user_id" name="user_id[]" multiple required>
                    <?php
                        foreach($users as $user){
                            echo "<option value=\"{$user['id']}\">{$user['name']}</option>";
                        }
                    ?>
                </select>
            </div>
        </div>
        <div class="form-group col-12">
            <label for="date" class="col-sm-12 col-form-label text-muted">Choose Date</label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="date" name="date" value="" placeholder="Choose Date" required>
            </div>
        </div>

    </div>

    <div class="col-12 mt-2" >
        <button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
            <small><i class="fa fa-check"></i></small> Save
        </button>
    </div>
</form>

<script type="text/javascript">
    $('.select2').select2();
</script>
<style type="text/css">
    .form-control{
        font-size: 16px;
    }
</style>