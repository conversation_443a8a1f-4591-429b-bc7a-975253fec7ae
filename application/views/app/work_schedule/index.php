<div class="container-fluid pt-0 mt-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2 bg-primary">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
    </div>
    <div class="p-2">
        <form action="" method="get">
            <div class="shadow-pro bg-white p-2 row" style="margin:10px;max-width: 900px">
                <div class="col-lg-3 col-sm-12 p-1">
                    <input type="date" class="form-control w-100" name="start_date" id="start_date" style="font-size: 17px;"  value="<?=$_GET['start_date']?>">
                </div>
                <div class="col-lg-3 col-sm-12 p-1">
                    <input type="date" class="form-control w-100" name="end_date" id="end_date" style="font-size: 17px"  value="<?=$_GET['end_date']?>">
                </div>
                <div class="col-lg-3 col-sm-12 p-1">
                    <button type="submit" class="btn btn-outline-primary" style="width: 150px;"><i class="bi bi-filter-circle"></i> Filter</button>
                </div>
                <div class="col-lg-3 col-sm-12 p-1">
                    <button type="button" onclick="show_ajax_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=work_schedule/add', '<?= get_phrase('add_work_schedule'); ?>')"
                            class="btn btn-success w-100"><i class="bi bi-plus-circle"></i> Add Schedule</button>
                </div>
            </div>
        </form>
    </div>
    <form action="" method="post">
        <div class="mt-3" style="margin:14px;max-width: 900px" id="download_area">
            <div class="shadow-pro pt-2 pb-2 bg-white p-1" >
                <div class="row p-2 m-0 mb-2" style="background-image: linear-gradient(90deg, #23384E, #166686) !important;border-radius: 5px">
                    <div class="col-6 pt-1">
                        <img src="<?=base_url('assets/logo/logo.png')?>" style="width: 130px;height: auto">
                    </div>
                    <div class="col-6 text-right">
                        <div style="font-size: 18px; color: #a0ccf5" class="pb-1">pms.trogon.info</div>
                        <div style="font-size: 18px; color: #ffffff">
                            <?= DateTime::createFromFormat('Y-m-d', $_GET['start_date'])->format('d-M, Y')?> <small>to</small>
                            <?= DateTime::createFromFormat('Y-m-d', $_GET['end_date'])->format('d-M, Y')?>
                        </div>
                    </div>
                </div>
                <?php
                if (isset($users) && isset($projects) && isset($work_schedule)){
                    $key = 0;
                    foreach ($projects as $project_id => $project_title){
                        $project_schedule = $work_schedule[$project_id];
                        ?>
                        <div class="p-2">
                            <div class="project_card p-2">
                                <div class="project_title p-2">
                                    <?=$project_title?>
                                </div>
                                <div class="schedule_list">
                                    <?php
                                        foreach ($project_schedule as $item){
                                            if ($item['date']==date('Y-m-d')){
                                                $date_color = 'bg-danger-lighten';
                                            }elseif ($item['date'] < date('Y-m-d')){
                                                $date_color = 'bg-danger';
                                            }else{
                                                $date_color = 'bg-info-lighten';
                                            }
                                            $schedule_date = DateTime::createFromFormat('Y-m-d', $item['date'])->format('d-m-Y (l)');
                                            $schedule_users = json_decode($item['user_id'], true);
                                            ?>
                                            <div class="pt-2 pb-2">
                                                <div class="schedule_item bg-white p-2" style="border-radius: 5px;">
                                                    <div class="">
                                                        <span class="badge <?=$date_color?> p-2" style="font-size: 14px;"><?=$schedule_date?></span>
                                                        <button onclick="confirm_modal('<?=base_url("app/work_schedule/delete/{$item['id']}/");?>')" type="button" class="btn btn-sm btn-outline-danger float-right ml-1 mr-1" style="width: 100px">Delete</button>
                                                        <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=work_schedule/edit'); ?>', '<?php echo get_phrase('update_work_schedule'); ?>')"
                                                                type="button" class="btn btn-sm btn-outline-info float-right ml-1 mr-1" style="width: 100px">Edit</button>
                                                    </div>
                                                    <div style="font-size: 18px;background-color: aliceblue;" class="p-2 mt-3"><?=$item['title']?></div>
                                                    <div class="pt-2">
                                                        <?php
                                                        foreach ($schedule_users as $user_id){
                                                            ?>
                                                            <div class="schedule_user d-inline-block bg-danger-lighten m-1" style="border-radius: 5px; padding: 5px 10px">
                                                                <i class="bi bi-person-circle"></i> <?= $users[$user_id] ?? '';?>
                                                            </div>
                                                            <?php
                                                        }
                                                        ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php
                                        }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                }
                ?>
            </div>
        </div>

        <div class="text-center p-2" style="max-width: 900px;">
            <button type="submit" name="submit" value="download" class="btn btn-primary">Download PDF</button>
        </div>
    </form>
</div>

<script type="text/javascript">
    <?php
    if ($_GET['user_id'] > 0){
    $user = $this->db->get_where('users', ['id' => $_GET['user_id']])->row();
    ?>
    $( document ).ready(function() {
        show_ajax_modal('<?= site_url('app/modal/popup/get/'.$user->id.'/?page_name=tasks_report/task_list_employee'); ?>', '<?= "[{$user->name}] - Assigned Tasks" ?>')
    });
    <?php
    }
    ?>
    function get_value(date){
        window.location.href='<?=base_url('app/work_assign/index/?job_date=')?>' + date;
    }
</script>

<style>
    .project_card{
        background-color: #f0f4f5;
        border-radius: 4px;
    }
    .project_title{
        font-size: 19px; !important;
        background-color: rgba(0, 0, 0, 0.08);
        border-radius: 5px;
        color: rgba(15, 102, 41, 0.8);
        font-weight: 600;
        letter-spacing: 0.5px;

    }
    .schedule_list{
    }
</style>


