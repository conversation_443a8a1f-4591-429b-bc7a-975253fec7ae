<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>

    </div>
    <div class="card card-primary row mt-3 shadow-pro" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title"><?= strtoupper($page_title ?? '')?></h3>
        </div>
        <!-- /.card-header -->
        <div class="card-body">
            <table id="example3" class="table table-bordered table-striped">
                <thead>
                <tr>
                    <th>#</th>
                    <th>Project</th>
                    <th>Tasks Pending</th>
                    <th>High Priority</th>
                    <th>Medium Priority</th>
                    <th>Low Priority</th>
                </tr>
                </thead>
                <tbody>
                <?php
                if (isset($list_items)) {
                    foreach ($list_items as $key => $item) {
                        ?>
                        <tr>
                            <td><?= $key + 1 ?></td>
                            <td><?= $item['title']?></td>
                            <td style="font-size: 17px;"><?= $item['task_count'] > 0 ? $item['task_count']  : 'No Pending'?></td>
                            <td style="font-size: 18px;"><?= $item['priority_high'] > 0 ? $item['priority_high']  : '-'?></td>
                            <td style="font-size: 18px;"><?= $item['priority_medium'] > 0 ? $item['priority_medium']  : '-'?></td>
                            <td style="font-size: 18px;"><?= $item['priority_low'] > 0 ? $item['priority_low']  : '-'?></td>
                        </tr>
                        <?php
                    }
                }
                ?>

                </tbody>

            </table>
        </div>
        <!-- /.card-body -->
    </div>
</div>

