<form class="form-horizontal" action="<?= base_url('app/project_assign/bulk_assign') ?>" method="post">
    <div class="row" style="margin: 0!important;">
        <div class="form-group col-md-6">
            <label for="job_date" class="col-form-label text-muted">Assignment Date <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="job_date" name="job_date" value="<?= date('Y-m-d') ?>" required>
            </div>
        </div>
        
        <div class="form-group col-md-6">
            <label for="project_id" class="col-form-label text-muted">Project <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="project_id" name="project_id" required>
                    <option value="">Select Project</option>
                    <?php foreach ($projects as $id => $title): ?>
                        <option value="<?= $id ?>"><?= $title ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        
        <div class="form-group col-md-12">
            <label for="user_ids" class="col-form-label text-muted">Select Employees <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="user_ids" name="user_ids[]" multiple required>
                    <?php foreach ($users as $user): ?>
                        <option value="<?= $user['id'] ?>"><?= $user['name'] ?></option>
                    <?php endforeach; ?>
                </select>
                <small class="form-text text-muted">Hold Ctrl/Cmd to select multiple employees</small>
            </div>
        </div>
        
        <div class="form-group col-md-6">
            <label for="status" class="col-form-label text-muted">Status <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control" id="status" name="status" required>
                    <option value="pending">Pending</option>
                    <option value="in_progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
        </div>
        
        <div class="form-group col-md-12">
            <label for="remarks" class="col-form-label text-muted">Remarks</label>
            <div class="col-sm-12">
                <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Enter remarks"></textarea>
            </div>
        </div>
        
        <div class="col-12">
            <button type="submit" class="btn btn-primary float-right">
                <i class="fas fa-save"></i> Assign to Selected Employees
            </button>
        </div>
    </div>
</form>

<script>
$(document).ready(function() {
    $('.select2').select2({
        width: '100%'
    });
});
</script>
