<?php
$item_id = $param1;
$assignment = $this->project_assign_m->get_assignment($item_id);
?>

<div class="row" style="margin: 0!important;">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">Assignment Details</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 40%">Assignment ID</th>
                                <td><?= $assignment['id'] ?></td>
                            </tr>
                            <tr>
                                <th>Assignment Date</th>
                                <td><?= date('d M Y', strtotime($assignment['job_date'])) ?></td>
                            </tr>
                            <tr>
                                <th>Employee</th>
                                <td><?= $assignment['user_name'] ?></td>
                            </tr>
                            <tr>
                                <th>Project</th>
                                <td><?= $assignment['project_title'] ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 40%">Status</th>
                                <td>
                                    <?php
                                    $status_class = '';
                                    switch ($assignment['status']) {
                                        case 'pending':
                                            $status_class = 'badge badge-warning';
                                            break;
                                        case 'in_progress':
                                            $status_class = 'badge badge-info';
                                            break;
                                        case 'completed':
                                            $status_class = 'badge badge-success';
                                            break;
                                        case 'cancelled':
                                            $status_class = 'badge badge-danger';
                                            break;
                                    }
                                    ?>
                                    <span class="<?= $status_class ?>"><?= ucfirst(str_replace('_', ' ', $assignment['status'])) ?></span>
                                </td>
                            </tr>
                            <tr>
                                <th>Created By</th>
                                <td><?= $assignment['creator_name'] ?></td>
                            </tr>
                            <tr>
                                <th>Created On</th>
                                <td><?= date('d M Y H:i', strtotime($assignment['created_on'])) ?></td>
                            </tr>
                            <tr>
                                <th>Last Updated</th>
                                <td>
                                    <?= !empty($assignment['updated_on']) ? date('d M Y H:i', strtotime($assignment['updated_on'])) . ' by ' . $assignment['updater_name'] : 'Not updated yet' ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">Remarks</h6>
                            </div>
                            <div class="card-body">
                                <?= !empty($assignment['remarks']) ? nl2br($assignment['remarks']) : '<em>No remarks provided</em>' ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="text-right">
                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item_id.'/?page_name=project_assign/edit'); ?>', '<?= get_phrase('edit_assignment'); ?>')"
                            class="btn btn-info">
                        <i class="fas fa-pencil-alt"></i> Edit
                    </button>
                    <a href="<?= site_url('app/project_assign/index'); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
