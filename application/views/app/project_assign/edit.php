<?php
$item_id = $param1;
$assignment = $this->project_assign_m->get_assignment($item_id);
?>

<form class="form-horizontal" action="<?= base_url('app/project_assign/edit/'.$item_id) ?>" method="post">
    <div class="row" style="margin: 0!important;">
        <div class="form-group col-md-6">
            <label for="job_date" class="col-form-label text-muted">Assignment Date <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="job_date" name="job_date" value="<?= $assignment['job_date'] ?>" required>
            </div>
        </div>
        
        <div class="form-group col-md-6">
            <label for="user_id" class="col-form-label text-muted">Employee <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="user_id" name="user_id" required>
                    <option value="">Select Employee</option>
                    <?php foreach ($users as $id => $name): ?>
                        <option value="<?= $id ?>" <?= $assignment['user_id'] == $id ? 'selected' : '' ?>><?= $name ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        
        <div class="form-group col-md-6">
            <label for="project_id" class="col-form-label text-muted">Project <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="project_id" name="project_id" required>
                    <option value="">Select Project</option>
                    <?php foreach ($projects as $id => $title): ?>
                        <option value="<?= $id ?>" <?= $assignment['project_id'] == $id ? 'selected' : '' ?>><?= $title ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        
        <div class="form-group col-md-6">
            <label for="status" class="col-form-label text-muted">Status <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control" id="status" name="status" required>
                    <option value="pending" <?= $assignment['status'] == 'pending' ? 'selected' : '' ?>>Pending</option>
                    <option value="in_progress" <?= $assignment['status'] == 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                    <option value="completed" <?= $assignment['status'] == 'completed' ? 'selected' : '' ?>>Completed</option>
                    <option value="cancelled" <?= $assignment['status'] == 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                </select>
            </div>
        </div>
        
        <div class="form-group col-md-12">
            <label for="remarks" class="col-form-label text-muted">Remarks</label>
            <div class="col-sm-12">
                <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Enter remarks"><?= $assignment['remarks'] ?></textarea>
            </div>
        </div>
        
        <div class="col-12">
            <button type="submit" class="btn btn-primary float-right">
                <i class="fas fa-save"></i> Update Assignment
            </button>
        </div>
    </div>
</form>

<script>
$(document).ready(function() {
    $('.select2').select2({
        width: '100%'
    });
});
</script>
