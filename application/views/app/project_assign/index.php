<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <div class="float-right">
            <a href="<?= base_url('app/project_assign/daily_assign'); ?>" class="btn btn-info btn-mini mr-2">
                <i class="fas fa-calendar-day"></i> Daily Assignment
            </a>
            <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=project_assign/add', '<?= get_phrase('add_project_assignment'); ?>')"
                    class="btn btn-primary btn-mini">
                <i class="fas fa-plus"></i> Add Assignment
            </button>
            <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=project_assign/bulk_assign', '<?= get_phrase('bulk_assign_projects'); ?>')"
                    class="btn btn-success btn-mini ml-2">
                <i class="fas fa-users"></i> Bulk Assign
            </button>
        </div>
    </div>

    <div class="card card-primary shadow-pro" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title"><?= strtoupper($page_title ?? '') ?></h3>
        </div>

        <!-- Filter Section -->
        <div class="card-body border-bottom">
            <form action="" method="get" class="mb-0">
                <div class="row align-items-end">
                    <div class="col-md-3 mb-2">
                        <label for="start_date" class="form-label text-muted">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?? '' ?>">
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="end_date" class="form-label text-muted">End Date</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?? '' ?>">
                    </div>
                    <div class="col-md-2 mb-2">
                        <label for="user_id" class="form-label text-muted">Employee</label>
                        <select class="form-control select2" id="user_id" name="user_id">
                            <option value="all" <?= isset($selected_user_id) && $selected_user_id == 'all' ? 'selected' : '' ?>>All Employees</option>
                            <?php foreach ($users as $id => $name): ?>
                                <option value="<?= $id ?>" <?= isset($selected_user_id) && $selected_user_id == $id ? 'selected' : '' ?>>
                                    <?= $name ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <label for="project_id" class="form-label text-muted">Project</label>
                        <select class="form-control select2" id="project_id" name="project_id">
                            <option value="all" <?= isset($selected_project_id) && $selected_project_id == 'all' ? 'selected' : '' ?>>All Projects</option>
                            <?php foreach ($projects as $id => $title): ?>
                                <option value="<?= $id ?>" <?= isset($selected_project_id) && $selected_project_id == $id ? 'selected' : '' ?>>
                                    <?= $title ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-filter"></i> Filter</button>
                        <a href="<?= site_url('app/project_assign/index') ?>" class="btn btn-outline-secondary ml-2"><i class="fas fa-sync"></i> Reset</a>
                    </div>
                </div>
            </form>
        </div>

        <div class="card-body">
            <table id="assignments-table" class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="10%">Date</th>
                        <th width="20%">Employee</th>
                        <th width="25%">Project</th>
                        <th width="10%">Status</th>
                        <th width="15%">Created By</th>
                        <th width="15%">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (isset($assignments) && count($assignments) > 0): ?>
                        <?php foreach ($assignments as $key => $item): ?>
                            <tr>
                                <td><?= $key + 1 ?></td>
                                <td><?= date('d M Y', strtotime($item['job_date'])) ?></td>
                                <td><?= $item['user_name'] ?></td>
                                <td><?= $item['project_title'] ?></td>
                                <td>
                                    <?php
                                    $status_class = '';
                                    switch ($item['status']) {
                                        case 'pending':
                                            $status_class = 'badge badge-warning';
                                            break;
                                        case 'in_progress':
                                            $status_class = 'badge badge-info';
                                            break;
                                        case 'completed':
                                            $status_class = 'badge badge-success';
                                            break;
                                        case 'cancelled':
                                            $status_class = 'badge badge-danger';
                                            break;
                                    }
                                    ?>
                                    <span class="<?= $status_class ?>"><?= ucfirst(str_replace('_', ' ', $item['status'])) ?></span>
                                </td>
                                <td><?= $item['creator_name'] ?></td>
                                <td>
                                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=project_assign/view'); ?>', '<?= get_phrase('view_assignment'); ?>')"
                                            class="btn btn-primary btn-sm" title="View">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=project_assign/edit'); ?>', '<?= get_phrase('edit_assignment'); ?>')"
                                            class="btn btn-info btn-sm" title="Edit">
                                        <i class="fas fa-pencil-alt"></i>
                                    </button>
                                    <a href="<?= site_url('app/project_assign/delete/'.$item['id']); ?>"
                                       class="btn btn-danger btn-sm"
                                       onclick="return confirm('Are you sure you want to delete this assignment?')"
                                       title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="7" class="text-center">No assignments found</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#assignments-table').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "buttons": ["copy", "csv", "excel", "pdf", "print"],
        "pageLength": 25,
        "order": [[1, "desc"]]
    }).buttons().container().appendTo('#assignments-table_wrapper .col-md-6:eq(0)');

    // Initialize select2
    $('.select2').select2({
        width: '100%'
    });
});
</script>
