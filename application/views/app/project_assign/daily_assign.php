<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <div class="float-right">
            <form class="form-inline">
                <div class="form-group mr-2">
                    <label for="job_date" class="mr-2">Date:</label>
                    <input type="date" id="job_date" name="job_date" class="form-control" value="<?= $job_date ?>" onchange="changeDate(this.value)">
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-pro">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Daily Project Assignment - <?= date('d M Y (l)', strtotime($job_date)) ?></h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Assign projects to employees. Changes are saved automatically.
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th style="width: 5%">#</th>
                                    <th style="width: 25%">Employee</th>
                                    <th style="width: 50%">Projects</th>
                                    <th style="width: 20%">Remarks</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $index => $user): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <td>
                                            <strong><?= $user['name'] ?></strong>
                                            <?php if (!empty($user['employee_code'])): ?>
                                                <br><small class="text-muted">Code: <?= $user['employee_code'] ?></small>
                                            <?php endif; ?>
                                        </td>

                                        <td>
                                            <?php
                                            // Get currently assigned projects for this user
                                            $assigned_projects = [];
                                            if (isset($user_assignments[$user['id']])) {
                                                foreach ($user_assignments[$user['id']] as $assignment) {
                                                    $assigned_projects[] = $assignment['project_id'];
                                                }
                                            }
                                            ?>
                                            <select class="form-control project-select"
                                                    data-user-id="<?= $user['id'] ?>"
                                                    data-date="<?= $job_date ?>"
                                                    multiple>
                                                <?php foreach ($projects as $project_id => $project_title): ?>
                                                    <option value="<?= $project_id ?>" <?= in_array($project_id, $assigned_projects) ? 'selected' : '' ?>><?= $project_title ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td>
                                            <?php
                                            // Get remarks from the first assignment (if any)
                                            $remarks = '';
                                            if (isset($user_assignments[$user['id']]) && !empty($user_assignments[$user['id']])) {
                                                $remarks = $user_assignments[$user['id']][0]['remarks'];
                                            }
                                            ?>
                                            <textarea class="form-control remarks-input"
                                                      data-user-id="<?= $user['id'] ?>"
                                                      data-date="<?= $job_date ?>"
                                                      rows="2"><?= $remarks ?></textarea>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Select2 CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
    $(document).ready(function() {
        // Initialize Select2 for project selection
        $('.project-select').select2({
            placeholder: 'Select projects',
            allowClear: true,
            width: '100%'
        });



        // Handle project selection change
        $('.project-select').change(function() {
            var userId = $(this).data('user-id');
            var date = $(this).data('date');
            var projectIds = $(this).val();
            var remarks = $('textarea[data-user-id="' + userId + '"]').val();

            // Show loading indicator
            showToast('info', 'Saving project assignments...');

            // Save project assignments via AJAX
            $.ajax({
                url: '<?= base_url("app/project_assign/save_assignment") ?>',
                type: 'POST',
                data: {
                    user_id: userId,
                    job_date: date,
                    project_ids: projectIds,
                    remarks: remarks
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 1) {
                        showToast('success', response.message);
                    } else {
                        showToast('error', response.message);
                    }
                },
                error: function() {
                    showToast('error', 'An error occurred while saving project assignments');
                }
            });
        });

        // Handle remarks change (with debounce)
        var remarksTimer;
        $('.remarks-input').on('input', function() {
            var textarea = $(this);
            clearTimeout(remarksTimer);
            remarksTimer = setTimeout(function() {
                var userId = textarea.data('user-id');
                var date = textarea.data('date');
                var remarks = textarea.val();
                var projectIds = $('select[data-user-id="' + userId + '"]').val();

                // Show loading indicator
                showToast('info', 'Saving remarks...');

                // Save project assignments with updated remarks via AJAX
                $.ajax({
                    url: '<?= base_url("app/project_assign/save_assignment") ?>',
                    type: 'POST',
                    data: {
                        user_id: userId,
                        job_date: date,
                        project_ids: projectIds,
                        remarks: remarks
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 1) {
                            showToast('success', response.message);
                        } else {
                            showToast('error', response.message);
                        }
                    },
                    error: function() {
                        showToast('error', 'An error occurred while saving remarks');
                    }
                });
            }, 1000); // 1 second debounce
        });
    });

    // Function to change date
    function changeDate(date) {
        window.location.href = '<?= base_url("app/project_assign/daily_assign") ?>?job_date=' + date;
    }

    // Function to show toast notifications
    function showToast(type, message) {
        // Check if toastr is available
        if (typeof toastr !== 'undefined') {
            toastr[type](message);
        } else {
            // Fallback to alert if toastr is not available
            if (type === 'error') {
                alert('Error: ' + message);
            } else if (type === 'success') {
                console.log('Success: ' + message);
            }
        }
    }
</script>

<style>
    .select2-container--default .select2-selection--multiple {
        min-height: 38px;
    }
    .toast-success {
        background-color: #51A351;
    }
    .toast-error {
        background-color: #BD362F;
    }
    .toast-info {
        background-color: #2F96B4;
    }
    .toast-warning {
        background-color: #F89406;
    }
</style>
