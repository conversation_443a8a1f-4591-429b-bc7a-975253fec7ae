<div class="container-fluid p-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?=$_SERVER['HTTP_REFERER']?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>


    </div>
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title">
                <?= strtoupper($page_title ?? '')?>
                (<?= DateTime::createFromFormat('Y-m-d', $_GET['log_date'])->format('d-m-Y')?>)
            </h3>
            <?php
            if (has_permission('time_log/daily_log') || get_user_id() == 57){
                ?>
                <div class="float-right">
                    <input type="date" name="log_date" onchange="get_value(this.value)" value="<?=$_GET['log_date']?>"
                           class="form-control" style="font-size: 18px;border-radius: 0!important;width: 160px;">
                </div>
                <?php
            }
            if (has_permission('time_log_report/daily_report') || get_user_id() == 57){
                ?>
                <div class="float-right p-1 pr-3">
                    <a href="<?= base_url("app/time_log_report/daily_report/?log_date={$_GET['log_date']}"); ?>" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-eye"></i> Daily Report
                    </a>
                </div>
                <?php
            }
            ?>
        </div>
        <!-- /.card-header -->
        <div class="card-body p-1">
            <div class="p-1">
                <div class="p-2 d-none">
                    <b>Publish Status:</b>
                    <input class="form-control" type="checkbox">
                </div>
                <?php
                if (isset($users) && isset($time_log) && isset($time_log_type) && isset($_GET['log_date']) && isset($attendance_data)) {
                    foreach ($users as $key => $user) {

                        $attendance = $attendance_data[$user['id']]['attendance'] ?? '';
                        $remarks = $attendance_data[$user['id']]['remarks'] ?? '';
                        $off_date = $attendance_data[$user['id']]['off_date'] ?? '';

                        if ($attendance=='P' || $attendance == 'HD'){
                            $time_log_display = '';
                        }else{
                            $time_log_display = 'none';
                        }

                        if ($attendance!='P' && $attendance!=''){
                            $remarks_display = '';
                        }else{
                            $remarks_display = 'none';
                        }

                        if ($attendance=='OF'){
                            $off_date_display = '';
                        }else{
                            $off_date_display = 'none';
                        }
                        ?>
                        <div class="shadow-pro pt-2 pb-2 bg-white">
                                <div class="row p-2 mb-2">
                                    <div class="text-muted user_name">
                                        <?= $user['employee_code']?>.
                                        <?= $user['name']?>
                                        <span style="font-size: 13px;" class="float-right d-none">
                                            (<?=strtoupper($user['designation'])?>)
                                        </span>
                                        <button type="button" style="font-size: 13px;padding: 2px 20px!important;" onclick="reset_user_log(<?=$user['id']?>)"
                                                class="btn btn-primary btn-sm float-right">
                                            Reset
                                        </button>
                                    </div>
                                </div>
                                <div class="row p-1">
                                    <div class="attendance_container">
                                        <form class="form_att cf" >
                                            <section class="plan cf">
                                                <input type="radio" name="attendance[<?=$user['id']?>]" id="P_<?=$user['id']?>" value="P"
                                                       onclick="mark_attendance(<?=$user['id']?>, 'P')" <?=$attendance == 'P' ? 'checked' : ''?> class="form_att_radio_<?=$user['id']?>">
                                                <label class="free-label att_col_width att_col" for="P_<?=$user['id']?>">P</label>

                                                <input type="radio" name="attendance[<?=$user['id']?>]" id="A_<?=$user['id']?>" value="A"
                                                       onclick="mark_attendance(<?=$user['id']?>, 'A')" <?=$attendance == 'A' ? 'checked' : ''?> class="form_att_radio_<?=$user['id']?>">
                                                <label class="basic-label att_col_width att_col" for="A_<?=$user['id']?>">A</label>

                                                <input type="radio" name="attendance[<?=$user['id']?>]" id="WH_<?=$user['id']?>" value="WH"
                                                       onclick="mark_attendance(<?=$user['id']?>, 'WH')" <?=$attendance == 'WH' ? 'checked' : ''?> class="form_att_radio_<?=$user['id']?>">
                                                <label class="premium-label att_col_width att_col" for="WH_<?=$user['id']?>">WFH</label>

                                                <input type="radio" name="attendance[<?=$user['id']?>]" id="OF_<?=$user['id']?>" value="OF"
                                                       onclick="mark_attendance(<?=$user['id']?>, 'OF')" <?=$attendance == 'OF' ? 'checked' : ''?> class="form_att_radio_<?=$user['id']?>">
                                                <label class="premium-label att_col_width att_col" for="OF_<?=$user['id']?>">OFF</label>

                                                <input type="radio" name="attendance[<?=$user['id']?>]" id="OD_<?=$user['id']?>" value="OD"
                                                       onclick="mark_attendance(<?=$user['id']?>, 'OD')" <?=$attendance == 'OD' ? 'checked' : ''?> class="form_att_radio_<?=$user['id']?>">
                                                <label class="premium-label att_col_width att_col" for="OD_<?=$user['id']?>">OD</label>

                                                <input type="radio" name="attendance[<?=$user['id']?>]" id="HD_<?=$user['id']?>" value="HD"
                                                       onclick="mark_attendance(<?=$user['id']?>, 'HD')" <?=$attendance == 'HD' ? 'checked' : ''?> class="form_att_radio_<?=$user['id']?>">
                                                <label class="premium-label att_col_width att_col" for="HD_<?=$user['id']?>">HD</label>
                                            </section>
                                        </form>


                                    </div>
                                </div>
                                <div class="row" id="time_log_<?=$user['id']?>" style="display: <?=$time_log_display?>">
                                    <?php
                                    foreach ($time_log_type as $log_type){
                                        ?>
                                        <div class="text-center time_log_input" style="padding: 5px;">
                                            <div style="background-color: #e7f1f5; border-radius: 5px;padding: 5px;">
                                                <label style="font-weight: 600!important; font-size: 14px;color: #718c96!important;">
                                                    <?=strtoupper(get_phrase($log_type['title']))?>
                                                </label>
                                                <input type="time" onchange="save_time_log(<?=$log_type['id']?>, this.value, <?= $user['id']?>)"
                                                       name="time_log[<?=$log_type['id']?>]" value="<?=$time_log[$user['id']][$log_type['id']]?>" class="form-control time_log time_log_<?=$user['id']?>" style="font-size: 16px;">
                                            </div>
                                        </div>
                                        <?php
                                    }
                                    ?>
                                </div>

                                <div class="row p-2" id="remarks_<?=$user['id']?>" style="display: <?=$remarks_display?>">
                                    <input type="text" class="form-control remarks_<?=$user['id']?>" id="remarks_text_<?=$user['id']?>" style="max-width: 510px;font-size: 15px!important;border: 1px solid #d4d4d4"
                                              onchange="save_remarks(<?=$user['id']?>, this.value)" placeholder="Remarks" value="<?=$remarks?>">
                                </div>
                                <div class="row p-2" id="off_date_<?=$user['id']?>" style="display: <?=$off_date_display?>; max-width: 510px;">
                                    <div class="p-2">
                                        <label for="off_date_input_<?=$user['id']?>" class="text-muted" style="font-weight: normal!important;">
                                            Off for the date:
                                        </label>
                                    </div>
                                    <input type="date" class="form-control off_date_<?=$user['id']?>" id="off_date_input_<?=$user['id']?>" style="width: 170px;font-size: 15px;"
                                              onchange="save_off_date(<?=$user['id']?>, this.value)" value="<?=$off_date?>">
                                </div>
                            </div>
                        <div class="p-1"></div>
                        <?php
                    }
                }
                ?>
            </div>
        </div>
        <!-- /.card-body -->
    </div>
</div>


<script type="text/javascript">
    function get_value(date){
        window.location.href='<?=base_url('app/time_log/daily_log/?log_date=')?>' + date;
    }

    function save_time_log(log_type_id, log_time, user_id){
        var log_date = '<?=$_GET['log_date']?>';
        $.ajax({
            type: 'POST',
            url: "<?=base_url('app/time_log/save_time_log/')?>",
            data: {
                log_date: log_date,
                log_type_id: log_type_id,
                log_time: log_time,
                user_id: user_id
            },
            dataType: "json",
            success: function (data) {
                console.log(data);
                if(data.status === 1) {
                    console.log('success');
                    // message_success(data.message);
                }else{
                    console.log(data.message);
                    message_error('Something went wrong!, Please reload page!');
                }
            },
            error: function (request, status, error) {
                message_error('Please check network!!' + error);
            }
        });

    }

    function mark_attendance(user_id, attendance){
        if (attendance==='P'){
            $('#time_log_' + user_id).show('slow');
            $('#remarks_' + user_id).hide('slow');
            $('#off_date_' + user_id).hide('slow');
        }else if (attendance==='A'){
            $('#time_log_' + user_id).hide('slow');
            $('#remarks_' + user_id).show('slow');
            $('#off_date_' + user_id).hide('slow');
        }else if (attendance==='WH'){
            $('#time_log_' + user_id).hide('slow');
            $('#remarks_' + user_id).show('slow');
            $('#off_date_' + user_id).hide('slow');
        }else if (attendance==='OF'){
            $('#time_log_' + user_id).hide('slow');
            $('#remarks_' + user_id).show('slow');
            $('#off_date_' + user_id).show('slow');
        }else if (attendance==='OD'){
            $('#time_log_' + user_id).hide('slow');
            $('#remarks_' + user_id).show('slow');
            $('#off_date_' + user_id).hide('slow');
        }else if (attendance==='HD'){
            $('#time_log_' + user_id).show('slow');
            $('#remarks_' + user_id).show('slow');
            $('#off_date_' + user_id).hide('slow');
        }else{
            $('#time_log_' + user_id).hide('slow');
            $('#remarks_' + user_id).hide('slow');
            $('#off_date_' + user_id).hide('slow');
        }

        save_attendance(user_id, attendance);
    }

    function save_attendance(user_id, attendance){
        var attendance_date = '<?=$_GET['log_date']?>';
        $.ajax({
            type: 'POST',
            url: "<?=base_url('app/time_log/save_attendance/')?>",
            data: {
                attendance: attendance,
                date: attendance_date,
                user_id: user_id
            },
            dataType: "json",
            success: function (data) {
                console.log(data);
                if(data.status === 1) {
                    console.log('success');
                    // message_success(data.message);
                }else{
                    console.log(data.message);
                    message_error('Something went wrong!, Please reload page!');
                }
            },
            error: function (request, status, error) {
                message_error('Please check network!!' + error);
            }
        });
    }

    function save_remarks(user_id, remarks){
        var attendance_date = '<?=$_GET['log_date']?>';
        $.ajax({
            type: 'POST',
            url: "<?=base_url('app/time_log/save_remarks/')?>",
            data: {
                remarks: remarks,
                date: attendance_date,
                user_id: user_id
            },
            dataType: "json",
            success: function (data) {
                console.log(data);
                if(data.status === 1) {
                    console.log('success');
                    // message_success(data.message);
                }else{
                    console.log(data.message);
                    message_error('Something went wrong!, Please reload page!');
                }
            },
            error: function (request, status, error) {
                message_error('Please check network!!' + error);
            }
        });
    }

    function save_off_date(user_id, off_date){
        var attendance_date = '<?=$_GET['log_date']?>';
        $.ajax({
            type: 'POST',
            url: "<?=base_url('app/time_log/save_off_date/')?>",
            data: {
                off_date: off_date,
                date: attendance_date,
                user_id: user_id
            },
            dataType: "json",
            success: function (data) {
                console.log(data);
                if(data.status === 1) {
                    console.log('success');
                    // message_success(data.message);
                }else{
                    console.log(data.message);
                    message_error('Something went wrong!, Please reload page!');
                }
            },
            error: function (request, status, error) {
                message_error('Please check network!!' + error);
            }
        });
    }

    function reset_user_log(user_id){

        //rest at backend
        var log_date = '<?=$_GET['log_date']?>';
        $.ajax({
            type: 'POST',
            url: "<?=base_url('app/time_log/reset_time_log/')?>",
            data: {
                log_date: log_date,
                user_id: user_id
            },
            dataType: "json",
            success: function (data) {
                console.log(data);
                if(data.status === 1) {
                    console.log('success');
                    location.reload();
                }else{
                    console.log(data.message);
                    message_error('Something went wrong!, Please reload page!');
                }
            },
            error: function (request, status, error) {
                message_error('Please check network!!' + error);
            }
        });

    }

    function save_publish_status(checkbox){
        var publish_status = 0;
        if (checkbox.checked){publish_status = 1;}
        //rest at backend
        var log_date = '<?=$_GET['log_date']?>';
        $.ajax({
            type: 'POST',
            url: "<?=base_url('app/time_log/save_publish_status/')?>",
            data: {
                date: log_date,
                publish_status: publish_status
            },
            dataType: "json",
            success: function (data) {
                console.log(data);
                if(data.status === 1) {
                    message_success(data.message);
                }else{
                    console.log(data.message);
                    message_error('Something went wrong!, Please reload page!');
                }
            },
            error: function (request, status, error) {
                message_error('Please check network!!' + error);
            }
        });

    }
</script>

<style>
    

    /* CLEARFIX */

    .cf:before,
    .cf:after {
        content: " ";
        display: table;
    }

    .cf:after {
        clear: both;
    }

    .cf {
        *zoom: 1;
    }

    /* FORM */

    .form_att .plan input, .form_att .payment-plan input, .form_att .payment-type input{
        display: none;
    }

    .form_att label{
        position: relative;
        color: #fff;
        background-color: #aaa;
        font-size: 15px!important;
        text-align: center;
        height: 30px;
        line-height: 25px;
        display: block;
        cursor: pointer;
        border: 3px solid transparent;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }

    .form_att .plan input:checked + label, .form_att .payment-plan input:checked + label, .form_att .payment-type input:checked + label{
        /*border: 1px solid #333;*/
        background-color: #2b425e;
    }

    .form_att .plan input:checked + label:after, form .payment-plan input:checked + label:after, .form_att .payment-type input:checked + label:after{
        content: "\2713";
        width: 25px;
        height: 25px;
        line-height: 25px;
        border-radius: 100%;
        border: 1px solid #fefefe;
        background-color: #2b425e;
        z-index: 999;
        position: absolute;
        top: -10px;
        right: -10px;
    }


</style>

