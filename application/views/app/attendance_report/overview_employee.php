<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.3.3/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
<div class="container-fluid p-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>


    </div>
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title">
                <?= strtoupper($page_title ?? '')?>
                (<?= DateTime::createFromFormat('Y-m-d', $_GET['start_date'])->format('d-m-Y')?>)
            </h3>
            <div class="float-right">
                <input type="date" name="end_date" id="end_date" onchange="get_value()" value="<?=$_GET['end_date']?>"
                       class="form-control" style="font-size: 18px;border-radius: 10px!important;width: 160px;">
            </div>
            <div class="float-right p-2">to</div>
            <div class="float-right mr-2">
                <input type="date" name="start_date" id="start_date" onchange="get_value()" value="<?=$_GET['start_date']?>"
                       class="form-control" style="font-size: 18px;border-radius: 10px!important;width: 160px;">
            </div>
            <div class="float-right mr-2">
                <input type="number" name="working_days" id="working_days" onchange="get_value()" value="<?=$_GET['working_days']?>"
                       class="form-control" style="font-size: 18px;border-radius: 10px!important;width: 80px;">
            </div>

        </div>
        <!-- /.card-header -->
        <div class="card-body p-1" id="download_area">
            <?php
            if (isset($report) && isset($days_array)) {
                ?>
                <div class="pt-2 pb-2 bg-white p-1" >
                    <div class="p-3 text-center" style="background-color: #eaf9fe">
                        <?php
                        $start_date = DateTime::createFromFormat('Y-m-d', $_GET['start_date'])->format('d-m-Y');
                        $end_date = DateTime::createFromFormat('Y-m-d', $_GET['end_date'])->format('d-m-Y');
                        ?>
                        <div style="font-size: 22px; font-weight: bold">Time Log Report <br> <small>(<?="<b>{$start_date}</b> to <b>{$end_date}</b>"?>)</small></div>
                    </div>
                    <table class="table table-bordered" style="color: #333">
                        <tr style="background-color: #f1f2f8; font-size: 13px">
                            <th style="width: 200px!important;">Month</th>
                            <?php
                            foreach ($days_array as $day){
                                echo "<th>{$day}</th>";
                            }
                            ?>
                            <th>OF</th>
                            <th>OD</th>
                            <th>HD</th>
                            <th>Late</th>
                            <th>Early</th>
                            <th>Productive</th>
                            <th>AVG/ EFFECT</th>
                            <th>Office</th>
                            <th>Break</th>
                            <th>Attendance Points</th>
                            <th>Job Points</th>
                            <th>Total Points</th>
                        </tr>
                        <?php
                        foreach ($report as $month => $month_data) {
                            $month_display = DateTime::createFromFormat('Y-m', $month)->format('F, Y')
                            ?>
                            <tr>
                                <th style="background-color: #e9eff6;font-size:16px" class="text-muted"><?=$month_display?></th>
                                <?php
                                foreach ($days_array as $day_key){
                                    $attendance = $month_data['data'][$day_key]['attendance'] ?? '';
                                    $attendance_date = $month_data['data'][$day_key]['date'] ?? '';
                                    $attendance_day = DateTime::createFromFormat('Y-m-d', $month.'-'.$day_key)->format('D');

                                    if (empty($attendance)){
                                        ?>
                                        <th style="background-color: rgba(221,236,246,0.41);" class="text-center">
                                            <small><?=$attendance_day?></small>
                                            -
                                        </th>
                                        <?php
                                    }elseif ($attendance == 'P' || $attendance == 'OD' || $attendance == 'WH'){
                                        ?>
                                        <th style="background-color: rgba(234,243,248,0.41);color: #087568" class="text-center">
                                            <small><?=$attendance_day?></small>
                                            <?=$attendance?>
                                        </th>
                                        <?php
                                    }else{
                                        ?>
                                        <th style="background-color: rgba(239,189,189,0.23);color: #a12829" class="text-center">
                                            <small><?=$attendance_day?></small>
                                            <?=$attendance?>
                                        </th>
                                        <?php
                                    }

                                }
                                ?>

                                <th style="background-color: rgba(239,189,189,0.23); color: #a12829; font-size:14px;" class="text-center"><?=$user['time_log_overview']['break_duration']?></th>
                                <th style="background-color: #eff5f4;color: #268a7b; font-size: 17px;" class="text-center">
                                    <?=$user['attendance_points']?>
                                </th>
                            </tr>
                            <?php
                        }
                        ?>
                    </table>

                </div>

                <?php
            }
            ?>

        </div>
        <div class="text-center p-2">
            <button id="download_image" class="btn btn-primary">Download Image</button>
            <button id="download_pdf" class="btn btn-primary">Download PDF</button>
        </div>
        <div id="canvasContainer">
            <canvas id="canvas" style="display: none"></canvas>
        </div>
        <div id="imageContainer" style="display: none"></div>
        <!-- /.card-body -->
    </div>
</div>

<script>
    $(document).ready(function() {
        $("#download_pdf").click(function() {
            var tableToConvert = document.getElementById('download_area');

            html2canvas(tableToConvert, {
                width: tableToConvert.offsetWidth,
                height: tableToConvert.offsetHeight
            }).then(function(canvas) {
                var imgData = canvas.toDataURL('image/png');

                var pdf = new jsPDF();
                pdf.addImage(imgData, 'PNG', 10, 10);  // coordinates (10, 10) define the position in the PDF. Adjust as needed.
                pdf.save("attendance_report_<?=$_GET['log_date']?>.pdf");
            });


        });

        $('#download_image').on('click', function() {
            // Select the table you want to convert
            var tableToConvert = document.getElementById('download_area');

            // Convert the table to a canvas
            html2canvas(tableToConvert, {
                width: tableToConvert.offsetWidth,
                height: tableToConvert.offsetHeight
            }).then(function(canvas) {
                // Hide the canvas
                document.getElementById('canvas').style.display = 'none';

                // Convert the canvas to an image
                var image = new Image();
                image.src = canvas.toDataURL('image/png');

                // Append the image to a container
                var imageContainer = document.getElementById('imageContainer');
                imageContainer.innerHTML = ''; // Clear previous content
                imageContainer.appendChild(image);

                // Create a hidden link to trigger automatic download
                var downloadLink = document.createElement('a');
                downloadLink.href = image.src;
                downloadLink.download = 'attendance_report_<?=$_GET['log_date']?>.png'; // Set the desired download filename
                downloadLink.style.display = 'none'; // Hide the link
                imageContainer.appendChild(downloadLink);

                // Simulate a click on the download link
                downloadLink.click();
            });
        });
    });
</script>
<script type="text/javascript">
    function get_value(){
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        var working_days = $('#working_days').val();
        window.location.href='<?=base_url('app/time_log_report/overview_report/?')?>' + 'start_date='+start_date+'&end_date='+end_date+'&working_days=' + working_days;
    }
</script>


