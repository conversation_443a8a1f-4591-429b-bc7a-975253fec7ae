<div class="container-fluid">
	<div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
		<a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
			<i class="fas fa-arrow-circle-left"></i> Go Back
		</a>
        <?php
            if (has_permission('clients/add')){
                ?>
                <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=clients/add', '<?= get_phrase('add_client'); ?>')"
                        class="btn btn-primary btn-mini float-right">
                    <small><i class="fas fa-plus"></i></small> Add <?= $page_title ?? '' ?>
                </button>
                <?php
            }
        ?>

	</div>
    <div class="mt-3" style="margin:14px;">
        <div class="pb-2">
            <div class="card-pro">
                <div style="font-size: 20px;font-weight: bold; color: #4a4741">
                    CLIENTS: <?=count($list_items ?? [])?>
                </div>
            </div>
        </div>
        <div class="pb-4 pt-2">
            <input type="text" class="form-control form-control-lg" id="ta_search_list" placeholder="Search Clients.." style="padding:25px;font-size:18px">
        </div>
        <div class="row">
            <?php
            if (isset($list_items)) {
                foreach ($list_items as $key => $item) {
                    ?>
                    <div class="col-4 p-2 ta_search_item">
                        <div class="bg-white p-4 shadow-pro">
                            <small class="text-muted">Client Name:</small>
                            <h5 style="font-weight: bold;" class="text-primary"><?= $item['title']?></h5>
                            <small class="text-muted">Created on: <?= DateTime::createFromFormat('Y-m-d H:i:s', $item['created_on'])->format('d-m-Y g:i A')?></small>
                            <hr>
                            <div>
                                <?php
                                if (is_super_admin() || is_project_manager()){
                                    ?>
                                    <div class="row">
                                        <div class="col-6 p-1">
                                            <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=clients/edit'); ?>', '<?php echo get_phrase('update_classes'); ?>')"
                                                    class="btn btn-info btn-sm w-100">
                                                <small><i class="fas fa-pencil-alt"></i></small> Edit
                                            </button>
                                        </div>
                                        <div class="col-6 p-1">
                                            <button onclick="confirm_modal('<?=base_url("app/clients/delete/{$item['id']}/");?>')"
                                                    class="btn btn-outline-danger btn-sm w-100">
                                                <small><i class="fas fa-trash"></i></small> Delete
                                            </button>
                                        </div>
                                    </div>
                                    <?php
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            }
            ?>
        </div>
        <div id="ta_no_result" class="text-center p-2" style="display: none">
            <div class="p-4 shadow_pro bg-white mx-auto" style="">
                <img width="96" height="96" src="https://img.icons8.com/fluency/96/nothing-found.png" alt="nothing-found"/>
                <h4 class="text-danger mt-3">No Result Found!</h4>
            </div>
        </div>
	</div>
</div>

