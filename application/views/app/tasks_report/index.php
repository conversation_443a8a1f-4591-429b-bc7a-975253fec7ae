<div class="container-fluid mobile_hide">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=todo/add', '<?= get_phrase('add_todo'); ?>')" style="width: 160px;"
                class="btn btn-primary btn-mini float-right">
            <small><i class="fas fa-plus"></i></small> Add <?= $page_title ?? '' ?>
        </button>
    </div>
    <div class="card card-primary row mt-3 shadow-pro" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title"><?= strtoupper($page_title ?? '')?></h3>
        </div>
        <!-- /.card-header -->
        <div class="card-body ">
            <form action="" method="get">
                <div class="row mt-2 " style="max-width: 1000px;">
                    <div class="col-3 form-group p-2">
                        <input type="date" value="<?=$_GET['start_date'] ?? date('Y-m-d')?>" class="form-control" id="start_date" name="start_date" required>
                    </div>
                    <div class="col-3 form-group p-2">
                        <input type="date" value="<?=$_GET['end_date'] ?? date('Y-m-d')?>" class="form-control" id="end_date" name="end_date" required>
                    </div>
                    <div class="col-3 form-group p-2">
                        <select class="form-control select2" id="user_id" name="user_id" required>
                            <option value="0" <?=$_GET['user_id'] == '0' ? 'selected' : ''?>>All Employees</option>
                            <?php
                                if (isset($users)){
                                    foreach ($users as $user_id => $user_name){
                                        $selected = $_GET['user_id'] == $user_id ? 'selected' : '';
                                        echo "<option value=\"{$user_id}\" {$selected}>{$user_name}</option>";
                                    }
                                }
                            ?>
                        </select>
                    </div>
                    <div class="col-3 form-group p-2">
                        <select class="form-control select2" id="project_id" name="project_id" required>
                            <option value="0" <?=$_GET['user_id'] == '0' ? 'selected' : ''?>>All Projects</option>
                            <?php
                                if (isset($projects)){
                                    foreach ($projects as $project_id => $project_name){
                                        $selected = $_GET['project_id'] == $project_id ? 'selected' : '';
                                        echo "<option value=\"{$project_id}\" {$selected}>{$project_name}</option>";
                                    }
                                }
                            ?>
                        </select>
                    </div>
                    <div class="col-2 form-group p-2">
                        <button type="submit" class="btn btn-secondary btn-block">
                            <small><i class="bi bi-funnel-fill"></i></small> Filter
                        </button>
                    </div>
                </div>
            </form>
            <table id="table_no_btn" class="table table-bordered table-striped1">
                <thead>
                <tr>
                    <th>#</th>
                    <th>Date</th>
                    <th>User</th>
                    <th>Project</th>
                    <th>Task</th>
                    <th>Time</th>
                </tr>
                </thead>
                <tbody>
                <?php
                if (isset($task_report)) {
                    foreach ($task_report as $key => $item) {
                        ?>
                        <tr>
                            <td><?= $key + 1 ?></td>
                            <td>
                                <span class="badge bg-primary-lighten mb-1">
                                    <b><?= $item['job_date']?></b>
                                </span>
                            </td>
                            <td><?= get_employee_name(strtoupper($users[$item['user_id']]))?></td>
                            <td>
                                <?=get_project_title($projects[$item['project_id']])?>
                            </td>
                            <td>
                                <h5 class="p-2 text-muted" onclick="toggle_items('<?= $item['id']?>')"
                                    style="font-size: 16px!important; font-weight: bold; background-color: rgba(250,247,250,0.81); border-radius: 2px;cursor: pointer">
                                    <?= $item['task_title']?>
                                </h5>
                                <div id="item_<?= $item['id']?>" style="display: none" class="p-1 mt-3">
                                    <?php
                                    if(!empty($item['remarks'])){
                                        ?>
                                        <div class="p-1">
                                            <div class="p-1 pb-3" style="background-color: rgb(250,246,246);border-radius: 2px;">
                                                <span class="badge badge-danger mb-1">Remarks:</span><br>
                                                <?=$item['remarks']?>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                    ?>
                                </div>

                            </td>
                            <td>
                                <?php
                                $start_time = !empty($item['start_time']) ? DateTime::createFromFormat('H:i:s', $item['start_time'])->format('g:i A') : '';
                                $end_time = !empty($item['end_time']) ? DateTime::createFromFormat('H:i:s', $item['end_time'])->format('g:i A') : '';
                                echo "<b>{$start_time}</b> to <b>{$end_time}</b><hr>";
                                echo "<span class='text-muted'>Duration: <b>{$item['time_taken']}</b></span>";
                                ?>
                            </td>
                        </tr>
                        <?php
                    }
                }
                ?>

                </tbody>

            </table>
        </div>
        <!-- /.card-body -->
    </div>
</div>

<script type="text/javascript">
    function toggle_items(item_id) {
        $('#item_'+item_id).toggle('slow');
    }
</script>


<style>
    td, th{
        padding: 6px!important;
    }
    .btn_hide_show{
        padding: 2px 4px!important;
        font-size: 13px!important;
        width: 120px;
    }
    .assigned_to{
        border-radius: 10px;
        padding: 2px 10px;
        font-size: 12px;
        font-weight: bold;
        color: rgb(105, 104, 104);
        background-color: rgba(154, 153, 153, 0.16);
    }
    .due_date{
        border-radius: 10px;
        padding: 2px 10px;
        font-size: 12px!important;
        font-weight: bold;
        background-color: rgba(224, 223, 223, 0.16);
    }
    .due_date span{
        font-size: 14px!important;
    }
</style>




