<div class="max-width-1500 p-2 pt-0">
    <div class="row p-2 pt-0" style="margin-top: -25px">
        <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;width: 100%">
            <div class="card-header">
                <h3 class="card-title">
                    <?= strtoupper($page_title ?? '')?>
                </h3>
            </div>

            <div class="card-body p-2" >
                <div class="row">
                    <div class="col-3 p-2">
                        <div class="shadow-pro p-2 text-danger">
                            <h3 style="font-weight: bolder">113</h3>
                            <div>PENDING</div>
                        </div>
                    </div>
                    <div class="col-6"></div>
                </div>
                <div class="" >
                    <div class="alert bg-danger-lighten text-danger p-1" role="alert" style="font-weight:500">
                        <ul>
                            <li>Click on the project name to view tasks list.</li>
                            <li>Click on the employee name to view employee dashboard.</li>
                        </ul>
                    </div>
                    <table class="table table-bordered">
                        <tr style="background-color: #f1f2f8; font-size: 13px">
                            <th style="width: 50px;">#</th>
                            <th style="width: 220px">Name</th>
                            <th>Projects</th>
                        </tr>
                        <?php
                        if (isset($users) && isset($projects)){
                            $key = 0;
                            foreach ($users as $user){
                                ?>
                                <tr>
                                    <td style="background-color: #f5f9fd;"><?=++$key?></td>
                                    <td style="background-color: #f5fdfc;font-size:18px">
                                        <a href="<?=base_url('app/dashboard/index/?user_id='.$user['id'])?>" class="btn_employee">
                                            <?=strtoupper($user['name'])?>
                                        </a>
                                    </td>
                                    <td style="background-color: rgba(246,243,243,0.41);">

                                        <?php
                                            if(empty($user['projects'])){
                                                echo "<div class=\"bg-info-lighten text-danger p-2\">No Tasks Assigned</div>";
                                            }else{
                                                foreach ($user['projects'] as $project_id => $task_count){
                                                    ?>
                                                    <a href="javascript:void(0)" onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$project_id.'/'.$user['id'].'/?page_name=tasks_report/task_list_project'); ?>', '<?= "[{$projects[$project_id]}] - Assigned Tasks" ?>')">
                                                        <?=get_project_title_secondary("{$projects[$project_id]} [{$task_count}]")?>
                                                    </a>
                                                    <?php
                                                }
                                            }

                                        ?>
                                    </td>
                                </tr>
                                <?php
                            }
                        }
                        ?>
                    </table>
                </div>

            </div>
        </div>

    </div>
</div>

<style type="text/css">
    .btn_employee:hover{
        border-bottom: 2px solid #374945;
    }
</style>