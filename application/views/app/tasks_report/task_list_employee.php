<?php
$user_id = $param1;
$this->db->where('user_id', $user_id);
$this->db->group_start();
$this->db->where('task_status', 'assigned');
$this->db->or_where('task_status', 'testing');
$this->db->group_end();

$tasks = $this->tasks_m->get()->result_array();



usort($tasks, function($a, $b) {
    if ($a['task_status'] == 'assigned' && $b['task_status'] != 'assigned') {
        return -1; // $a is assigned, $b is not, so $a should come first
    } else if ($a['task_status'] != 'assigned' && $b['task_status'] == 'assigned') {
        return 1; // $a is not assigned, $b is, so $b should come first
    } else {
        // If task_status is same (either both 'assigned' or both 'not assigned'), compare dates
        $dateA = new DateTime($a['due_date']);
        $dateB = new DateTime($b['due_date']);

        // Compare dates, newer date should come first
        return $dateB <=> $dateA;
    }
});

$projects = array_column($this->projects_m->get()->result_array(), 'title', 'id');
$users = array_column($this->users_m->get()->result_array(), 'name', 'id');


?>
<div class="p-0" >

    <?php
    if (isset($tasks) && isset($projects) && isset($users)){
        foreach ($tasks as $task){
            $bg_color = get_task_bg_employee($task['task_status'], $task['due_date']);
            if ($bg_color == 'bg-success-lighten' || $bg_color == 'bg-dark-lighten'){
                $style = 'style="opacity: 0.7"';
            }else{
                $style = '';
            }
            ?>
            <div class="pb-2">
                <div class="single_task_item shadow-sm pl-2 <?=$bg_color?>"  <?=$style?>>
                    <div class="bg-white p-2 pb-0">
                        <?php
                        if ($task['task_status'] == 'assigned' || $task['task_status'] == 'testing'){
                            ?>
                            <div style="background-color: #f8f9fa;margin-bottom: 5px;">
                                <div class="row pb-2">
                                    <div class="col-4 text-left">
                                        <?=get_project_title($projects[$task['project_id']])?>
                                    </div>
                                    <div class="col-4 text-center">
                                        <?= get_due_date($task['due_date'])?>
                                    </div>
                                    <div class="col-4">
                                        <div class="mb-1 job_id_dashboard" style="margin-top: 0px!important;margin-right: 0px!important;">
                                            TRG-<b><?=$task['id']?></b>
                                        </div>
                                    </div>
                                </div>
                                <div class="row pb-2 text-center">
                                    <?php 
                                        if (is_mobile()){
                                            $modal_type = "show_ajax_modal";
                                        }else{
                                            $modal_type = "show_large_modal";
                                        }
                                    ?>
                                    <div class="col-3 p-1">
                                        <button onclick="<?=$modal_type?>('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/task_status'); ?>', '<?php echo get_phrase('change_status'); ?>')"
                                                class="btn btn-sm btn-outline-secondary pb-2 pt-2" style="width: 100%;">
                                            <small><i class="bi bi-person-check"></i></small> Status
                                        </button>
                                    </div>
                                    <div class="col-3 p-1">
                                        <button onclick="<?=$modal_type?>('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/assign'); ?>', '<?php echo get_phrase('assign_task'); ?>')"
                                                class="btn btn-info btn-sm pb-2 pt-2" style="width: 100%;">
                                            <small><i class="bi bi-person-check"></i></small> Assign
                                        </button>
                                    </div>
                                    <div class="col-3 p-1">
                                        <button onclick="<?=$modal_type?>('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/un_assign'); ?>', '<?php echo get_phrase('un_assign'); ?>')"
                                                class="btn btn-outline-warning btn-sm pb-2 pt-2" style="width: 100%;">
                                            <small><i class="bi bi-person-check"></i></small> Un-Assign
                                        </button>
                                    </div>
                                    <div class="col-3 p-1">
                                        <button onclick="<?=$modal_type?>('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/edit'); ?>', '<?php echo get_phrase('update_task'); ?>')"
                                                class="btn btn-outline-info btn-sm pb-2 pt-2" style="width: 100%!important;">
                                            <b><i class="fas fa-pencil-alt"></i></b> Edit
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <?php
                        }
                        ?>
                        <h5 style="background-color: #f4f4fa" class="text-primary p-2 m-0"><?=$task['title']?></h5>
                        <?php
                        if ($task['task_status'] == 'assigned'){
                            ?>
                            <div class="text-muted p-2 pb-3">
                                <div class="task_description">
                                    <?=substr($task['description'], 0, 20)?>
                                </div>
                            </div>
                            <?php
                        }
                        ?>
                        <div class="float-left text-muted mt-2" style="font-size: 12px;">
                            Added by: <?=strtoupper($users[$task['created_by']])?> | Updated on: <?= DateTime::createFromFormat('Y-m-d H:i:s', $task['updated_on'])->format('d-m-Y g:i A')?>
                        </div>
                        <div class="clearfix"></div>

                    </div>
                </div>
            </div>
            <?php
        }
    }
    ?>

</div>

<style type="text/css">
    h5{
        font-size: 17px!important;
    }
    .task_description{
        font-size: 15px!important;
    }
</style>