<?php
$start_date = DateTime::createFromFormat('Y-m-d', $_GET['start_date'])->format('d-m-Y');
$end_date = DateTime::createFromFormat('Y-m-d', $_GET['end_date'])->format('d-m-Y');
$user_id = $_GET['user_id'];
$project_id = $_GET['project_id'];
?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.3.3/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
<div class="container-fluid p-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/work_report/overview_report"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>


    </div>
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title text-center">
                <?= strtoupper($page_title ?? '')?>
            </h3>
        </div>
        <!-- /.card-header -->
        <div class="card-body p-1" id="download_area">
           <form method="get" action="">
               <div class="shadow_pro row p-1" style="max-width: 1000px;">
                   <div class="col-12 col-lg-3 p-2">
                       <select name="user_id" id="user_id" value="<?=$user_id?>"
                               class="form-control select2">
                           <option value="">Choose Employee</option>
                           <?php
                           if (isset($users)){
                               foreach ($users as $employee_id => $employee_name){
                                   $selected = $employee_id == $user_id ? 'selected' : '';
                                   echo "<option value='{$employee_id}' {$selected}>{$employee_name}</option>";
                               }
                           }
                           ?>
                       </select>
                   </div>
                   <div class="col-12 col-lg-3 p-2">
                       <select name="project_id" id="project_id" value="<?=$project_id?>"
                               class="form-control select2">
                           <option value="">Choose Project</option>
                           <?php
                           if (isset($projects)){
                               foreach ($projects as $item_id => $project_name){
                                   $selected = $item_id == $project_id ? 'selected' : '';
                                   echo "<option value='{$item_id}' {$selected}>{$project_name}</option>";
                               }
                           }
                           ?>
                       </select>
                   </div>
                   <div class="col-6 col-lg-2 p-2">
                       <input type="date" name="start_date" id="start_date" value="<?=$_GET['start_date']?>"
                              class="form-control">
                   </div>
                   <div class="col-6 col-lg-2 p-2">
                       <input type="date" name="end_date" id="end_date" value="<?=$_GET['end_date']?>"
                              class="form-control">
                   </div>
                   <div class="col-6 col-lg-2 p-2">
                       <button type="submit" class="btn btn-primary w-100">
                           Filter
                       </button>
                   </div>

               </div>
           </form>
            <?php
            if (isset($users) && isset($work_report) && isset($_GET['start_date']) && isset($_GET['end_date'])) {
                ?>
                <div class="shadow-pro pt-2 pb-2 bg-white p-1" >
                    <div class="p-3 text-center bg-primary" style="background-color: rgb(234,254,244)">
                        <div style="font-size: 22px; font-weight: bold;">WORK REPORT - <?=strtoupper($users[$user_id])?></div>
                        <div style="font-size: 20px;">
                            (<?= "{$start_date} to {$end_date}" ?>)
                        </div>
                    </div>
                    <table class="table table-bordered mt-2" style="max-width: 800px;margin: auto">

                        <?php
                        if(count($work_report)){
                            foreach ($work_report as $job_date => $jobs) {
                            $total_duration = '00:00:00';
                            $duration_array = [];
                            if (is_array($jobs['jobs'])){
                                foreach ($jobs['jobs'] as $job) {
                                    foreach ($job['history'] as $job_history) {
                                        $duration_array[] = $job_history['time_taken'];
                                    }
                                }
                                $total_duration = add_duration($duration_array, 'duration');
                            }


                            if (is_array($jobs)){
                                $task_count = count($jobs);
                            }else{
                                $jobs = [];
                                $task_count = 0;
                            }

                            $task_row = $task_count == 0 ? 1 : $task_count;
                            ?>
                            <tr>
                                <th colspan="3" style='background-color: #f5f7fd!important;font-size: 18px;color: #9d3b98;text-align: center'>
                                    <?php 
                                        if(!empty($job_date)){
                                            echo DateTime::createFromFormat('Y-m-d', $job_date)->format('d-m-Y (l)');
                                        }
                                    ?>
                                    <div style='color:#999999; font-size: 15px;margin-top: 4px;'>[<?= DateTime::createFromFormat('H:i:s', $total_duration)->format('H:i')?> <small>Hours</small>]</div>
                                </th>
                            </tr>
                            <?php
                            if (count($jobs['jobs'])){
                                foreach ($jobs['jobs'] as $job) {
                                    echo "<tr>";
                                    ?>

                                    <td style="padding: 0px!important;">
                                        <div style="background-color: rgba(248,242,250,0.82);font-size: 17px;" class="p-2 pt-3 pb-3">
                                            <span style="font-size: 17px;color: #2d7dbe;margin-right: 15px">
                                                [TRG-<b><?=$job['details']['id']?></b>]
                                            </span>
                                            <span style="font-size: 17px;margin-top: 5px;color: #136565">
                                                <b><?=$job['details']['title']?></b>
                                            </span>
                                        </div>
                                        <div style="background-color: #fff;font-size: 14px;" class="p-2 pt-3 pb-3">
                                            <?php
                                            foreach ($job['history'] as $job_history){
                                                $duration_array[] = $job_history['time_taken'];
                                                if(!empty($job_date)){
                                                    $job_date = DateTime::createFromFormat('Y-m-d', $job_history['job_date'])->format('d-m-Y');
                                                }else{
                                                    $job_date = '';
                                                }
                                                
                                                if(!empty($start_time)){
                                                    $start_time = DateTime::createFromFormat('H:i:s', $job_history['start_time'])->format('g:i A');
                                                }else{
                                                    $start_time = '';
                                                }
                                                
                                                if(!empty($end_time)){
                                                     $end_time = DateTime::createFromFormat('H:i:s', $job_history['end_time'])->format('g:i A');
                                                }else{
                                                    $end_time = '';
                                                }
                                                
                                                ?>
                                                <div class="" style="border-bottom: 1px solid #efefef">
                                                    <span style="color: #7457fe"><?="<b>{$job_date}</b> ({$start_time} - {$end_time})"?></span>
                                                    <br><span style="color: rgb(30,169,121)">[<?= "<b>{$job_history['time_taken']}</b>" ?>]</span>
                                                    <div style="margin-top: 16px"><?=$job_history['remarks']?></div>
                                                </div>
                                                <?php
                                            }
                                            ?>
                                        </div>
                                    </td>

                                    <?php
                                    echo "</tr>";
                                }
                            }
                            ?>
                            <?php
                        }
                        }else{
                            ?>
                            <h2 class="text-danger text-center p-4">No Job Logs Found</h2>
                            <?php
                        }
                        
                        ?>
                    </table>

                </div>

                <?php
            }
            ?>

        </div>
        <!-- /.card-body -->
    </div>
</div>

<script type="text/javascript">
    function get_value(){
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        var user_id = $('#user_id').val();
        window.location.href='<?=base_url("app/work_report/employee_report/?")?>user_id=' + user_id +'&start_date=' + start_date + '&end_date=' + end_date;
    }
</script>


<style>
    .date_input{
        font-size: 18px!important;border-radius: 40px!important;width: 150px;color:#fff!important;background-image: linear-gradient(90deg, #23384E, #166686)!important;
        cursor: pointer !important;
    }
    .date_input:hover{
        background-image: linear-gradient(90deg, #166686, #23384E)!important;
    }
</style>