<?php
    $start_date = DateTime::createFromFormat('Y-m-d', $_GET['start_date'])->format('d-m-Y');
    $end_date = DateTime::createFromFormat('Y-m-d', $_GET['end_date'])->format('d-m-Y');
?>

<div class="container-fluid p-0" style="min-width: 800px;">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <a href="<?= base_url("app/work_report/employee_status_report"); ?>" class="btn btn-info btn-mini pull-right">
            <i class="fas fa-calendar-check"></i> Employee Status Report
        </a>

    </div>
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title">
                <?= strtoupper($page_title ?? '')?>
                (<?= "{$start_date} to {$end_date}" ?>)
            </h3>
            <div class="float-right">
                <input type="date" name="end_date" id="end_date" onchange="get_value()" value="<?=$_GET['end_date']?>"
                       class="form-control date_input">
            </div>
            <div class="float-right mr-2">
                <input type="date" name="start_date" id="start_date" onchange="get_value()" value="<?=$_GET['start_date']?>"
                       class="form-control date_input">
            </div>


        </div>
        <!-- /.card-header -->
        <div class="card-body p-1" id="download_area">
            <?php
            if (isset($users) && isset($work_report) && isset($_GET['start_date']) && isset($_GET['end_date']) && isset($projects)) {
                ?>
                <div class="shadow-pro pt-2 pb-2 bg-white p-1" >
                    <div class="p-3 text-center bg-primary" style="background-color: rgb(234,254,244)">
                        <div style="font-size: 22px; font-weight: bold;">Work Report</div>
                        <div style="font-size: 20px;">
                            (<?= "{$start_date} to {$end_date}" ?>)
                        </div>
                    </div>

                    <table class="table table-bordered">
                        <tr style="background-color: #f1f2f8; font-size: 13px">
                            <th>SL.NO</th>
                            <th>NAME</th>
                            <th>TIME SHEET</th>
                        </tr>
                        <?php
                        foreach ($users as $key => $user) {
                            $total_duration = '00:00:00';
                            $jobs = $work_report[$user['id']]['jobs'];
                            $duration_array = [];
                            if (is_array($jobs)){
                                foreach ($jobs as $job) {
                                    $duration_array[] = $job['time_taken'];
                                }
                                $total_duration = add_duration($duration_array, 'duration');
                            }


                            if (is_array($jobs)){
                                $task_count = count($jobs);
                            }else{
                                $jobs = [];
                                $task_count = 0;
                            }

                            if ($attendance[$user['id']][$_GET['start_date']]!='working'){
                                $name_style =  "style='background-color:#f8b9be!important'";
                            }else{
                                $name_style =  "style='background-color: #f5fdfc!important;'";
                            }
                            ?>
                            <tr <?php if ($task_count == 0){ echo $name_style;} ?>>
                                <td><?=$key+1?></td>
                                <th <?=$name_style?>>
                                    <?=$user['name']?>
                                    <div class="pt-2">
                                        <a href="<?=base_url("app/work_report/employee_report/?start_date={$_GET['start_date']}&end_date={$_GET['end_date']}&user_id={$user['id']}")?>" class="btn btn-outline-secondary btn-sm pull-right" style="padding: 1px 3px!important;font-size: 11px!important;">
                                                Employee Report
                                        </a>
                                    </div>
                                </th>
                                <td>
                                    <?php
                                        if (count($jobs)){
                                            ?>
                                            <table class="table table-striped table-bordered time_sheet">
                                                <tr class="row">
                                                    <th class="col-2">Time taken</th>
                                                    <th class="col-3">Project</th>
                                                    <th class="col-7">Task</th>
                                                </tr>
                                                <?php
                                                foreach ($jobs as $job) {
                                                    $duration_array[] = $job['time_taken'];
                                                    $job_date = DateTime::createFromFormat('Y-m-d', $job['job_date'])->format('d-m-Y');
                                                    $start_time = DateTime::createFromFormat('H:i:s', $job['start_time'])->format('g:i A');
                                                    $end_time = DateTime::createFromFormat('H:i:s', $job['end_time'])->format('g:i A');
                                                    ?>
                                                    <tr class="row">
                                                        <td class="col-2"><?=$start_time?> to <?=$end_time?> (<?=format_time_from_seconds(time_to_seconds($job['time_taken']))?>)</td>
                                                        <td class="col-3"><?=get_project_title($projects[$job['task_details']['project_id']])?></td>
                                                        <td class="col-7">
                                                            <div style="color: #6a5894">[<b>TRG-<?=$job['task_id']?></b>] </div>
                                                            <div style="color: #35696b"><?=$job['task_details']['title']?></div>
                                                            <blockquote><?=$job['remarks']?></blockquote>
                                                        </td>
                                                    </tr>
                                                    <?php
                                                }
                                                ?>
                                            </table>
                                            <?php
                                        }else{
                                            if ($attendance[$user['id']][$_GET['start_date']]=='working'){
                                                echo "<h1 style='font-size: 28px;font-weight: bold;color: red'>TASK NOT UPDATED</h1>";
                                            }else{
                                                echo "<h1 style='font-size: 18px;font-weight: bold;color: black'>".$attendance[$user['id']][$_GET['start_date']]."</h1>";
                                            }
                                        }
                                    ?>


                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </table>

                </div>

                <?php
            }
            ?>

        </div>
        <div class="text-center p-2">
            <button id="download_image" class="btn btn-primary">Download Image</button>
            <a href="<?=$pdf_url??''?>" class="btn btn-primary" download="">Download PDF</a>
        </div>
        <div id="canvasContainer">
            <canvas id="canvas" style="display: none"></canvas>
        </div>
        <div id="imageContainer" style="display: none"></div>
        <!-- /.card-body -->
    </div>
</div>


<script type="text/javascript">
    function get_value(){
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        window.location.href='<?=base_url('app/work_report/overview_report/?')?>start_date=' + start_date + '&end_date=' + end_date;
    }
</script>


<style>
    .date_input{
        font-size: 18px!important;border-radius: 40px!important;width: 150px;color:#fff!important;background-image: linear-gradient(90deg, #23384E, #166686)!important;
        cursor: pointer !important;
    }
    .date_input:hover{
        background-image: linear-gradient(90deg, #166686, #23384E)!important;
    }
    .time_sheet td, .time_sheet th{padding: 7px;}
    blockquote{
        border-left: 2px solid rgba(35, 56, 78, 0.34);
        background-color: rgba(197, 225, 206, 0.19);
        font-size: 14px;
        padding: 4px!important;
    }
</style>