<?php
    if (isset($start_date) && isset($end_date) && isset($work_report) && isset($users) && isset($attendance)){

        if ($start_date == $end_date){
            $work_date = DateTime::createFromFormat('Y-m-d', $start_date)->format('d-m-Y (l)');
        }else{
            $start_date = DateTime::createFromFormat('Y-m-d', $start_date)->format('d-m-Y');
            $end_date = DateTime::createFromFormat('Y-m-d', $end_date)->format('d-m-Y');
            $work_date = "{$start_date} - {$end_date}";
        }
        $date_array = array_reverse(get_date_array($start_date, $end_date));
        ?>
        <!DOCTYPE html>
        <html lang="en">
            <head>
                <meta charset="UTF-8">
                <title>Work Report : <?=$work_date?></title>
            </head>
            <body style="background-color: #f5faf8">
                <div class="header">
                    <div class="col-6">
                        <div id="logo_title" style="padding: 10px;padding-left:13px;padding-bottom:5px;font-size: 14px">
                            <a href="https://pms.trogon.info" target="_blank" style="text-decoration: none; color: azure">pms.trogon.info</a>
                        </div>
                        <div id="logo" style="padding: 10px;padding-top: 2px;">
                            <a href="https://pms.trogon.info" target="_blank">
                                <img src="<?=base_url('assets/logo/logo.png')?>" alt="Trogon Logo" style="width: 115px;height: auto">
                            </a>
                        </div>

                    </div>
                    <div class="col-6" style="text-align: right">
                        <div class="header_title">Work Report</div>
                        <div class="header_date"><?=$work_date?></div>
                        <div style="font-size: 10px; padding-right: 10px; color: #bdecec;">
                            Generated on: <?= DateTime::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->format('d-m-Y g:i A')?>
                        </div>
                    </div>
                </div>
                <div class="main">
                    <?php
                        foreach ($users as $user){
                            ?>
                            <div style="padding:7px 2px;">
                                <div style="background-color: #ffffff;border-radius: 5px;padding: 10px">
                                    <div id="employee_name" style="padding:5px;border-radius:4px;font-size: 20px;font-weight: bold;color: #1a5e60; background-color: rgb(248,241,248);text-align: center">
                                        <?=strtoupper($user['name'])?>
                                    </div>
                                    <div class="">
                                        <?php
                                        $user_jobs = $work_report[$user['id']]['jobs'];
                                        foreach ($date_array as $job_date){
                                            $user_attendance = $attendance[$user['id']][$job_date];

                                            $user_jobs = $work_report[$user['id']]['jobs'][$job_date] ?? [];
                                            $job_date = DateTime::createFromFormat('Y-m-d', $job_date)->format('d-m-Y (l)');
                                            ?>
                                            <div style='padding: 6px;'>
                                                <?php
                                                    if ($user_attendance == 'off' || $user_attendance == 'absent'){
                                                        ?>
                                                        <div style='text-align:center; color: #ec2136; padding:10px;font-size: 15px;font-weight: bold;background-color: rgb(250,229,231);display: inline-block;'>
                                                            <?=$job_date?>
                                                            <span style="color: #ef5e5e; font-size:12px">
                                                                <small> - Attendance: </small><?=strtoupper($user_attendance)?>
                                                            </span>
                                                        </div>
                                                        <?php
                                                    }else{
                                                        ?>
                                                        <div style='color: #7a62e5; font-size: 13px;font-weight: bold;background-color: rgba(211,225,239,0.1);display: inline-block;'>
                                                            <?=$job_date?>
                                                            <span style="color: #a588ef; font-size:12px">
                                                                <small> - Attendance: </small><?=strtoupper($user_attendance)?>
                                                            </span>
                                                        </div>
                                                        <?php
                                                    }
                                                ?>

                                            </div>
                                            <?php
                                            if(count($user_jobs)){
                                                foreach ($user_jobs as $job){
                                                    ?>
                                                        <div style="padding: 5px;">

                                                            <div style="font-size: 14px;color:#113665;background-color: rgba(239,246,250,0.73);padding: 7px;font-weight: bold">
                                                                <?=$job['details']['title']?>

                                                                <div style="margin-top:5px;font-size: 12px!important;float: right!important;color: rgb(31,84,225);text-align: right;font-weight: normal!important;">
                                                                    [TRG-<?=$job['details']['id']?>] -
                                                                    <?php
                                                                        if ($job['details']['task_status']=='completed' || $job['details']['task_status']=='testing'){
                                                                            $task_status_color = '#069647';
                                                                            $task_status = 'COMPLETED';
                                                                        }elseif($job['details']['task_details'] == 'on_hold'){
                                                                            $task_status_color = '#F6B84B';
                                                                            $task_status = 'ON HOLD';
                                                                        }else{
                                                                            $task_status_color = '#EF6547';
                                                                            $task_status = 'IN PROGRESS';
                                                                        }
                                                                    ?>
                                                                    <span style="font-size: 10px!important;color: <?=$task_status_color?>;">
                                                                        <small>[<?=$task_status?>]</small>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <div style="padding-top: 15px;padding-bottom: 15px">
                                                                <?php
                                                                    foreach($job['history'] as $history){
                                                                        if (!empty($history['start_time']) && !empty($history['end_time'])){
                                                                            $start_time = DateTime::createFromFormat('H:i:s', $history['start_time'])->format('g:i A');
                                                                            $end_time = DateTime::createFromFormat('H:i:s', $history['end_time'])->format('g:i A');
                                                                        }else{
                                                                            $start_time = '';
                                                                            $end_time = '';
                                                                        }
                                                                        $updated_on = DateTime::createFromFormat('Y-m-d H:i:s', $history['updated_on'])->format('d-m-Y, g:i A')
                                                                        ?>
                                                                        <div style="padding: 2px 0">
                                                                            <div style="background-color: rgba(238,248,241,0.05); border: 1px solid rgb(231,246,235)">
                                                                                <div style="font-size:13px;background-color:rgba(238,248,241,0.40);color: #234b4d;">
                                                                                   <div style="padding: 1px 5px">
                                                                                       <?= "<b>{$history['time_taken']}</b> - <small>[{$start_time} to {$end_time}]</small>"?>
                                                                                   </div>
                                                                                    <div style="padding: 3px 5px;font-size: 8px;color: #4e5e86;display:none">
                                                                                        Updated On: <?=$updated_on?>
                                                                                    </div>
                                                                                </div>
                                                                                <div style="padding: -5px 5px; font-size: 12px;">
                                                                                    <?=$history['remarks']?>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <?php
                                                                    }
                                                                ?>
                                                            </div>
                                                        </div>
                                                    <?php
                                                }
                                            }elseif($user_attendance!='absent' && $user_attendance!='off'){
                                                ?>
                                                <div style='font-size: 15px;color: #e50719;text-align: center;padding: 20px;background-color: rgba(254,136,145,0.13)'>
                                                    <img width="40" height="40" src="https://img.icons8.com/color/48/error--v1.png" alt="error--v1"/><br><br>
                                                    No jobs found for <b><?=strtoupper($user['name'])?></b> for <b><?=$job_date?></b>
                                                    <div style="padding: 5px">
                                                        <hr>
                                                    </div>
                                                    <div style="font-size: 14px;line-height: 1.7em!important;">
<!--                                                        Dear --><?php //=ucwords($user['name'])?><!--, -->
                                                        Kindly ensure to update your work status with proper descriptions. <br>Failure to do so may impact project timelines and performance evaluations.
                                                    </div>
                                                </div>
                                                <?php
                                            }
                                        }


                                        ?>
                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                    ?>
                </div>
                <div class="footer"
                     style="margin-top:100px;text-align: center;background-color: #faeeef; color: #8c0d26;border-radius: 7px;font-size: 14px;padding: 50px 30px;">
                    <b>Note:</b> All should update their daily work status to ensure smooth work assignment and project completion.
                </div>
                <style>
                    .col-6{
                        width: 50%!important;
                        float: left!important;
                    }
                    body{
                        border:1px solid #454545;
                        font-family: Arial, sans-serif;
                    }
                    .header{
                        background-image: linear-gradient(90deg, #23384E, #166686)!important;
                        color:#efefef;
                        padding:5px;
                        border-radius: 7px;
                    }
                    .header_title{
                        font-size: 21px;
                        font-weight: bold;
                        padding: 5px 10px;
                    }
                    .header_date{
                        padding: 2px 10px 10px;
                        font-weight: bold;
                        font-size: 13px;
                        color: azure;
                    }
                </style>
            </body>
        </html>
        <?php
    }
?>