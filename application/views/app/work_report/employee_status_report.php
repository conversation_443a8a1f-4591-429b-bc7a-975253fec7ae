<?php
    $start_date_formatted = DateTime::createFromFormat('Y-m-d', $start_date)->format('d-m-Y');
    $end_date_formatted = DateTime::createFromFormat('Y-m-d', $end_date)->format('d-m-Y');
?>

<div class="container-fluid p-0" style="min-width: 1200px;">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <a href="<?= base_url("app/work_report/overview_report"); ?>" class="btn btn-info btn-mini pull-right">
            <i class="fas fa-view-list"></i> Work Report Overview
        </a>
    </div>
    
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title">
                <?= strtoupper($page_title ?? '')?>
                (<?= "{$start_date_formatted} to {$end_date_formatted}" ?>)
            </h3>
            <div class="float-right">
                <input type="date" name="end_date" id="end_date" onchange="get_value()" value="<?=$end_date?>"
                       class="form-control date_input">
            </div>
            <div class="float-right mr-2">
                <input type="date" name="start_date" id="start_date" onchange="get_value()" value="<?=$start_date?>"
                       class="form-control date_input">
            </div>
        </div>
        
        <!-- /.card-header -->
        <div class="card-body p-1" id="download_area">
            <?php if (isset($users) && isset($status_report) && isset($date_range)) { ?>
                <div class="shadow-pro pt-2 pb-2 bg-white p-1">
                    <div class="p-3 text-center bg-primary" style="background-color: rgb(234,254,244)">
                        <div style="font-size: 22px; font-weight: bold;">Monthly Work Status Report</div>
                        <div style="font-size: 20px;">
                            (<?= "{$start_date_formatted} to {$end_date_formatted}" ?>)
                        </div>
                    </div>

                    <!-- Legend -->
                    <div class="p-3 bg-light">
                        <div class="row">
                            <div class="col-md-3">
                                <span class="status-legend status-updated"></span> Updated (7+ hours)
                            </div>
                            <div class="col-md-3">
                                <span class="status-legend status-partially"></span> Partially Updated (&lt;7 hours)
                            </div>
                            <div class="col-md-3">
                                <span class="status-legend status-not-updated"></span> Not Updated
                            </div>
                            <div class="col-md-3">
                                <span class="status-legend status-not-applicable"></span> Not Applicable (Off/Absent)
                            </div>
                        </div>
                        <?php if (count($date_range) > 15) { ?>
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="alert alert-info alert-sm mb-0" style="padding: 8px 12px; font-size: 13px;">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Tip:</strong> Use horizontal scroll or drag to view all dates. Employee names remain fixed for easy reference.
                                </div>
                            </div>
                        </div>
                        <?php } ?>
                    </div>

                    <div class="table-responsive-horizontal">
                        <table class="table table-bordered table-sm status-report-table">
                            <thead>
                                <tr style="background-color: #f1f2f8; font-size: 12px">
                                    <th rowspan="2" class="employee-header">Employee</th>
                                    <th colspan="<?= count($date_range) ?>" class="text-center">Work Status by Date</th>
                                    <th colspan="5" class="text-center">Summary</th>
                                </tr>
                                <tr style="background-color: #f8f9fa; font-size: 11px">
                                    <?php foreach ($date_range as $date) { 
                                        $date_obj = DateTime::createFromFormat('Y-m-d', $date);
                                        $day = $date_obj->format('d');
                                        $month = $date_obj->format('M');
                                    ?>
                                        <th class="date-header" title="<?= $date_obj->format('d-m-Y') ?>">
                                            <?= $day ?><br><small><?= $month ?></small>
                                        </th>
                                    <?php } ?>
                                    <th class="summary-header">Total Hours</th>
                                    <th class="summary-header">Updated</th>
                                    <th class="summary-header">Partial</th>
                                    <th class="summary-header">Not Updated</th>
                                    <th class="summary-header">N/A</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if (empty($users)) {
                                    ?>
                                    <tr>
                                        <td colspan="<?= count($date_range) + 6 ?>" class="text-center text-muted">
                                            <h4>No employees found with work_report enabled</h4>
                                        </td>
                                    </tr>
                                    <?php
                                } else {
                                    foreach ($users as $user) {
                                        $user_id = $user['id'];
                                        $user_data = $status_report[$user_id] ?? null;
                                        if (!$user_data) continue;
                                    ?>
                                        <tr>
                                            <td class="employee-name">
                                                <strong><?= $user['name'] ?></strong>
                                            </td>
                                            <?php foreach ($date_range as $date) {
                                                $day_data = $user_data['daily_status'][$date] ?? null;
                                                $status = $day_data['status'] ?? 'not_applicable';
                                                $work_hours_formatted = $day_data['work_hours_formatted'] ?? '0h 0m';
                                                $work_status = $day_data['work_status'] ?? '';

                                                // Create tooltip text
                                                $tooltip_text = ucfirst(str_replace('_', ' ', $status));
                                                if ($work_hours_formatted && $work_hours_formatted != '0h 0m') {
                                                    $tooltip_text .= ' - ' . $work_hours_formatted;
                                                }
                                                if ($work_status) {
                                                    $tooltip_text .= ' - ' . ucfirst($work_status);
                                                }
                                            ?>
                                                <td class="status-cell status-<?= $status ?>"
                                                    title="<?= $tooltip_text ?>">
                                                    <?php if ($status == 'updated') { ?>
                                                        <i class="fas fa-check-circle text-success"></i>
                                                    <?php } elseif ($status == 'partially_updated') { ?>
                                                        <i class="fas fa-clock text-warning"></i>
                                                    <?php } elseif ($status == 'not_updated') { ?>
                                                        <i class="fas fa-times-circle text-danger"></i>
                                                    <?php } else { ?>
                                                        <i class="fas fa-minus text-muted"></i>
                                                    <?php } ?>
                                                </td>
                                            <?php } ?>
                                            <td class="summary-cell"><strong><?= $user_data['summary']['total_hours_formatted'] ?: '0h 0m' ?></strong></td>
                                            <td class="summary-cell text-success"><strong><?= $user_data['summary']['days_updated'] ?></strong></td>
                                            <td class="summary-cell text-warning"><strong><?= $user_data['summary']['days_partially_updated'] ?></strong></td>
                                            <td class="summary-cell text-danger"><strong><?= $user_data['summary']['days_not_updated'] ?></strong></td>
                                            <td class="summary-cell text-muted"><strong><?= $user_data['summary']['days_not_applicable'] ?></strong></td>
                                        </tr>
                                    <?php
                                    }
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php } ?>
        </div>
        
        <div class="text-center p-2">
            <button id="download_image" class="btn btn-primary">Download Image</button>
            <button onclick="exportToCSV()" class="btn btn-success">Export CSV</button>
        </div>
        <div id="canvasContainer">
            <canvas id="canvas" style="display: none"></canvas>
        </div>
        <div id="imageContainer" style="display: none"></div>
    </div>
</div>

<script type="text/javascript">
    function get_value(){
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        window.location.href='<?=base_url('app/work_report/employee_status_report/?')?>start_date=' + start_date + '&end_date=' + end_date;
    }

    function exportToCSV() {
        // CSV export functionality
        var csv = [];
        var table = document.querySelector('.status-report-table');
        var rows = table.querySelectorAll('tr');
        
        for (var i = 0; i < rows.length; i++) {
            var row = [], cols = rows[i].querySelectorAll('td, th');
            
            for (var j = 0; j < cols.length; j++) {
                var cellText = cols[j].innerText.replace(/"/g, '""');
                row.push('"' + cellText + '"');
            }
            
            csv.push(row.join(','));
        }
        
        var csvFile = new Blob([csv.join('\n')], {type: 'text/csv'});
        var downloadLink = document.createElement('a');
        downloadLink.download = 'employee_status_report_<?= $start_date ?>_to_<?= $end_date ?>.csv';
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = 'none';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
    }

    // Enhanced scrolling experience
    document.addEventListener('DOMContentLoaded', function() {
        const tableContainer = document.querySelector('.table-responsive-horizontal');
        if (tableContainer) {
            // Add smooth scrolling
            tableContainer.style.scrollBehavior = 'smooth';

            // Add keyboard navigation
            tableContainer.addEventListener('keydown', function(e) {
                const scrollAmount = 100;
                switch(e.key) {
                    case 'ArrowLeft':
                        e.preventDefault();
                        this.scrollLeft -= scrollAmount;
                        break;
                    case 'ArrowRight':
                        e.preventDefault();
                        this.scrollLeft += scrollAmount;
                        break;
                }
            });

            // Make table container focusable for keyboard navigation
            tableContainer.setAttribute('tabindex', '0');

            // Add scroll indicators if content overflows
            function updateScrollIndicators() {
                const isScrollable = tableContainer.scrollWidth > tableContainer.clientWidth;
                const isAtStart = tableContainer.scrollLeft <= 0;
                const isAtEnd = tableContainer.scrollLeft >= tableContainer.scrollWidth - tableContainer.clientWidth - 1;

                // Remove existing indicators
                document.querySelectorAll('.scroll-indicator').forEach(el => el.remove());

                if (isScrollable) {
                    if (!isAtEnd) {
                        const rightIndicator = document.createElement('div');
                        rightIndicator.className = 'scroll-indicator scroll-right';
                        rightIndicator.innerHTML = '<i class="fas fa-chevron-right"></i>';
                        tableContainer.parentNode.appendChild(rightIndicator);
                    }

                    if (!isAtStart) {
                        const leftIndicator = document.createElement('div');
                        leftIndicator.className = 'scroll-indicator scroll-left';
                        leftIndicator.innerHTML = '<i class="fas fa-chevron-left"></i>';
                        tableContainer.parentNode.appendChild(leftIndicator);
                    }
                }
            }

            // Update indicators on scroll and resize
            tableContainer.addEventListener('scroll', updateScrollIndicators);
            window.addEventListener('resize', updateScrollIndicators);
            updateScrollIndicators(); // Initial call
        }
    });
</script>

<style>
    .date_input{
        font-size: 18px!important;border-radius: 40px!important;width: 150px;color:#fff!important;background-image: linear-gradient(90deg, #23384E, #166686)!important;
        cursor: pointer !important;
    }
    .date_input:hover{
        background-image: linear-gradient(90deg, #166686, #23384E)!important;
    }
    
    .status-report-table {
        font-size: 12px;
    }
    
    .employee-header {
        min-width: 150px;
        background-color: #e9ecef !important;
        vertical-align: middle;
    }
    
    .employee-name {
        background-color: #f8f9fa;
        min-width: 150px;
        vertical-align: middle;
    }
    
    .date-header {
        min-width: 35px;
        text-align: center;
        background-color: #e9ecef !important;
    }
    
    .summary-header {
        min-width: 60px;
        text-align: center;
        background-color: #d1ecf1 !important;
    }
    
    .status-cell {
        text-align: center;
        vertical-align: middle;
        width: 35px;
        height: 35px;
        padding: 5px !important;
    }
    
    .summary-cell {
        text-align: center;
        vertical-align: middle;
        background-color: #f8f9fa;
    }
    
    .status-updated {
        background-color: #d4edda !important;
    }
    
    .status-partially {
        background-color: #fff3cd !important;
    }
    
    .status-not-updated {
        background-color: #f8d7da !important;
    }
    
    .status-not-applicable {
        background-color: #e2e3e5 !important;
    }
    
    .status-legend {
        display: inline-block;
        width: 20px;
        height: 15px;
        margin-right: 5px;
        border: 1px solid #ccc;
    }
    
    .table-responsive-horizontal {
        max-height: 600px;
        overflow: auto;
        white-space: nowrap;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }

    .table-responsive-horizontal .table {
        margin-bottom: 0;
        min-width: 100%;
    }

    .table-responsive-horizontal .table thead th {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
    }

    .table-responsive-horizontal .employee-name,
    .table-responsive-horizontal .employee-header {
        position: sticky;
        left: 0;
        z-index: 11;
        background-color: #f8f9fa;
        border-right: 2px solid #dee2e6;
        min-width: 150px;
        max-width: 150px;
    }

    .table-responsive-horizontal .employee-name {
        background-color: #ffffff;
    }

    /* Ensure proper scrolling behavior */
    .table-responsive-horizontal::-webkit-scrollbar {
        height: 8px;
        width: 8px;
    }

    .table-responsive-horizontal::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .table-responsive-horizontal::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
    }

    .table-responsive-horizontal::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* Scroll indicators */
    .scroll-indicator {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 10px 8px;
        border-radius: 4px;
        z-index: 1000;
        pointer-events: none;
        font-size: 14px;
        animation: fadeInOut 2s infinite;
    }

    .scroll-indicator.scroll-right {
        right: 10px;
    }

    .scroll-indicator.scroll-left {
        left: 160px; /* Account for sticky employee column */
    }

    @keyframes fadeInOut {
        0%, 100% { opacity: 0.7; }
        50% { opacity: 1; }
    }

    /* Focus styles for keyboard navigation */
    .table-responsive-horizontal:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
    }

    /* Improved mobile responsiveness */
    @media (max-width: 768px) {
        .table-responsive-horizontal .employee-name,
        .table-responsive-horizontal .employee-header {
            min-width: 120px;
            max-width: 120px;
            font-size: 12px;
        }

        .date-header {
            min-width: 30px;
            font-size: 10px;
        }

        .status-cell {
            width: 30px;
            height: 30px;
            padding: 3px !important;
        }

        .scroll-indicator.scroll-left {
            left: 130px;
        }
    }
</style>
