<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
    </div>

    <!-- Filters Section -->
    <div class="mt-3" style="margin:14px;">
        <div class="card shadow-pro">
            <div class="card-header">
                <h5 class="card-title">Filters</h5>
            </div>
            <div class="card-body">
                <form action="<?= base_url('app/aptitude_summary'); ?>" method="get" class="row">
                    <div class="col-md-3 form-group">
                        <label for="start_date">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                    </div>
                    <div class="col-md-3 form-group">
                        <label for="end_date">End Date</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                    </div>
                    <div class="col-md-3 form-group">
                        <label for="district">District</label>
                        <select class="form-control" id="district" name="district">
                            <option value="">All Districts</option>
                            <?php foreach ($districts as $district): ?>
                                <option value="<?= $district ?>" <?= ($selected_district == $district) ? 'selected' : '' ?>>
                                    <?= $district ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3 form-group d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">Apply Filters</button>
                        <a href="<?= base_url('app/aptitude_summary'); ?>" class="btn btn-outline-secondary ml-2">Reset</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Summary Stats -->
    <div class="mt-3" style="margin:14px;">
        <div class="row text-center bg-white p-2" style="border-radius: 10px">
            <div class="col-6 col-lg-3 p-1 pb-0">
                <div class="shadow_pro bg-white p-2 pb-0">
                    <div class="text-primary p-2" style="background-color: #eaf3fd;border-radius: 8px;">
                        <span style="font-size: 28px; font-weight: bold"><?= count($candidates) ?></span>
                        <div class="mt-0 pt-0" style="margin-top: -5px!important;">
                            <small>TOTAL CANDIDATES</small>
                        </div>
                    </div>
                </div>
            </div>

            <?php
            // Count candidates by status
            $status_counts = [
                'registered' => 0,
                'completed' => 0,
                'evaluated' => 0
            ];

            foreach ($candidates as $candidate) {
                if (isset($candidate['status']) && isset($status_counts[$candidate['status']])) {
                    $status_counts[$candidate['status']]++;
                }
            }
            ?>

            <div class="col-6 col-lg-3 p-1 pb-0">
                <div class="shadow_pro bg-white p-2 pb-0">
                    <div class="text-warning p-2" style="background-color: #f8f4ec;border-radius: 8px;">
                        <span style="font-size: 28px; font-weight: bold"><?= $status_counts['registered'] ?></span>
                        <div class="mt-0 pt-0" style="margin-top: -5px!important;">
                            <small>REGISTERED</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-6 col-lg-3 p-1 pb-0">
                <div class="shadow_pro bg-white p-2 pb-0">
                    <div class="text-info p-2" style="background-color: #e5f4f9;border-radius: 8px;">
                        <span style="font-size: 28px; font-weight: bold"><?= $status_counts['completed'] ?></span>
                        <div class="mt-0 pt-0" style="margin-top: -5px!important;">
                            <small>COMPLETED</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-6 col-lg-3 p-1 pb-0">
                <div class="shadow_pro bg-white p-2 pb-0">
                    <div class="text-success p-2" style="background-color: #e5f9e7;border-radius: 8px;">
                        <span style="font-size: 28px; font-weight: bold"><?= $status_counts['evaluated'] ?></span>
                        <div class="mt-0 pt-0" style="margin-top: -5px!important;">
                            <small>EVALUATED</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Candidates List -->
    <div class="mt-3" style="margin:14px;">
        <div class="card shadow-pro">
            <div class="card-header">
                <h5 class="card-title">Aptitude Test Candidates</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="candidates-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>District</th>
                                <th>Test Date</th>
                                <th>Status</th>
                                <th>Ratings</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($candidates)): ?>
                                <tr>
                                    <td colspan="9" class="text-center">No candidates found</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($candidates as $candidate): ?>
                                    <tr>
                                        <td><?= $candidate['id'] ?></td>
                                        <td><?= $candidate['fullname'] ?></td>
                                        <td><?= $candidate['email'] ?></td>
                                        <td><?= $candidate['phone'] ?></td>
                                        <td><?= $candidate['district'] ?></td>
                                        <td><?= date('d M Y', strtotime($candidate['created_at'])) ?></td>
                                        <td>
                                            <?php
                                            $status_badge = '';
                                            switch ($candidate['status']) {
                                                case 'registered':
                                                    $status_badge = 'badge-warning';
                                                    break;
                                                case 'completed':
                                                    $status_badge = 'badge-info';
                                                    break;
                                                case 'evaluated':
                                                    $status_badge = 'badge-success';
                                                    break;
                                                default:
                                                    $status_badge = 'badge-secondary';
                                            }
                                            ?>
                                            <span class="badge <?= $status_badge ?>"><?= ucfirst($candidate['status']) ?></span>
                                        </td>
                                        <td>
                                            <?php 
                                            $candidate['rating_total'] = $candidate['rating_total']*10;
                                            if (isset($candidate['rating_total']) && $candidate['rating_total'] > 0): ?>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress" style="height: 8px; width: 80px;">
                                                        <div class="progress-bar bg-success" role="progressbar"
                                                            style="width: <?= min(100, $candidate['rating_total']) ?>%;"
                                                            aria-valuenow="<?= $candidate['rating_total'] ?>"
                                                            aria-valuemin="0"
                                                            aria-valuemax="100">
                                                        </div>
                                                    </div>
                                                    <span class="ml-2"><?= $candidate['rating_total'] ?>/100</span>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">Not rated</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="action-column">
                                            <div class="action-buttons">
                                                <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$candidate['id'].'/?page_name=aptitude_summary/view_answers_modal'); ?>', '<?= get_phrase('Candidate Answers: ' . $candidate['fullname']); ?>')"
                                                        class="btn btn-primary btn-sm"
                                                        title="View Answers">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <a href="<?=base_url('app/aptitude_summary/process_answers/'.$candidate['id'])?>" class="btn-sm btn btn-info">Process AI</a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#candidates-table').DataTable({
        "order": [[0, "desc"]],
        "pageLength": 25
    });
});
</script>
