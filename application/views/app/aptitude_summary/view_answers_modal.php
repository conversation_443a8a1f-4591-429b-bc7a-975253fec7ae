<?php
// Get candidate details
$candidate = $this->aptitude_candidates_m->get(['id' => $param1])->row_array();

// Get all answers for this candidate
$answers = $this->aptitude_candidate_answers_m->get(['candidate_id' => $param1])->result_array();

// Get all questions
$questions = [];
$question_ids = array_column($answers, 'question_id');

if (!empty($question_ids)) {
    $questions_data = $this->aptitude_questions_m->get()->result_array();
    foreach ($questions_data as $question) {
        $questions[$question['id']] = $question;
    }
}

// If questions table doesn't have the data, create a mock structure
// This is for backward compatibility or if questions are hardcoded in the view
if (empty($questions)) {
    // Create mock questions based on the form structure
    $mock_questions = [
        1 => ['id' => 1, 'question_text' => 'What\'s the difference between `#header { … }` and `.header { … }` in CSS?', 'question_type' => 'multiple_choice', 'options' => json_encode([
            'a' => '`#header` targets elements with id="header"; `.header` targets elements with class="header"',
            'b' => '`#header` targets elements with class "header"; `.header` targets elements with id "header"',
            'c' => '`#header` can apply to multiple elements; `.header` only to one',
            'd' => 'There is no difference'
        ])],
        2 => ['id' => 2, 'question_text' => 'Which method would you use to select the element with id="main"?', 'question_type' => 'multiple_choice'],
        // Add more mock questions as needed
    ];

    // Merge with existing questions
    $questions = array_merge($questions, $mock_questions);
}
?>

<!-- Candidate Information -->
<div class="card mb-3">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">Candidate Information</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Name</th>
                        <td><?= $candidate['fullname'] ?></td>
                    </tr>
                    <tr>
                        <th>Email</th>
                        <td><?= $candidate['email'] ?></td>
                    </tr>
                    <tr>
                        <th>Phone</th>
                        <td><?= $candidate['phone'] ?></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">District</th>
                        <td><?= $candidate['district'] ?></td>
                    </tr>
                    <tr>
                        <th>Location</th>
                        <td><?= $candidate['location'] ?></td>
                    </tr>
                    <tr>
                        <th>Test Date</th>
                        <td><?= date('d M Y H:i', strtotime($candidate['created_at'])) ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <?php if (isset($candidate['rating_total']) && $candidate['rating_total'] > 0): ?>
        <div class="row mt-3">
            <div class="col-12">

                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3><?= $candidate['rating_total'] ?? 'N/A' ?></h3>
                                <p class="mb-0">Total Rating</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3><?= $candidate['rating_ai'] ?? 'N/A' ?></h3>
                                <p class="mb-0">AI Rating</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3><?= $candidate['rating_technical'] ?? 'N/A' ?></h3>
                                <p class="mb-0">Technical</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3><?= $candidate['rating_communication'] ?? 'N/A' ?></h3>
                                <p class="mb-0">Communication</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if (!empty($candidate['remarks']) || !empty($candidate['suggested_questions']) || !empty($candidate['remarks_interviewer'])): ?>
        <div class="row mt-3">
            <div class="col-12">
                <ul class="nav nav-tabs" id="remarksTab" role="tablist">
                    <?php if (!empty($candidate['remarks'])): ?>
                    <li class="nav-item">
                        <a class="nav-link active" id="remarks-tab" data-toggle="tab" href="#remarks" role="tab" aria-controls="remarks" aria-selected="true">General Remarks</a>
                    </li>
                    <?php endif; ?>

                    <?php if (!empty($candidate['suggested_questions'])): ?>
                    <li class="nav-item">
                        <a class="nav-link <?= empty($candidate['remarks']) ? 'active' : '' ?>" id="questions-tab" data-toggle="tab" href="#questions" role="tab" aria-controls="questions" aria-selected="false">Suggested Questions</a>
                    </li>
                    <?php endif; ?>

                    <?php if (!empty($candidate['remarks_interviewer'])): ?>
                    <li class="nav-item">
                        <a class="nav-link <?= (empty($candidate['remarks']) && empty($candidate['suggested_questions'])) ? 'active' : '' ?>" id="interviewer-tab" data-toggle="tab" href="#interviewer" role="tab" aria-controls="interviewer" aria-selected="false">Interviewer Remarks</a>
                    </li>
                    <?php endif; ?>
                </ul>
                <div class="tab-content p-3 border border-top-0 rounded-bottom" id="remarksTabContent">
                    <?php if (!empty($candidate['remarks'])): ?>
                    <div class="tab-pane fade show active" id="remarks" role="tabpanel" aria-labelledby="remarks-tab">
                        <?= nl2br(htmlspecialchars($candidate['remarks'])) ?>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($candidate['suggested_questions'])): ?>
                    <div class="tab-pane fade <?= empty($candidate['remarks']) ? 'show active' : '' ?>" id="questions" role="tabpanel" aria-labelledby="questions-tab">
                        <?= nl2br(htmlspecialchars($candidate['suggested_questions'])) ?>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($candidate['remarks_interviewer'])): ?>
                    <div class="tab-pane fade <?= (empty($candidate['remarks']) && empty($candidate['suggested_questions'])) ? 'show active' : '' ?>" id="interviewer" role="tabpanel" aria-labelledby="interviewer-tab">
                        <?= nl2br(htmlspecialchars($candidate['remarks_interviewer'])) ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Answers Section -->
<h5 class="">Test Answers</h5>
<div class="pt-1">
    <?php if (empty($answers)): ?>
        <div class="alert alert-info">No answers found for this candidate.</div>
    <?php else: ?>
        <?php
        // Sort answers by question_id
        $sorted_answers = [];
        foreach ($answers as $answer) {
            $sorted_answers[$answer['question_id']] = $answer;
        }
        ksort($sorted_answers);

        foreach ($sorted_answers as $question_id => $answer):
            $question = $questions[$question_id] ?? null;
            $question_text = $question ? $question['question_text'] : "Question {$question_id}";
            $question_type = $question ? $question['question_type'] : 'short_answer';
            $options = [];

            if ($question && isset($question['options']) && !empty($question['options'])) {
                // Try to decode JSON options
                $decoded_options = json_decode($question['options'], true);
                if (is_array($decoded_options)) {
                    $options = $decoded_options;
                }
            }
        ?>
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h5 class="mb-0" style="line-height: 1.6em;">
                        <span class="text-primary">Q<?= $question_id ?>:</span> <?= htmlspecialchars($question_text) ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($question_type == 'multiple_choice' && !empty($options)): ?>
                        <div class="mb-3">
                            <strong>Options:</strong>
                            <ul class="list-group mt-2">
                                <?php foreach ($options as $key => $option_text): ?>
                                    <li class="list-group-item <?= ($answer['answer'] == $key) ? 'list-group-item-primary' : '' ?>">
                                        <?php if ($answer['answer'] == $key): ?>
                                            <i class="fas fa-check-circle text-success mr-2"></i>
                                        <?php endif; ?>
                                        <strong><?= $option_text['value'] ?>:</strong> <?= htmlspecialchars($option_text['text']) ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <div>
                            <strong>Candidate's Answer:</strong>
                            <span class="badge badge-primary"><?= $answer['answer'] ?></span>
                            <?php if (isset($options[$answer['answer']])): ?>
                                - <?= htmlspecialchars($options[$answer['answer']]) ?>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div>
                            <strong>Candidate's Answer:</strong>
                            <div class="p-3 bg-light mt-2 border rounded">
                                <?= nl2br(htmlspecialchars($answer['answer'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($answer['is_correct'])): ?>
                        <div class="mt-3">
                            <strong>Correctness:</strong>
                            <?php if ($answer['is_correct'] == 1): ?>
                                <span class="badge badge-success">Correct</span>
                            <?php elseif ($answer['is_correct'] == 0): ?>
                                <span class="badge badge-danger">Incorrect</span>
                            <?php else: ?>
                                <span class="badge badge-secondary">Not Evaluated</span>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($answer['score']) && $answer['score'] !== null): ?>
                        <div class="mt-3">
                            <strong>Score:</strong> <?= $answer['score'] ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($answer['evaluator_comment']) && !empty($answer['evaluator_comment'])): ?>
                        <div class="mt-3">
                            <strong>Evaluator Comment:</strong>
                            <div class="p-3 bg-light mt-2 border rounded">
                                <?= nl2br(htmlspecialchars($answer['evaluator_comment'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>


