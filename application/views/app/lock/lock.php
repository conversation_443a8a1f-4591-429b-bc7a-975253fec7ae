<div class="lockscreen-wrapper p-4" style="max-width:900px">
    <div class="text-center p-3">
        <i class="fas fa-lock fa-2x text-danger"></i>
    </div>
    <div class="lockscreen-logo">
        
      <span class="text-danger" style="font-size:26px;"><b>Dashboard is blocked!</b></span>
    </div>
    <div class="lockscreen-name text-center" style="font-size:17px;line-height:1.5em">
        PMS records indicate that the work status update for the previous day was not received. <br>Consequently, access to your dashboard has been <b>temporarily blocked</b>. <hr>
        Kindly provide a brief explanation below to facilitate review of your request to restore access. <br>
        Your prompt attention to this matter is appreciated.
    </div>
    
    <!-- Main Blocked Message -->
    <div style="max-width:500px;margin:auto;">
      <!-- Lock Screen Form (Explanation Request) -->
      
      <?php 
        if(!$is_review){
            ?>
            <form action="" id="explanationForm" method="post">
                <div class="p-2">
                  <!-- This input can be replaced by a textarea if a longer explanation is desired -->
                  <div class="p-1">
                      <textarea class="form-control" name="explanation" id="explanationInput" placeholder="Explnataion" style="font-size:18px;height:140px;"></textarea>
                  </div>
                  <div class="p-1 text-center">
                      <button type="submit" class="btn btn-primary p-2" style="min-width:280px;margin-auto!important;">Submit Request</button>
                  </div>
                </div>
              </form>
            <?php
        }else{
            ?>
            <div class="text-center text-danger p-2">
                <h3>Your request to unlock is under review.</h3>
            </div>
            <?php
        }
      ?>
      
    </div>
</div>

<script>
    document.getElementById('explanationForm').addEventListener('submit', function(event) {
      // Get the explanation text and trim any whitespace
      var explanation = document.getElementById('explanationInput').value.trim();
      // Split the text into words using whitespace as a delimiter
      var words = explanation.split(/\s+/).filter(function(word) {
        return word.length > 0;
      });

      // Check if the explanation has at least 10 words
      if (words.length < 10) {
        message_error("Your explanation must contain at least 10 words. Please provide more details.");
        // Prevent the form from being submitted
        event.preventDefault();
      }
    });
  </script>