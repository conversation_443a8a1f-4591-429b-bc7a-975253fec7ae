<?php
$item_id = $param1;
$ticket_data = $this->tickets_m->get(['id' => $item_id])->row_array();
?>
<form class="form-horizontal" action="<?=base_url('app/tickets/change_status/'.$item_id)?>" method="post">
    <div class="row" style="margin: 0!important;">
        <div class="col-12 mb-3">
            <div class="alert alert-info">
                <strong>Ticket:</strong> <?= $ticket_data['title'] ?><br>
                <strong>Current Status:</strong> 
                <?php
                switch($ticket_data['status']) {
                    case 'new':
                        echo '<span class="badge badge-info">NEW</span>';
                        break;
                    case 'assigned':
                        echo '<span class="badge badge-warning">ASSIGNED</span>';
                        break;
                    case 'closed':
                        echo '<span class="badge badge-success">CLOSED</span>';
                        break;
                    case 'on_hold':
                        echo '<span class="badge badge-secondary">ON HOLD</span>';
                        break;
                    case 're_open':
                        echo '<span class="badge badge-danger">RE-OPEN</span>';
                        break;
                }
                ?>
            </div>
        </div>
        
        <div class="form-group col-12 p-0">
            <label for="status" class="col-sm-12 col-form-label text-muted">New Status <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="status" name="status" required>
                    <option value="">Choose Status</option>
                    <option value="new" <?= $ticket_data['status'] == 'new' ? 'selected' : '' ?>>New</option>
                    <option value="assigned" <?= $ticket_data['status'] == 'assigned' ? 'selected' : '' ?>>Assigned</option>
                    <option value="closed" <?= $ticket_data['status'] == 'closed' ? 'selected' : '' ?>>Closed</option>
                    <option value="on_hold" <?= $ticket_data['status'] == 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                    <option value="re_open" <?= $ticket_data['status'] == 're_open' ? 'selected' : '' ?>>Re-open</option>
                </select>
                <small class="text-muted">Select the new status for this ticket</small>
            </div>
        </div>
    </div>

    <div class="col-12">
        <button type="submit" name="update_status" value="Update" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
            <small><i class="fa fa-check"></i></small> Update
        </button>
    </div>
</form>

<script type="application/javascript">
$('.select2').select2();
</script>