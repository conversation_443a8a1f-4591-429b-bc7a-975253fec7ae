<?php
    $projects = array_column($this->projects_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
    $users = $this->users_m->get(['employee_status' => 1, 'is_employee' => 1], ['id', 'name'])->result_array();
    $reported_to_users = $this->users_m->get(['employee_status' => 1], ['id', 'name'])->result_array();
?>

<form id="ajax_ticket_form" class="form-horizontal" method="post" enctype="multipart/form-data">
<div class="row" style="margin: 0!important;">
    <div class="form-group col-12 p-0">
        <label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
        <div class="col-sm-12">
            <input type="text" class="form-control" id="title" name="title" value="" placeholder="Ticket Title" required>
            <div class="invalid-feedback"></div>
        </div>
    </div>
    
    <div class="form-group col-4 p-0">
        <label for="project_id" class="col-sm-12 col-form-label text-muted">Project <span class="text-danger">*</span></label>
        <div class="col-sm-12">
            <select class="form-control select2" id="project_id" name="project_id" required>
                <option value="">Choose Project</option>
                <?php
                    foreach ($projects as $project_id => $project) {
                        echo "<option value='{$project_id}'>{$project}</option>";
                    }
                ?>
            </select>
            <div class="invalid-feedback"></div>
        </div>
    </div>

    <div class="form-group col-4 p-0">
        <label for="type" class="col-sm-12 col-form-label text-muted">Type <span class="text-danger">*</span></label>
        <div class="col-sm-12">
            <select class="form-control select2" id="type" name="type" required>
                <option value="">Choose Type</option>
                <option value="new">New</option>
                <option value="bug">Bug</option>
                <option value="support">Support</option>
                <option value="quick_support">Quick Support</option>
                <option value="queries">Queries</option>
                <option value="critical_bug">Critical Bug</option>
            </select>
            <div class="invalid-feedback"></div>
        </div>
    </div>

    <div class="form-group col-4 p-0">
        <label for="priority" class="col-sm-12 col-form-label text-muted">Priority <span class="text-danger">*</span></label>
        <div class="col-sm-12">
            <select class="form-control select2" id="priority" name="priority" required>
                <option value="">Choose Priority</option>
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
            </select>
            <div class="invalid-feedback"></div>
        </div>
    </div>

    <div class="form-group col-4 p-0">
        <label for="estimated_time" class="col-sm-12 col-form-label text-muted">Expected Time</label>
        <div class="col-sm-12">
            <div class="input-group">
                <input type="number" class="form-control" id="estimated_hours" name="estimated_hours" min="0" max="999" placeholder="Hours">
                <input type="number" class="form-control" id="estimated_minutes" name="estimated_minutes" min="0" max="59" placeholder="Minutes">
                <div class="input-group-append">
                    <span class="input-group-text">H:M</span>
                </div>
            </div>
            <small class="form-text text-muted">Enter expected time to complete this ticket</small>
            <input type="hidden" id="estimated_time" name="estimated_time" value="">
        </div>
    </div>

    <div class="form-group col-4 p-0">
        <label for="ticket_via" class="col-sm-12 col-form-label text-muted">Ticket Via</label>
        <div class="col-sm-12">
            <select class="form-control select2" id="ticket_via" name="ticket_via">
                <option value="">Choose Method</option>
                <option value="group">Group</option>
                <option value="message">Message</option>
                <option value="call">Call</option>
                <option value="other">Other</option>
            </select>
        </div>
    </div>

    <div class="form-group col-4 p-0">
        <label for="reported_to" class="col-sm-12 col-form-label text-muted">Reported To</label>
        <div class="col-sm-12">
            <select class="form-control select2" id="reported_to" name="reported_to">
                <option value="">Choose User</option>
                <?php
                    $current_user_id = get_user_id();
                    foreach ($reported_to_users as $user) {
                        $selected = ($user['id'] == $current_user_id) ? 'selected' : '';
                        echo "<option value='{$user['id']}' {$selected}>{$user['name']}</option>";
                    }
                ?>
            </select>
        </div>
    </div>

    <div class="form-group col-4 p-0">
        <label for="reported_by" class="col-sm-12 col-form-label text-muted">Reported By</label>
        <div class="col-sm-12">
            <input type="text" class="form-control" id="reported_by" name="reported_by" value="" placeholder="Contact person name/phone">
        </div>
    </div>
    


    
    <div class="form-group col-12 p-0">
        <label for="description" class="col-sm-12 col-form-label text-muted">Description</label>
        <div class="col-sm-12">
            <textarea class="form-control" id="description" name="description" rows="4" placeholder="Ticket Description"></textarea>
        </div>
    </div>
</div>

<!-- Loading indicator -->
<div id="ajax_loading" class="text-center" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
    </div>
    <p class="mt-2">Adding ticket...</p>
</div>

<!-- Success/Error messages -->
<div id="ajax_message" style="display: none;"></div>

<div class="col-12">
    <button type="button" id="ajax_submit_btn" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
        <small><i class="fa fa-check"></i></small> Save
    </button>
</div>
</form>

<script type="application/javascript">
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#ajax_ticket_form').closest('.modal-body')
    });

    // Time conversion functionality
    function updateEstimatedTime() {
        const hours = parseInt($('#estimated_hours').val()) || 0;
        const minutes = parseInt($('#estimated_minutes').val()) || 0;
        const totalMinutes = (hours * 60) + minutes;
        $('#estimated_time').val(totalMinutes > 0 ? totalMinutes : '');
    }

    // Update estimated time when hours or minutes change
    $('#estimated_hours, #estimated_minutes').on('input change', updateEstimatedTime);

    // AJAX form submission
    $('#ajax_submit_btn').on('click', function(e) {
        e.preventDefault();
        
        // Clear previous validation errors
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').text('');
        $('#ajax_message').hide();
        
        // Validate required fields
        let isValid = true;
        let requiredFields = ['title', 'project_id', 'type', 'priority'];
        
        requiredFields.forEach(function(fieldName) {
            let field = $('[name="' + fieldName + '"]');
            if (!field.val()) {
                field.addClass('is-invalid');
                field.siblings('.invalid-feedback').text('This field is required');
                isValid = false;
            }
        });
        
        if (!isValid) {
            return false;
        }
        
        // Show loading
        $('#ajax_loading').show();
        $('#ajax_submit_btn').prop('disabled', true);
        
        // Prepare form data
        let formData = new FormData($('#ajax_ticket_form')[0]);
        
        // AJAX request
        $.ajax({
            url: '<?= base_url("app/tickets/ajax_add") ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                $('#ajax_loading').hide();
                $('#ajax_submit_btn').prop('disabled', false);
                
                if (response.status === 'success') {
                    // Show success message
                    $('#ajax_message').html(
                        '<div class="alert alert-success alert-dismissible">' +
                        '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                        '<i class="fas fa-check-circle"></i> ' + response.message +
                        '</div>'
                    ).show();
                    
                    // Reset form
                    $('#ajax_ticket_form')[0].reset();
                    $('.select2').val(null).trigger('change');
                    $('#ticket_date').val('<?= date('Y-m-d') ?>');
                    
                    // Show success toast
                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message);
                    }
                    
                    // Optionally close modal after delay
                    setTimeout(function() {
                        $('.modal').modal('hide');
                        // Refresh the page or update the tickets list
                        if (typeof refreshTicketsList === 'function') {
                            refreshTicketsList();
                        } else {
                            location.reload();
                        }
                    }, 1500);
                    
                } else {
                    // Show error message
                    $('#ajax_message').html(
                        '<div class="alert alert-danger alert-dismissible">' +
                        '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                        '<i class="fas fa-exclamation-circle"></i> ' + response.message +
                        '</div>'
                    ).show();
                    
                    if (typeof toastr !== 'undefined') {
                        toastr.error(response.message);
                    }
                }
            },
            error: function(xhr, status, error) {
                $('#ajax_loading').hide();
                $('#ajax_submit_btn').prop('disabled', false);
                
                let errorMessage = 'An error occurred while adding the ticket.';
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseText) {
                    try {
                        let response = JSON.parse(xhr.responseText);
                        errorMessage = response.message || errorMessage;
                    } catch (e) {
                        errorMessage = 'Server error: ' + xhr.status;
                    }
                }
                
                $('#ajax_message').html(
                    '<div class="alert alert-danger alert-dismissible">' +
                    '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                    '<i class="fas fa-exclamation-circle"></i> ' + errorMessage +
                    '</div>'
                ).show();
                
                if (typeof toastr !== 'undefined') {
                    toastr.error(errorMessage);
                }
            }
        });
    });
    
    // Real-time validation
    $('input[required], select[required]').on('change keyup', function() {
        if ($(this).val()) {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').text('');
        }
    });
});

// Function to refresh tickets list (can be called from parent page)
function refreshTicketsList() {
    // This function can be implemented in the parent page to refresh the tickets list
    // without full page reload
    if (typeof updateTicketsDisplay === 'function') {
        updateTicketsDisplay();
    } else {
        location.reload();
    }
}
</script>

<style>
.spinner-border {
    width: 2rem;
    height: 2rem;
}

.alert {
    margin-top: 15px;
}

.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.select2-container .select2-selection--single {
    height: 38px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
}
</style>