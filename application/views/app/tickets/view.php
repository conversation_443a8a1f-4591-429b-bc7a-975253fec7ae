<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/tickets/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Back to Tickets
        </a>
        
        <?php if (has_permission('tickets/edit')): ?>
            <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$ticket['id'].'/?page_name=tickets/edit'); ?>', 'Edit Ticket')"
                    class="btn btn-info btn-mini float-right">
                <small><i class="fas fa-pencil-alt"></i></small> Edit Ticket
            </button>
        <?php endif; ?>
    </div>
    
    <div class="row" style="margin:14px;">
        <!-- Ticket Details Card -->
        <div class="col-md-8">
            <div class="card card-primary shadow-pro">
                <div class="card-header">
                    <h3 class="card-title">Ticket Details</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <h4 class="text-primary"><?= $ticket['title'] ?></h4>
                            <hr>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Project:</strong><br>
                            <span class="text-muted"><?= $ticket['project_title'] ?? 'N/A' ?></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Type:</strong><br>
                            <?= get_task_type($ticket['type']) ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Priority:</strong><br>
                            <?= get_task_priority($ticket['priority']) ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Status:</strong><br>
                            <?php
                            switch($ticket['status']) {
                                case 'new':
                                    echo '<span class="badge badge-info badge-lg">NEW</span>';
                                    break;
                                case 'assigned':
                                    echo '<span class="badge badge-warning badge-lg">ASSIGNED</span>';
                                    break;
                                case 'closed':
                                    echo '<span class="badge badge-success badge-lg">CLOSED</span>';
                                    break;
                                case 'on_hold':
                                    echo '<span class="badge badge-secondary badge-lg">ON HOLD</span>';
                                    break;
                                case 're_open':
                                    echo '<span class="badge badge-danger badge-lg">RE-OPEN</span>';
                                    break;
                                default:
                                    echo '<span class="badge badge-light badge-lg">UNKNOWN</span>';
                            }
                            ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Assigned To:</strong><br>
                            <span class="text-muted"><?= $ticket['assigned_user_name'] ?? 'Unassigned' ?></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Reported By:</strong><br>
                            <span class="text-muted"><?= $ticket['reported_by'] ?? 'N/A' ?></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Reported To:</strong><br>
                            <span class="text-muted"><?= isset($users[$ticket['reported_to']]) ? $users[$ticket['reported_to']] : 'N/A' ?></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Ticket Via:</strong><br>
                            <span class="text-muted"><?= ucfirst($ticket['ticket_via'] ?? 'N/A') ?></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Ticket Date:</strong><br>
                            <span class="text-muted"><?= (!empty($ticket['ticket_date']) && $ticket['ticket_date'] !== '0000-00-00 00:00:00') ? date('d-m-Y H:i', strtotime($ticket['ticket_date'])) : 'N/A' ?></span>
                        </div>
                        
                        <?php if (!empty($ticket['close_date']) && $ticket['close_date'] !== '0000-00-00 00:00:00'): ?>
                        <div class="col-md-6 mb-3">
                            <strong>Close Date:</strong><br>
                            <span class="text-muted"><?= date('d-m-Y H:i', strtotime($ticket['close_date'])) ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($ticket['closed_by']): ?>
                        <div class="col-md-6 mb-3">
                            <strong>Closed By:</strong><br>
                            <span class="text-muted"><?= isset($users[$ticket['closed_by']]) ? $users[$ticket['closed_by']] : 'N/A' ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($ticket['description'])): ?>
                        <div class="col-12">
                            <strong>Description:</strong><br>
                            <div class="bg-light p-3 rounded mt-2">
                                <?= nl2br(htmlspecialchars($ticket['description'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions Card -->
        <div class="col-md-4">
            <div class="card card-secondary shadow-pro">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div class="card-body">
                    <?php if ($ticket['status'] !== 'closed'): ?>
                        <?php if (has_permission('tickets/edit')): ?>
                            <form action="<?= base_url('app/tickets/change_status/'.$ticket['id']) ?>" method="post" class="mb-3">
                                <div class="form-group">
                                    <label for="status">Change Status:</label>
                                    <select name="status" id="status" class="form-control" required>
                                        <option value="">Select Status</option>
                                        <option value="new" <?= $ticket['status'] == 'new' ? 'selected' : '' ?>>New</option>
                                        <option value="assigned" <?= $ticket['status'] == 'assigned' ? 'selected' : '' ?>>Assigned</option>
                                        <option value="closed" <?= $ticket['status'] == 'closed' ? 'selected' : '' ?>>Closed</option>
                                        <option value="on_hold" <?= $ticket['status'] == 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                                        <option value="re_open" <?= $ticket['status'] == 're_open' ? 'selected' : '' ?>>Re-open</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary btn-sm btn-block">
                                    <i class="fas fa-sync"></i> Update Status
                                </button>
                            </form>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <div class="text-center">
                        <small class="text-muted">
                            <strong>Created:</strong><br>
                            <?= date('d-m-Y g:i A', strtotime($ticket['created_on'])) ?><br>
                            by <?= isset($users[$ticket['created_by']]) ? $users[$ticket['created_by']] : 'System' ?>
                        </small>
                        
                        <?php if ($ticket['updated_on'] && $ticket['updated_on'] != $ticket['created_on']): ?>
                            <hr>
                            <small class="text-muted">
                                <strong>Last Updated:</strong><br>
                                <?= date('d-m-Y g:i A', strtotime($ticket['updated_on'])) ?><br>
                                by <?= isset($users[$ticket['updated_by']]) ? $users[$ticket['updated_by']] : 'System' ?>
                            </small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Ticket Info Card -->
            <div class="card card-info shadow-pro">
                <div class="card-header">
                    <h3 class="card-title">Ticket Information</h3>
                </div>
                <div class="card-body">
                    <div class="info-box">
                        <span class="info-box-icon bg-info"><i class="fas fa-ticket-alt"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Ticket ID</span>
                            <span class="info-box-number">#<?= str_pad($ticket['id'], 6, '0', STR_PAD_LEFT) ?></span>
                        </div>
                    </div>
                    
                    <?php if ($ticket['status'] == 'closed' && !empty($ticket['close_date']) && $ticket['close_date'] !== '0000-00-00 00:00:00' && !empty($ticket['created_on'])): ?>
                        <?php $resolution_display = get_ticket_resolution_time($ticket['created_on'], $ticket['close_date']); ?>
                        <div class="info-box">
                            <span class="info-box-icon bg-success"><i class="fas fa-clock"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Resolution Time</span>
                                <span class="info-box-number"><?= $resolution_display ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Comments/History Section -->
        <div class="col-12 mt-4">
            <div class="card card-success shadow-pro">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-comments"></i> Comments & History
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Add Comment Form -->
                    <?php if (has_permission('tickets/edit')): ?>
                    <div class="mb-4">
                        <form id="add_comment_form" class="border rounded p-3 bg-light">
                            <div class="form-group">
                                <label for="comment_text"><strong>Add Comment:</strong></label>
                                <textarea name="remarks" id="comment_text" class="form-control" rows="3"
                                          placeholder="Enter your comment here..." required></textarea>
                            </div>

                            <!-- Status Update Option -->
                            <div class="form-group status-update-section">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="update_status_checkbox">
                                            <label class="custom-control-label" for="update_status_checkbox">
                                                <i class="fas fa-sync-alt"></i> <strong>Also update ticket status</strong>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <select name="new_status" id="new_status" class="form-control" disabled>
                                            <option value="">Select New Status</option>
                                            <option value="new" <?= $ticket['status'] == 'new' ? 'selected' : '' ?>>New</option>
                                            <option value="assigned" <?= $ticket['status'] == 'assigned' ? 'selected' : '' ?>>Assigned</option>
                                            <option value="closed" <?= $ticket['status'] == 'closed' ? 'selected' : '' ?>>Closed</option>
                                            <option value="on_hold" <?= $ticket['status'] == 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                                            <option value="re_open" <?= $ticket['status'] == 're_open' ? 'selected' : '' ?>>Re-open</option>
                                        </select>
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i> Current:
                                            <span class="badge badge-secondary">
                                                <?php
                                                $status_labels = [
                                                    'new' => 'New',
                                                    'assigned' => 'Assigned',
                                                    'closed' => 'Closed',
                                                    'on_hold' => 'On Hold',
                                                    're_open' => 'Re-open'
                                                ];
                                                echo $status_labels[$ticket['status']] ?? $ticket['status'];
                                                ?>
                                            </span>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-0">
                                <button type="submit" id="submit_comment_btn" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> <span id="submit_btn_text">Add Comment</span>
                                </button>
                                <button type="reset" id="reset_form_btn" class="btn btn-outline-secondary ml-2">
                                    <i class="fas fa-times"></i> Clear
                                </button>
                            </div>
                        </form>
                    </div>
                    <?php endif; ?>

                    <!-- Comments Timeline -->
                    <div id="comments_timeline">
                        <?php if (!empty($ticket_history)): ?>
                            <div class="timeline-header mb-3">
                                <small class="text-muted">
                                    <i class="fas fa-history"></i>
                                    <strong><?= count($ticket_history) ?></strong> entries found
                                    <span class="ml-2">
                                        <i class="fas fa-sort-amount-down"></i> Latest first
                                    </span>
                                </small>
                            </div>
                            <?php foreach ($ticket_history as $index => $history): ?>
                                <div class="comment-item border-bottom pb-3 mb-3 position-relative">
                                    <?php if ($index < count($ticket_history) - 1): ?>
                                        <div class="timeline-connector"></div>
                                    <?php endif; ?>
                                    <div class="d-flex">
                                        <div class="comment-avatar mr-3">
                                            <?php if (!empty($history['user_photo']) && file_exists(FCPATH . 'uploads/users/' . $history['user_photo'])): ?>
                                                <img src="<?= base_url('uploads/users/'.$history['user_photo']) ?>"
                                                     alt="<?= htmlspecialchars($history['user_name']) ?>"
                                                     class="user-avatar rounded-circle shadow-sm"
                                                     style="width: 45px; height: 45px; object-fit: cover; border: 2px solid #e9ecef;"
                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                <div class="user-avatar-fallback bg-primary text-white rounded-circle d-flex align-items-center justify-content-center shadow-sm"
                                                     style="width: 45px; height: 45px; font-size: 16px; font-weight: bold; border: 2px solid #e9ecef; display: none;">
                                                    <?= strtoupper(substr($history['user_name'], 0, 1)) ?>
                                                </div>
                                            <?php else: ?>
                                                <div class="user-avatar-fallback bg-primary text-white rounded-circle d-flex align-items-center justify-content-center shadow-sm"
                                                     style="width: 45px; height: 45px; font-size: 16px; font-weight: bold; border: 2px solid #e9ecef;">
                                                    <?= strtoupper(substr($history['user_name'], 0, 1)) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="comment-content flex-grow-1">
                                            <div class="comment-header mb-2">
                                                <strong class="text-primary"><?= htmlspecialchars($history['user_name']) ?></strong>
                                                <small class="text-muted ml-2">
                                                    <i class="fas fa-clock"></i>
                                                    <?= date('d-m-Y g:i A', strtotime($history['created_at'])) ?>
                                                    <span class="ml-1 text-info">
                                                        <?php
                                                        $time_diff = time() - strtotime($history['created_at']);
                                                        if ($time_diff < 60) {
                                                            echo '(Just now)';
                                                        } elseif ($time_diff < 3600) {
                                                            echo '(' . floor($time_diff / 60) . ' min ago)';
                                                        } elseif ($time_diff < 86400) {
                                                            echo '(' . floor($time_diff / 3600) . ' hrs ago)';
                                                        } elseif ($time_diff < 604800) {
                                                            echo '(' . floor($time_diff / 86400) . ' days ago)';
                                                        }
                                                        ?>
                                                    </span>
                                                </small>
                                                <?php if (!empty($history['status'])): ?>
                                                    <span class="ml-2">
                                                        <?php
                                                        $status_colors = [
                                                            'new' => 'info',
                                                            'assigned' => 'warning',
                                                            'closed' => 'success',
                                                            'on_hold' => 'secondary',
                                                            're_open' => 'danger'
                                                        ];
                                                        $status_labels = [
                                                            'new' => 'New',
                                                            'assigned' => 'Assigned',
                                                            'closed' => 'Closed',
                                                            'on_hold' => 'On Hold',
                                                            're_open' => 'Re-opened'
                                                        ];
                                                        $color = $status_colors[$history['status']] ?? 'light';
                                                        $label = $status_labels[$history['status']] ?? $history['status'];
                                                        ?>
                                                        <span class="badge badge-<?= $color ?> badge-sm">
                                                            <i class="fas fa-tag"></i> <?= $label ?>
                                                        </span>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="comment-text">
                                                <?= nl2br(htmlspecialchars($history['remarks'])) ?>
                                            </div>
                                            <?php if (!empty($history['files'])): ?>
                                                <?php $files = json_decode($history['files'], true); ?>
                                                <?php if (!empty($files)): ?>
                                                    <div class="comment-files mt-2">
                                                        <small class="text-muted">
                                                            <i class="fas fa-paperclip"></i> Attachments:
                                                        </small>
                                                        <?php foreach ($files as $file): ?>
                                                            <a href="<?= base_url('uploads/tickets/'.$file) ?>"
                                                               target="_blank" class="badge badge-info ml-1">
                                                                <?= htmlspecialchars($file) ?>
                                                            </a>
                                                        <?php endforeach; ?>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center text-muted py-5">
                                <div class="mb-3">
                                    <i class="fas fa-comments fa-4x" style="opacity: 0.3;"></i>
                                </div>
                                <h5 class="text-muted">No History Yet</h5>
                                <p class="mb-0">This ticket doesn't have any comments or history entries yet.<br>
                                   Be the first to add a comment!</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge-lg {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.comment-item:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

.comment-avatar {
    flex-shrink: 0;
}

.user-avatar {
    transition: transform 0.2s ease;
}

.user-avatar:hover {
    transform: scale(1.05);
}

.user-avatar-fallback {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    transition: transform 0.2s ease;
}

.user-avatar-fallback:hover {
    transform: scale(1.05);
}

.comment-content {
    min-height: 40px;
}

.comment-text {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-radius: 10px;
    border-left: 3px solid #007bff;
}

#add_comment_form {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

#new_status:disabled {
    background-color: #e9ecef;
    opacity: 0.6;
}

.custom-control-label {
    cursor: pointer;
}

.status-update-section {
    background-color: rgba(0, 123, 255, 0.05);
    border: 1px dashed #007bff;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
}

#update_status_checkbox:checked + .custom-control-label {
    color: #007bff;
    font-weight: bold;
}

.badge-sm {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
}

.comment-header .badge {
    vertical-align: middle;
}

.comment-header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}

.timeline-connector {
    position: absolute;
    left: 22px;
    top: 55px;
    bottom: -15px;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #e9ecef);
    z-index: 1;
}

.timeline-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 20px !important;
}

.comment-item:last-child .timeline-connector {
    display: none;
}
</style>

<script>
$(document).ready(function() {
    // Handle status update checkbox
    $('#update_status_checkbox').on('change', function() {
        var isChecked = $(this).is(':checked');
        $('#new_status').prop('disabled', !isChecked);

        // Update button text based on checkbox state
        if (isChecked) {
            $('#submit_btn_text').text('Add Comment & Update Status');
            $('#submit_comment_btn').removeClass('btn-primary').addClass('btn-success');
            $('#new_status').focus();
        } else {
            $('#submit_btn_text').text('Add Comment');
            $('#submit_comment_btn').removeClass('btn-success').addClass('btn-primary');
            $('#new_status').val('<?= $ticket['status'] ?>');
        }
    });

    // Handle comment form submission
    $('#add_comment_form').on('submit', function(e) {
        e.preventDefault();

        var formData = {
            remarks: $('#comment_text').val().trim()
        };

        // Check if status update is requested
        var updateStatus = $('#update_status_checkbox').is(':checked');
        if (updateStatus) {
            var newStatus = $('#new_status').val();
            if (!newStatus) {
                alert('Please select a new status or uncheck the status update option.');
                return;
            }

            // Check if status is actually changing
            if (newStatus === '<?= $ticket['status'] ?>') {
                alert('The selected status is the same as the current status. Please select a different status or uncheck the status update option.');
                return;
            }

            formData.update_status = true;
            formData.new_status = newStatus;
        }

        if (!formData.remarks) {
            alert('Please enter a comment before submitting.');
            return;
        }

        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();

        // Update button text based on action
        var loadingText = updateStatus ?
            '<i class="fas fa-spinner fa-spin"></i> Adding Comment & Updating Status...' :
            '<i class="fas fa-spinner fa-spin"></i> Adding Comment...';

        // Disable submit button and show loading
        submitBtn.prop('disabled', true).html(loadingText);

        $.ajax({
            url: '<?= base_url("app/tickets/add_comment/".$ticket['id']) ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // Clear the form
                    $('#comment_text').val('');
                    $('#update_status_checkbox').prop('checked', false);
                    $('#new_status').prop('disabled', true).val('<?= $ticket['status'] ?>');

                    // Reload the page to show the new comment and updated status
                    location.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('An error occurred while processing your request. Please try again.');
                console.error('AJAX Error:', error);
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Handle form reset
    $('#reset_form_btn').on('click', function() {
        $('#comment_text').val('');
        $('#update_status_checkbox').prop('checked', false);
        $('#new_status').prop('disabled', true).val('<?= $ticket['status'] ?>');
        $('#submit_btn_text').text('Add Comment');
        $('#submit_comment_btn').removeClass('btn-success').addClass('btn-primary');

        // Reset textarea height
        $('#comment_text').css('height', 'auto');
    });

    // Auto-resize textarea
    $('#comment_text').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>