<?php
$item_id = $param1;
$ticket_data = $this->tickets_m->get(['id' => $item_id])->row_array();
$users = $this->users_m->get(['employee_status' => 1, 'is_employee' => 1], ['id', 'name'])->result_array();
?>

<div class="p-0">
    <!-- Alert container -->
    <div id="quick_actions_alert" style="display: none;">
        <div class="alert alert-dismissible fade show" role="alert">
            <span id="quick_actions_message"></span>
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    </div>

    <!-- Ticket Info Header -->
    <div class="bg-light rounded p-2 mb-4 border">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h5 class="mb-2 text-dark font-weight-bold">
                    <?= htmlspecialchars($ticket_data['title']) ?>
                </h5>
                <div class="d-flex gap-3">
                    <div class="text-left">
                        <small class="text-muted">Ticket ID</small>
                        <div class="font-weight-bold text-primary">TK-<?= $ticket_data['id'] ?></div>
                    </div>
                    <div>
                        <small class="text-muted d-block">Status</small>
                        <?php
                        switch($ticket_data['status']) {
                            case 'new': echo '<span class="badge badge-info">NEW</span>'; break;
                            case 'assigned': echo '<span class="badge badge-warning">ASSIGNED</span>'; break;
                            case 'closed': echo '<span class="badge badge-success">CLOSED</span>'; break;
                            case 'on_hold': echo '<span class="badge badge-secondary">ON HOLD</span>'; break;
                            case 're_open': echo '<span class="badge badge-danger">RE-OPEN</span>'; break;
                        }
                        ?>
                    </div>
                    <div>
                        <small class="text-muted d-block">Priority</small>
                        <?= get_task_priority($ticket_data['priority']) ?>
                    </div>
                    
                </div>
                
            </div>
            
        </div>
    </div>
    
    <!-- Quick Actions Grid -->
    <div class="row g-3">
        <!-- Update Actions -->
        <div class="col-md-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0 text-dark font-weight-semibold">
                        <i class="fas fa-edit text-primary me-2"></i>Update Ticket
                    </h6>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <!-- Status -->
                        <div class="col-md-6">
                            <label class="form-label text-muted small font-weight-bold">STATUS</label>
                            <select id="quick_status" class="form-control form-control-sm select2">
                                <option value="">Select Status</option>
                                <option value="new" <?= $ticket_data['status'] == 'new' ? 'selected' : '' ?>>New</option>
                                <option value="assigned" <?= $ticket_data['status'] == 'assigned' ? 'selected' : '' ?>>Assigned</option>
                                <option value="closed" <?= $ticket_data['status'] == 'closed' ? 'selected' : '' ?>>Closed</option>
                                <option value="on_hold" <?= $ticket_data['status'] == 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                                <option value="re_open" <?= $ticket_data['status'] == 're_open' ? 'selected' : '' ?>>Re-open</option>
                            </select>
                            <button onclick="updateTicketStatus()" class="btn btn-outline-primary btn-sm mt-2 w-100">
                                <i class="fas fa-sync me-1"></i>Update
                            </button>
                        </div>

                        <!-- Priority -->
                        <div class="col-md-6">
                            <label class="form-label text-muted small font-weight-bold">PRIORITY</label>
                            <select id="quick_priority" class="form-control form-control-sm select2">
                                <option value="">Select Priority</option>
                                <option value="critical" <?= $ticket_data['priority'] == 'critical' ? 'selected' : '' ?>>Critical</option>
                                <option value="high" <?= $ticket_data['priority'] == 'high' ? 'selected' : '' ?>>High</option>
                                <option value="medium" <?= $ticket_data['priority'] == 'medium' ? 'selected' : '' ?>>Medium</option>
                                <option value="low" <?= $ticket_data['priority'] == 'low' ? 'selected' : '' ?>>Low</option>
                            </select>
                            <button onclick="updateTicketPriority()" class="btn btn-outline-warning btn-sm mt-2 w-100">
                                <i class="fas fa-flag me-1"></i>Update
                            </button>
                        </div>

                        <!-- Assign User -->
                        <div class="col-md-12">
                            <label class="form-label text-muted small font-weight-bold">ASSIGNED TO</label>
                            <select id="quick_assign" class="form-control form-control-sm select2">
                                <option value="">Select User</option>
                                <?php
                                    foreach ($users as $user) {
                                        $selected = $ticket_data['user_id'] == $user['id'] ? 'selected' : '';
                                        echo "<option value='{$user['id']}' {$selected}>{$user['name']}</option>";
                                    }
                                ?>
                            </select>
                            <button onclick="assignTicketUser()" class="btn btn-outline-success btn-sm mt-2 w-100">
                                <i class="fas fa-user-check me-1"></i>Assign
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0 text-dark font-weight-semibold">
                        <i class="fas fa-bolt text-warning me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body p-3">
                    <div class="d-grid gap-2">
                        <button onclick="viewTicketDetails()" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-eye me-2"></i>View Details
                        </button>
                        <button onclick="editTicket()" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-2"></i>Edit Ticket
                        </button>
                        <?php if ($ticket_data['status'] !== 'closed'): ?>
                        <button onclick="closeTicket()" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-check-circle me-2"></i>Close Ticket
                        </button>
                        <?php endif; ?>
                        <?php if (has_permission('tickets/delete')): ?>
                        <hr class="my-2">
                        <button onclick="deleteTicket()" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash me-2"></i>Delete Ticket
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$('.select2').select2();
const ticketId = <?= $item_id ?>;

function updateTicketStatus() {
    const status = $('#quick_status').val();
    if (!status) {
        showQuickAlert('warning', 'Please select a status');
        return;
    }
    
    performQuickAction('update_status', {status: status}, 'Status updated successfully');
}

function assignTicketUser() {
    const userId = $('#quick_assign').val();
    if (!userId) {
        showQuickAlert('warning', 'Please select a user');
        return;
    }
    
    performQuickAction('assign_user', {user_id: userId}, 'User assigned successfully');
}

function updateTicketPriority() {
    const priority = $('#quick_priority').val();
    if (!priority) {
        showQuickAlert('warning', 'Please select a priority');
        return;
    }
    
    performQuickAction('update_priority', {priority: priority}, 'Priority updated successfully');
}

function closeTicket() {
    if (confirm('Are you sure you want to close this ticket?')) {
        performQuickAction('update_status', {status: 'closed'}, 'Ticket closed successfully');
    }
}

function viewTicketDetails() {
    window.open('<?= base_url("app/tickets/view/".$item_id) ?>', '_blank');
}

function editTicket() {
    $('.modal').modal('hide');
    setTimeout(function() {
        show_large_modal('<?= site_url("app/modal/popup/get/".$item_id."/?page_name=tickets/edit") ?>', 'Edit Ticket');
    }, 500);
}

function deleteTicket() {
    if (confirm('Are you sure you want to delete this ticket? This action cannot be undone.')) {
        performQuickAction('delete', {}, 'Ticket deleted successfully', function() {
            $('.modal').modal('hide');
            location.reload();
        });
    }
}

function performQuickAction(action, data, successMessage, callback) {
    // Show loading state
    showQuickAlert('info', 'Processing...');
    
    // Prepare AJAX data
    const ajaxData = {
        action: action,
        ticket_id: ticketId,
        ...data
    };
    
    $.ajax({
        url: '<?= base_url("app/tickets/quick_action") ?>',
        type: 'POST',
        data: ajaxData,
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                showQuickAlert('success', successMessage);
                if (callback) {
                    setTimeout(callback, 1500);
                } else {
                    // Reload the modal content after 1.5 seconds
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                }
            } else {
                showQuickAlert('danger', response.message || 'Action failed');
            }
        },
        error: function(xhr, status, error) {
            showQuickAlert('danger', 'An error occurred: ' + error);
        }
    });
}

function showQuickAlert(type, message) {
    $('#quick_actions_alert .alert').removeClass('alert-success alert-danger alert-warning alert-info')
                                   .addClass('alert-' + type);
    $('#quick_actions_message').text(message);
    $('#quick_actions_alert').show();
    
    // Auto-hide after 3 seconds for success/info messages
    if (type === 'success' || type === 'info') {
        setTimeout(function() {
            $('#quick_actions_alert').fadeOut();
        }, 3000);
    }
}

// Initialize on document ready
$(document).ready(function() {
    // Auto-hide alerts
    $(document).on('click', '#quick_actions_alert .close', function() {
        $('#quick_actions_alert').hide();
    });
});
</script>

<style>
/* Professional Quick Actions Styling */
.card {
    border-radius: 8px;
}

.card-header {
    border-radius: 8px 8px 0 0 !important;
    padding: 12px 16px;
}

.card-body {
    border-radius: 0 0 8px 8px;
}

.form-label {
    font-size: 11px;
    letter-spacing: 0.5px;
    margin-bottom: 6px;
}

.form-control-sm {
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    padding: 6px 10px;
    font-size: 13px;
}

.form-control-sm:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    font-weight: 500;
}

.btn-outline-primary:hover,
.btn-outline-warning:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.gap-3 > * {
    margin-right: 1rem !important;
}

.gap-3 > *:last-child {
    margin-right: 0 !important;
}

.d-grid.gap-2 > * {
    margin-bottom: 8px;
}

.d-grid.gap-2 > *:last-child {
    margin-bottom: 0;
}

#quick_actions_alert {
    margin-bottom: 20px;
}

.font-weight-semibold {
    font-weight: 600;
}

.text-dark {
    color: #2c3e50 !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .col-md-4 {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem !important;
    }
}
</style>