<?php
$item_id = $param1;
$edit_data = $this->tickets_m->get(['id' => $item_id])->row_array();
$projects = array_column($this->projects_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
$users = $this->users_m->get(['employee_status' => 1, 'is_employee' => 1], ['id', 'name'])->result_array();
$reported_to_users = $this->users_m->get(['employee_status' => 1], ['id', 'name'])->result_array();
?>
<form class="form-horizontal" action="<?=base_url('app/tickets/edit/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">
        <div class="form-group col-12 p-0">
            <label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="title" name="title" value="<?=$edit_data['title']?>" placeholder="Ticket Title" required>
            </div>
        </div>
        
        <div class="form-group col-6 p-0">
            <label for="project_id" class="col-sm-12 col-form-label text-muted">Project <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="project_id" name="project_id" required>
                    <option value="">Choose Project</option>
                    <?php
                        foreach ($projects as $project_id => $project) {
                            $selected = $edit_data['project_id'] == $project_id ? 'selected' : '';
                            echo "<option value='{$project_id}' {$selected}>{$project}</option>";
                        }
                    ?>
                </select>
            </div>
        </div>
        
        <div class="form-group col-6 p-0">
            <label for="type" class="col-sm-12 col-form-label text-muted">Type <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="type" name="type" required>
                    <option value="">Choose Type</option>
                    <option value="new" <?= $edit_data['type'] == 'new' ? 'selected' : '' ?>>New</option>
                    <option value="bug" <?= $edit_data['type'] == 'bug' ? 'selected' : '' ?>>Bug</option>
                    <option value="support" <?= $edit_data['type'] == 'support' ? 'selected' : '' ?>>Support</option>
                    <option value="quick_support" <?= $edit_data['type'] == 'quick_support' ? 'selected' : '' ?>>Quick Support</option>
                    <option value="queries" <?= $edit_data['type'] == 'queries' ? 'selected' : '' ?>>Queries</option>
                    <option value="critical_bug" <?= $edit_data['type'] == 'critical_bug' ? 'selected' : '' ?>>Critical Bug</option>
                </select>
            </div>
        </div>
        
        <div class="form-group col-6 p-0">
            <label for="priority" class="col-sm-12 col-form-label text-muted">Priority <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="priority" name="priority" required>
                    <option value="">Choose Priority</option>
                    <option value="critical" <?= $edit_data['priority'] == 'critical' ? 'selected' : '' ?>>Critical</option>
                    <option value="high" <?= $edit_data['priority'] == 'high' ? 'selected' : '' ?>>High</option>
                    <option value="medium" <?= $edit_data['priority'] == 'medium' ? 'selected' : '' ?>>Medium</option>
                    <option value="low" <?= $edit_data['priority'] == 'low' ? 'selected' : '' ?>>Low</option>
                </select>
            </div>
        </div>

        <div class="form-group col-6 p-0">
            <label for="estimated_time" class="col-sm-12 col-form-label text-muted">Expected Time</label>
            <div class="col-sm-12">
                <div class="input-group">
                    <?php
                    $estimated_hours = 0;
                    $estimated_minutes = 0;
                    if (!empty($edit_data['estimated_time'])) {
                        $estimated_hours = floor($edit_data['estimated_time'] / 60);
                        $estimated_minutes = $edit_data['estimated_time'] % 60;
                    }
                    ?>
                    <input type="number" class="form-control" id="estimated_hours" name="estimated_hours" min="0" max="999" placeholder="Hours" value="<?= $estimated_hours > 0 ? $estimated_hours : '' ?>">
                    <input type="number" class="form-control" id="estimated_minutes" name="estimated_minutes" min="0" max="59" placeholder="Minutes" value="<?= $estimated_minutes > 0 ? $estimated_minutes : '' ?>">
                    <div class="input-group-append">
                        <span class="input-group-text">H:M</span>
                    </div>
                </div>
                <small class="form-text text-muted">Enter expected time to complete this ticket</small>
                <input type="hidden" id="estimated_time" name="estimated_time" value="<?= $edit_data['estimated_time'] ?? '' ?>">
            </div>
        </div>

        <div class="form-group col-6 p-0">
            <label for="status" class="col-sm-12 col-form-label text-muted">Status <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="status" name="status" required>
                    <option value="">Choose Status</option>
                    <option value="new" <?= $edit_data['status'] == 'new' ? 'selected' : '' ?>>New</option>
                    <option value="assigned" <?= $edit_data['status'] == 'assigned' ? 'selected' : '' ?>>Assigned</option>
                    <option value="closed" <?= $edit_data['status'] == 'closed' ? 'selected' : '' ?>>Closed</option>
                    <option value="on_hold" <?= $edit_data['status'] == 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                    <option value="re_open" <?= $edit_data['status'] == 're_open' ? 'selected' : '' ?>>Re-open</option>
                </select>
            </div>
        </div>
        
        <div class="form-group col-6 p-0">
            <label for="ticket_via" class="col-sm-12 col-form-label text-muted">Ticket Via</label>
            <div class="col-sm-12">
                <select class="form-control select2" id="ticket_via" name="ticket_via">
                    <option value="">Choose Method</option>
                    <option value="group" <?= $edit_data['ticket_via'] == 'group' ? 'selected' : '' ?>>Group</option>
                    <option value="message" <?= $edit_data['ticket_via'] == 'message' ? 'selected' : '' ?>>Message</option>
                    <option value="call" <?= $edit_data['ticket_via'] == 'call' ? 'selected' : '' ?>>Call</option>
                    <option value="other" <?= $edit_data['ticket_via'] == 'other' ? 'selected' : '' ?>>Other</option>
                </select>
            </div>
        </div>
        
        <div class="form-group col-6 p-0">
            <label for="reported_to" class="col-sm-12 col-form-label text-muted">Reported To</label>
            <div class="col-sm-12">
                <select class="form-control select2" id="reported_to" name="reported_to">
                    <option value="">Choose User</option>
                    <?php
                        foreach ($reported_to_users as $user) {
                            $selected = $edit_data['reported_to'] == $user['id'] ? 'selected' : '';
                            echo "<option value='{$user['id']}' {$selected}>{$user['name']}</option>";
                        }
                    ?>
                </select>
            </div>
        </div>
        
        <div class="form-group col-6 p-0">
            <label for="reported_by" class="col-sm-12 col-form-label text-muted">Reported By</label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="reported_by" name="reported_by" value="<?=$edit_data['reported_by']?>" placeholder="Contact person name/phone">
            </div>
        </div>
        
        <div class="form-group col-6 p-0">
            <label for="user_id" class="col-sm-12 col-form-label text-muted">Assign To</label>
            <div class="col-sm-12">
                <select class="form-control select2" id="user_id" name="user_id">
                    <option value="">Choose User</option>
                    <?php
                        foreach ($users as $user) {
                            $selected = $edit_data['user_id'] == $user['id'] ? 'selected' : '';
                            echo "<option value='{$user['id']}' {$selected}>{$user['name']}</option>";
                        }
                    ?>
                </select>
            </div>
        </div>
        
        <div class="form-group col-6 p-0">
            <label for="ticket_date" class="col-sm-12 col-form-label text-muted">Ticket Date</label>
            <div class="col-sm-12">
                <?php
                $ticket_date_value = '';
                if (!empty($edit_data['ticket_date']) && $edit_data['ticket_date'] !== '0000-00-00 00:00:00') {
                    $ticket_date_value = date('Y-m-d', strtotime($edit_data['ticket_date']));
                }
                ?>
                <input type="date" class="form-control" id="ticket_date" name="ticket_date" value="<?=$ticket_date_value?>">
                <small class="form-text text-muted">Leave empty to set no specific date</small>
            </div>
        </div>
        
        <div class="form-group col-12 p-0">
            <label for="description" class="col-sm-12 col-form-label text-muted">Description</label>
            <div class="col-sm-12">
                <textarea class="form-control" id="description" name="description" rows="4" placeholder="Ticket Description"><?=$edit_data['description']?></textarea>
            </div>
        </div>
    </div>
    
    <div class="col-12">
        <button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
            <small><i class="fa fa-check"></i></small> Save
        </button>
    </div>
</form>

<script type="application/javascript">
    $(document).ready(function() {
        $('.select2').select2();

        // Time conversion functionality
        function updateEstimatedTime() {
            const hours = parseInt($('#estimated_hours').val()) || 0;
            const minutes = parseInt($('#estimated_minutes').val()) || 0;
            const totalMinutes = (hours * 60) + minutes;
            $('#estimated_time').val(totalMinutes > 0 ? totalMinutes : '');
        }

        // Update estimated time when hours or minutes change
        $('#estimated_hours, #estimated_minutes').on('input change', updateEstimatedTime);

        // Initialize the estimated time on page load
        updateEstimatedTime();
    });
</script>