<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <a href="<?= base_url("app/tickets/index"); ?>" class="btn btn-info btn-mini float-right">
            <i class="fas fa-list"></i> View All Tickets
        </a>
    </div>
    
    <div class="row" style="margin:14px;">
        <!-- Status Overview -->
        <div class="col-md-6">
            <div class="card card-primary shadow-pro">
                <div class="card-header">
                    <h3 class="card-title">Ticket Status Overview</h3>
                </div>
                <div class="card-body">
                    <?php if (isset($status_count)): ?>
                        <div class="row">
                            <div class="col-6 text-center mb-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-info"><i class="fas fa-plus-circle"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">New</span>
                                        <span class="info-box-number"><?= $status_count['new_count'] ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 text-center mb-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-warning"><i class="fas fa-user-check"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Assigned</span>
                                        <span class="info-box-number"><?= $status_count['assigned_count'] ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 text-center mb-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-success"><i class="fas fa-check-circle"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Closed</span>
                                        <span class="info-box-number"><?= $status_count['closed_count'] ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 text-center mb-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-secondary"><i class="fas fa-pause-circle"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">On Hold</span>
                                        <span class="info-box-number"><?= $status_count['on_hold_count'] ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Priority Overview -->
        <div class="col-md-6">
            <div class="card card-warning shadow-pro">
                <div class="card-header">
                    <h3 class="card-title">Priority Overview</h3>
                </div>
                <div class="card-body">
                    <?php if (isset($priority_count)): ?>
                        <div class="row">
                            <div class="col-4 text-center mb-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-danger"><i class="fas fa-exclamation-triangle"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">High</span>
                                        <span class="info-box-number"><?= $priority_count['high_count'] ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4 text-center mb-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-warning"><i class="fas fa-exclamation-circle"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Medium</span>
                                        <span class="info-box-number"><?= $priority_count['medium_count'] ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4 text-center mb-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-info"><i class="fas fa-info-circle"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Low</span>
                                        <span class="info-box-number"><?= $priority_count['low_count'] ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Recent Tickets -->
        <div class="col-12">
            <div class="card card-success shadow-pro">
                <div class="card-header">
                    <h3 class="card-title">Recent Tickets</h3>
                </div>
                <div class="card-body">
                    <?php if (isset($recent_tickets) && !empty($recent_tickets)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Project</th>
                                        <th>Status</th>
                                        <th>Priority</th>
                                        <th>Created</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_tickets as $ticket): ?>
                                        <tr>
                                            <td><?= get_formatted_ticket_id($ticket['id']) ?></td>
                                            <td><?= substr($ticket['title'], 0, 50) ?>...</td>
                                            <td><?= $ticket['project_title'] ?? 'N/A' ?></td>
                                            <td><?= get_ticket_status($ticket['status']) ?></td>
                                            <td><?= get_task_priority($ticket['priority']) ?></td>
                                            <td><?= date('d-m-Y', strtotime($ticket['created_on'])) ?></td>
                                            <td>
                                                <a href="<?= base_url('app/tickets/view/'.$ticket['id']) ?>" 
                                                   class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <h5>No recent tickets found</h5>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>