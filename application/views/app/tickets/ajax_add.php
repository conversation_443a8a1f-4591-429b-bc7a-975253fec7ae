<?php

$projects = array_column($CI->projects_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
$users = $CI->users_m->get(['employee_status' => 1, 'is_employee' => 1], ['id', 'name'])->result_array();
$reported_to_users = $CI->users_m->get(['employee_status' => 1], ['id', 'name'])->result_array();
?>

<form id="ajax_ticket_form" class="form-horizontal" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">
        <!-- Alert container for AJAX messages -->
        <div class="col-12 p-0">
            <div id="ajax_alert_container" style="display: none;">
                <div id="ajax_alert" class="alert alert-dismissible fade show" role="alert">
                    <span id="ajax_message"></span>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="form-group col-12 p-0">
            <label for="title" class="col-sm-12 col-form-label text-muted">Title <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="title" name="title" value="" placeholder="Ticket Title" required>
            </div>
        </div>
        
        <div class="form-group col-4 p-0">
            <label for="project_id" class="col-sm-12 col-form-label text-muted">Project <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="project_id" name="project_id" required>
                    <option value="">Choose Project</option>
                    <?php
                        foreach ($projects as $project_id => $project) {
                            echo "<option value='{$project_id}'>{$project}</option>";
                        }
                    ?>
                </select>
            </div>
        </div>

        <div class="form-group col-4 p-0">
            <label for="type" class="col-sm-12 col-form-label text-muted">Type <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="type" name="type" required>
                    <option value="">Choose Type</option>
                    <option value="new">New</option>
                    <option value="bug">Bug</option>
                    <option value="support">Support</option>
                    <option value="quick_support">Quick Support</option>
                    <option value="queries">Queries</option>
                    <option value="critical_bug">Critical Bug</option>
                </select>
            </div>
        </div>

        <div class="form-group col-4 p-0">
            <label for="priority" class="col-sm-12 col-form-label text-muted">Priority <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="priority" name="priority" required>
                    <option value="">Choose Priority</option>
                    <option value="critical">Critical</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                </select>
            </div>
        </div>

        <div class="form-group col-4 p-0">
            <label for="estimated_time" class="col-sm-12 col-form-label text-muted">Expected Time</label>
            <div class="col-sm-12">
                <div class="input-group">
                    <input type="number" class="form-control" id="estimated_hours" name="estimated_hours" min="0" max="999" placeholder="Hours">
                    <input type="number" class="form-control" id="estimated_minutes" name="estimated_minutes" min="0" max="59" placeholder="Minutes">
                    <div class="input-group-append">
                        <span class="input-group-text">H:M</span>
                    </div>
                </div>
                <small class="form-text text-muted">Enter expected time to complete this ticket</small>
                <input type="hidden" id="estimated_time" name="estimated_time" value="">
            </div>
        </div>

        <div class="form-group col-4 p-0">
            <label for="ticket_via" class="col-sm-12 col-form-label text-muted">Ticket Via</label>
            <div class="col-sm-12">
                <select class="form-control select2" id="ticket_via" name="ticket_via">
                    <option value="">Choose Method</option>
                    <option value="group">Group</option>
                    <option value="message">Message</option>
                    <option value="call">Call</option>
                    <option value="other">Other</option>
                </select>
            </div>
        </div>
        
        <div class="form-group col-4 p-0">
            <label for="reported_to" class="col-sm-12 col-form-label text-muted">Reported To</label>
            <div class="col-sm-12">
                <select class="form-control select2" id="reported_to" name="reported_to">
                    <option value="">Choose User</option>
                    <?php
                        $current_user_id = get_user_id();
                        foreach ($reported_to_users as $user) {
                            $selected = ($user['id'] == $current_user_id) ? 'selected' : '';
                            echo "<option value='{$user['id']}' {$selected}>{$user['name']}</option>";
                        }
                    ?>
                </select>
            </div>
        </div>

        <div class="form-group col-4 p-0">
            <label for="reported_by" class="col-sm-12 col-form-label text-muted">Reported By</label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="reported_by" name="reported_by" value="" placeholder="Contact person name/phone">
            </div>
        </div>
        


        
        <div class="form-group col-12 p-0">
            <label for="description" class="col-sm-12 col-form-label text-muted">Description</label>
            <div class="col-sm-12">
                <textarea class="form-control" id="description" name="description" rows="4" placeholder="Ticket Description"></textarea>
            </div>
        </div>
    </div>

    <div class="col-12">
        <button type="button" id="ajax_submit_btn" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
            <span id="btn_text"><small><i class="fa fa-check"></i></small> Save</span>
            <span id="btn_loading" style="display: none;"><small><i class="fa fa-spinner fa-spin"></i></small> Saving...</span>
        </button>
    </div>
</form>

<script type="application/javascript">
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#ajax_ticket_form').closest('.modal-content')
    });

    // Time conversion functionality
    function updateEstimatedTime() {
        const hours = parseInt($('#estimated_hours').val()) || 0;
        const minutes = parseInt($('#estimated_minutes').val()) || 0;
        const totalMinutes = (hours * 60) + minutes;
        $('#estimated_time').val(totalMinutes > 0 ? totalMinutes : '');
    }

    // Update estimated time when hours or minutes change
    $('#estimated_hours, #estimated_minutes').on('input change', updateEstimatedTime);

    // Form submission handler
    $('#ajax_submit_btn').click(function(e) {
        e.preventDefault();
        submitTicketForm();
    });
    
    // Form validation on submit
    $('#ajax_ticket_form').on('submit', function(e) {
        e.preventDefault();
        submitTicketForm();
    });
    
    function submitTicketForm() {
        // Show loading state
        $('#btn_text').hide();
        $('#btn_loading').show();
        $('#ajax_submit_btn').prop('disabled', true);
        
        // Hide previous alerts
        $('#ajax_alert_container').hide();
        
        // Get form data
        var formData = new FormData();
        
        // Append all form fields
        $('#ajax_ticket_form').find('input, select, textarea').each(function() {
            var field = $(this);
            var name = field.attr('name');
            var value = field.val();
            
            if (name && value !== undefined) {
                formData.append(name, value);
            }
        });
        
        // AJAX request
        $.ajax({
            url: '<?= base_url("app/tickets/ajax_add") ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                // Reset loading state
                $('#btn_text').show();
                $('#btn_loading').hide();
                $('#ajax_submit_btn').prop('disabled', false);
                
                if (response.status === 'success') {
                    // Show success message
                    showAjaxAlert('success', response.message);
                    
                    // Reset form
                    $('#ajax_ticket_form')[0].reset();
                    $('.select2').val('').trigger('change');
                    
                    // Reload page after short delay to show updated list
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                    
                    // Optionally close modal after success
                    setTimeout(function() {
                        $('.modal').modal('hide');
                    }, 2000);
                    
                } else {
                    // Show error message
                    showAjaxAlert('danger', response.message);
                }
            },
            error: function(xhr, status, error) {
                // Reset loading state
                $('#btn_text').show();
                $('#btn_loading').hide();
                $('#ajax_submit_btn').prop('disabled', false);
                
                // Show error message
                var errorMessage = 'An error occurred while saving the ticket.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showAjaxAlert('danger', errorMessage);
                
                console.error('AJAX Error:', error);
            }
        });
    }
    
    function showAjaxAlert(type, message) {
        $('#ajax_alert').removeClass('alert-success alert-danger alert-warning alert-info')
                        .addClass('alert-' + type);
        $('#ajax_message').text(message);
        $('#ajax_alert_container').show();
    }
    
    // Auto-hide alerts after 5 seconds
    $(document).on('shown.bs.alert', '#ajax_alert', function() {
        setTimeout(function() {
            $('#ajax_alert_container').fadeOut();
        }, 5000);
    });
});
</script>

<style>
.select2-container {
    width: 100% !important;
}

.select2-container--default .select2-selection--single {
    height: 38px;
    line-height: 36px;
}

#ajax_alert_container {
    margin-bottom: 15px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>