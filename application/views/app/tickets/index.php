<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <?php
        if (has_permission('tickets/add')){
            ?>
            <!-- AJAX Add Button -->
            <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tickets/ajax_add', 'Add New Ticket (AJAX)')"
                    class="btn btn-success btn-mini float-right btn-round mr-2">
                <small><i class="fas fa-plus"></i></small> Quick Add
            </button>
            
            <!-- Regular Add Button -->
            <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tickets/add', 'Add New Ticket')"
                    class="btn btn-primary btn-mini float-right btn-round">
                <small><i class="fas fa-plus"></i></small> Add Ticket
            </button>
            <?php
        }
        ?>
    </div>
    
    <div class="mt-3" style="margin:14px;">
        <!-- Simple Attractive Overview -->
        <div class="p-2">
            <!-- Optimized All Time Summary -->
            <div class="row mb-3">
                <div class="col-12">
                    <?php
                    $all_time_total = ($overview['all_time']['total'] ?? 0);
                    $all_time_closed = ($overview['all_time']['closed'] ?? 0);
                    $all_time_progress = $all_time_total > 0 ? round(($all_time_closed / $all_time_total) * 100) : 0;
                    ?>
                    <div class="bg-white rounded shadow-sm py-3 px-4 border-left-primary">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <h6 class="text-primary mb-0 font-weight-bold">All Time</h6>
                                <small class="text-muted">Overview</small>
                            </div>
                            <div class="col-md-7">
                                <div class="row text-center">
                                    <div class="col-3">
                                        <h5 class="mb-0 text-dark font-weight-bold"><?= number_format($overview['all_time']['total'] ?? 0) ?></h5>
                                        <small class="text-muted">Total</small>
                                    </div>
                                    <div class="col-3">
                                        <h5 class="mb-0 text-success font-weight-bold"><?= number_format($overview['all_time']['closed'] ?? 0) ?></h5>
                                        <small class="text-muted">Closed</small>
                                    </div>
                                    <div class="col-3">
                                        <h5 class="mb-0 text-warning font-weight-bold"><?= number_format($overview['all_time']['assigned'] ?? 0) ?></h5>
                                        <small class="text-muted">Assigned</small>
                                    </div>
                                    <div class="col-3">
                                        <h5 class="mb-0 text-danger font-weight-bold"><?= number_format($overview['all_time']['not_assigned'] ?? 0) ?></h5>
                                        <small class="text-muted">Pending</small>
                                    </div>
                                </div>
                                <?php if (isset($overview['all_time']['avg_resolution_time']) && $overview['all_time']['avg_resolution_time'] !== 'N/A'): ?>
                                <div class="row text-center mt-3 pt-2 border-top">
                                    <div class="col-12">
                                        <div class="avg-resolution-badge-large bg-light-secondary">
                                            <i class="fas fa-chart-line text-secondary"></i>
                                            <span class="resolution-label-large">Overall Avg Resolution</span>
                                            <span class="resolution-time-large"><?= $overview['all_time']['avg_resolution_time'] ?></span>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="d-flex align-items-center justify-content-center">
                                    <div class="position-relative mr-3">
                                        <svg width="60" height="60" class="circular-progress-small">
                                            <circle cx="30" cy="30" r="25" stroke="#e9ecef" stroke-width="4" fill="none"></circle>
                                            <circle cx="30" cy="30" r="25" stroke="#28a745" stroke-width="4" fill="none"
                                                    stroke-dasharray="<?= 2 * 3.14159 * 25 ?>"
                                                    stroke-dashoffset="<?= 2 * 3.14159 * 25 * (1 - $all_time_progress / 100) ?>"
                                                    stroke-linecap="round"
                                                    transform="rotate(-90 30 30)">
                                            </circle>
                                        </svg>
                                        <div class="position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                            <small class="font-weight-bold text-success"><?= $all_time_progress ?>%</small>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-success font-weight-bold"><?= $all_time_progress ?>%</div>
                                        <small class="text-muted">Complete</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Time Period Cards -->
            <div class="row">
                <!-- Today -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="bg-white rounded shadow-sm p-3 h-100">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-primary mb-0 font-weight-bold">Today</h6>
                            <span class="badge badge-primary font-weight-bold"><?= $overview['today']['created'] ?? 0 ?></span>
                        </div>
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="text-success">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['today']['closed'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Closed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-warning">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['today']['assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Assigned</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-danger">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['today']['not_assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Pending</small>
                                </div>
                            </div>
                        </div>
                        <?php
                        $today_total = ($overview['today']['created'] ?? 0);
                        $today_closed = ($overview['today']['closed'] ?? 0);
                        $today_progress = $today_total > 0 ? round(($today_closed / $today_total) * 100) : 0;
                        ?>
                        <div class="progress" style="height: 8px; border-radius: 10px;">
                            <div class="progress-bar bg-success"
                                 style="width: <?= $today_progress ?>%; border-radius: 10px;"
                                 title="<?= $today_progress ?>% Complete">
                            </div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted font-weight-bold"><?= $today_progress ?>% Complete</small>
                        </div>
                        <?php if (isset($overview['today']['avg_resolution_time']) && $overview['today']['avg_resolution_time'] !== 'N/A'): ?>
                        <div class="text-center mt-3 pt-2 border-top">
                            <div class="avg-resolution-badge bg-light-primary">
                                <i class="fas fa-stopwatch text-primary"></i>
                                <span class="resolution-label">Avg Resolution</span>
                                <span class="resolution-time"><?= $overview['today']['avg_resolution_time'] ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Yesterday -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="bg-white rounded shadow-sm p-3 h-100">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-info mb-0 font-weight-bold">Yesterday</h6>
                            <span class="badge badge-info font-weight-bold"><?= $overview['yesterday']['created'] ?? 0 ?></span>
                        </div>
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="text-success">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['yesterday']['closed'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Closed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-warning">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['yesterday']['assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Assigned</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-danger">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['yesterday']['not_assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Pending</small>
                                </div>
                            </div>
                        </div>
                        <?php
                        $yesterday_total = ($overview['yesterday']['created'] ?? 0);
                        $yesterday_closed = ($overview['yesterday']['closed'] ?? 0);
                        $yesterday_progress = $yesterday_total > 0 ? round(($yesterday_closed / $yesterday_total) * 100) : 0;
                        ?>
                        <div class="progress" style="height: 8px; border-radius: 10px;">
                            <div class="progress-bar bg-success"
                                 style="width: <?= $yesterday_progress ?>%; border-radius: 10px;"
                                 title="<?= $yesterday_progress ?>% Complete">
                            </div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted font-weight-bold"><?= $yesterday_progress ?>% Complete</small>
                        </div>
                        <?php if (isset($overview['yesterday']['avg_resolution_time']) && $overview['yesterday']['avg_resolution_time'] !== 'N/A'): ?>
                        <div class="text-center mt-3 pt-2 border-top">
                            <div class="avg-resolution-badge bg-light-info">
                                <i class="fas fa-stopwatch text-info"></i>
                                <span class="resolution-label">Avg Resolution</span>
                                <span class="resolution-time"><?= $overview['yesterday']['avg_resolution_time'] ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Last 7 Days -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="bg-white rounded shadow-sm p-3 h-100">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-warning mb-0 font-weight-bold">Last 7 Days</h6>
                            <span class="badge badge-warning font-weight-bold"><?= $overview['last_7_days']['created'] ?? 0 ?></span>
                        </div>
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="text-success">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['last_7_days']['closed'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Closed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-warning">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['last_7_days']['assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Assigned</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-danger">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['last_7_days']['not_assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Pending</small>
                                </div>
                            </div>
                        </div>
                        <?php
                        $week_total = ($overview['last_7_days']['created'] ?? 0);
                        $week_closed = ($overview['last_7_days']['closed'] ?? 0);
                        $week_progress = $week_total > 0 ? round(($week_closed / $week_total) * 100) : 0;
                        ?>
                        <div class="progress" style="height: 8px; border-radius: 10px;">
                            <div class="progress-bar bg-success"
                                 style="width: <?= $week_progress ?>%; border-radius: 10px;"
                                 title="<?= $week_progress ?>% Complete">
                            </div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted font-weight-bold"><?= $week_progress ?>% Complete</small>
                        </div>
                        <?php if (isset($overview['last_7_days']['avg_resolution_time']) && $overview['last_7_days']['avg_resolution_time'] !== 'N/A'): ?>
                        <div class="text-center mt-3 pt-2 border-top">
                            <div class="avg-resolution-badge bg-light-warning">
                                <i class="fas fa-stopwatch text-warning"></i>
                                <span class="resolution-label">Avg Resolution</span>
                                <span class="resolution-time"><?= $overview['last_7_days']['avg_resolution_time'] ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Last 30 Days -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="bg-white rounded shadow-sm p-3 h-100">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-success mb-0 font-weight-bold">Last 30 Days</h6>
                            <span class="badge badge-success font-weight-bold"><?= $overview['last_30_days']['created'] ?? 0 ?></span>
                        </div>
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="text-success">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['last_30_days']['closed'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Closed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-warning">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['last_30_days']['assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Assigned</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-danger">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['last_30_days']['not_assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Pending</small>
                                </div>
                            </div>
                        </div>
                        <?php
                        $month_total = ($overview['last_30_days']['created'] ?? 0);
                        $month_closed = ($overview['last_30_days']['closed'] ?? 0);
                        $month_progress = $month_total > 0 ? round(($month_closed / $month_total) * 100) : 0;
                        ?>
                        <div class="progress" style="height: 8px; border-radius: 10px;">
                            <div class="progress-bar bg-success"
                                 style="width: <?= $month_progress ?>%; border-radius: 10px;"
                                 title="<?= $month_progress ?>% Complete">
                            </div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted font-weight-bold"><?= $month_progress ?>% Complete</small>
                        </div>
                        <?php if (isset($overview['last_30_days']['avg_resolution_time']) && $overview['last_30_days']['avg_resolution_time'] !== 'N/A'): ?>
                        <div class="text-center mt-3 pt-2 border-top">
                            <div class="avg-resolution-badge bg-light-success">
                                <i class="fas fa-stopwatch text-success"></i>
                                <span class="resolution-label">Avg Resolution</span>
                                <span class="resolution-time"><?= $overview['last_30_days']['avg_resolution_time'] ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
                
                <!-- Search & Filter Section -->
                <div class="p-2 m-0 pb-2 pt-0 col-12">
                    <div class="card card-light">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-search"></i> Search & Filter
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-9 mb-3">
                                    <label for="ta_search_list">Search Tickets</label>
                                    <div class="input-group input-group-lg">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">
                                                <i class="fas fa-search"></i>
                                            </span>
                                        </div>
                                        <input type="text" class="form-control form-control-lg" id="ta_search_list"
                                               placeholder="Search tickets by ID (TKT123), title, description, project, or assigned user..."
                                               style="font-size:16px; height: 48px;">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary btn-lg" type="button" id="clear_search_original">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label>&nbsp;</label> <!-- Empty label for alignment -->
                                    <div class="d-flex justify-content-end">
                                        <?php if (has_permission('tickets/add')): ?>
                                        <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/') ?>/?page_name=tickets/ajax_add', 'Quick Add Ticket')"
                                                class="btn btn-primary btn-lg mr-2">
                                            <i class="fas fa-plus"></i> Add Ticket
                                        </button>
                                        <?php endif; ?>
                                        <?php if (has_permission('tasks/add')): ?>
                                        <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/') ?>/?page_name=tasks/ajax_add', 'Quick Add Task')"
                                                class="btn btn-success btn-lg">
                                            <i class="fas fa-tasks"></i> Add Task
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="button" onclick="location.reload()" class="btn btn-secondary">
                                        <i class="fas fa-sync-alt"></i> Refresh
                                    </button>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        Total: <strong><?= count($list_items ?? []) ?></strong> tickets
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tickets Table -->
        <div class="card card-primary row mt-3 shadow-pro" style="margin:14px;">
            <div class="card-header">
                <h3 class="card-title"><?= strtoupper($page_title ?? '')?></h3>
            </div>
            <div class="card-body">
                <table id="example1" class="table table-bordered table-striped">
                    <thead>
                    <tr>
                        <th>Ticket ID</th>
                        <th>Title</th>
                        <th>Type</th>
                        <th>Priority</th>
                        <th>Status</th>
                        <th>Assigned To</th>
                        <th>Reported By</th>
                        <th style="width: 120px;">Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    if (isset($list_items) && isset($users) && isset($projects)) {
                        foreach ($list_items as $key => $item) {
                            ?>
                            <tr class="ta_search_item">
                                <td>
                                    <div class="mb-1 job_id_dashboard">TK-<b><?= $item['id'] ?></b></div>
                                </td>
                                <td>
                                    <?php
                                    $project_title = $item['project_title'] ?? 'N/A';
                                    if ($project_title !== 'N/A'): ?>
                                        <span style="background-color:#efefef;font-size: 13px;border-left: 2px solid #999;color: blueviolet;padding-left: 5px">
                                            <?= strtoupper(htmlspecialchars($project_title)) ?>
                                        </span><br>
                                    <?php endif; ?>
                                    <div class="p-2 text-muted" style="font-size: 16px!important; font-weight: bold; background-color: rgba(250,247,250,0.81); border-radius: 2px;">
                                        <?= htmlspecialchars($item['title']) ?>
                                        <div class="d-inline-block float-right" style="font-size: 12px; color: #666;">
                                            <i class="fas fa-calendar"></i> <?= (!empty($item['ticket_date']) && $item['ticket_date'] !== '0000-00-00 00:00:00') ? date('d-m-Y', strtotime($item['ticket_date'])) : 'N/A' ?>
                                        </div>
                                    </div>
                                </td>
                                <td><?= get_task_type($item['type'])?></td>
                                <td><?= get_task_priority($item['priority'])?></td>
                                <td>
                                    <?php
                                    switch($item['status']) {
                                        case 'new':
                                            echo '<span class="badge badge-info">NEW</span>';
                                            break;
                                        case 'assigned':
                                            echo '<span class="badge badge-warning">ASSIGNED</span>';
                                            break;
                                        case 'closed':
                                            echo '<span class="badge badge-success">CLOSED</span>';
                                            break;
                                        case 'on_hold':
                                            echo '<span class="badge badge-secondary">ON HOLD</span>';
                                            break;
                                        case 're_open':
                                            echo '<span class="badge badge-danger">RE-OPEN</span>';
                                            break;
                                        default:
                                            echo '<span class="badge badge-light">UNKNOWN</span>';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    $assigned_user = $item['assigned_user_name'] ?? null;
                                    if (!empty($assigned_user)): ?>
                                        <button class="btn_assigned_user">
                                            <small><i class="bi bi-person-circle"></i></small> <?= strtoupper(htmlspecialchars($assigned_user)) ?>
                                        </button>
                                    <?php else: ?>
                                        <span style="color: #999; font-style: italic;">Unassigned</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= $item['reported_by'] ?? 'N/A'?></td>
                                <td>
                                    <a href="<?= base_url('app/tickets/view/'.$item['id']); ?>"
                                       class="btn btn-success btn-sm" title="View Details">
                                        <small><i class="fas fa-eye"></i></small>
                                    </a>

                                    <?php if (empty($item['user_id']) || $item['user_id'] == 0): ?>
                                        <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tickets/quick_actions'); ?>', 'Assign Developer')"
                                                class="btn btn-info btn-sm" title="Assign Developer">
                                            <small><i class="fas fa-user-plus"></i></small>
                                        </button>
                                    <?php endif; ?>

                                    <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tickets/quick_actions'); ?>', 'Quick Actions')"
                                            class="btn btn-warning btn-sm" title="Quick Actions">
                                        <small><i class="fas fa-bolt"></i></small>
                                    </button>

                                    <?php if (has_permission('tickets/edit')): ?>
                                        <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tickets/edit'); ?>', 'Edit Ticket')"
                                                class="btn btn-primary btn-sm" title="Edit">
                                            <small><i class="fas fa-pencil-alt"></i></small>
                                        </button>
                                    <?php endif; ?>

                                    <?php if (has_permission('tickets/delete')): ?>
                                        <button onclick="confirm_modal('<?=base_url("app/tickets/delete/{$item['id']}/");?>')"
                                                class="btn btn-outline-danger btn-sm" title="Delete">
                                            <small><i class="fas fa-trash"></i></small>
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php
                        }
                    }
                    ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div id="ta_no_result" class="text-center p-2" style="display: none">
            <div class="p-4 shadow_pro bg-white mx-auto">
                <img width="96" height="96" src="https://img.icons8.com/fluency/96/nothing-found.png" alt="nothing-found"/>
                <h4 class="text-danger mt-3">No Result Found!</h4>
            </div>
        </div>
    </div>
</div>

<!-- Floating Quick Add Button -->
<?php if (has_permission('tickets/add')): ?>
<div class="floating-add-btn">
    <button id="floating_quick_add" class="btn btn-success btn-lg rounded-circle shadow-lg" 
            title="Quick Add Ticket"
            onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tickets/ajax_add', 'Quick Add Ticket')">
        <i class="fas fa-plus fa-lg"></i>
    </button>
</div>
<?php endif; ?>

<style>
.floating-add-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    display: block !important; /* Always visible */
}

.floating-add-btn .btn {
    width: 60px;
    height: 60px;
    border-radius: 50% !important;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.floating-add-btn .btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.floating-add-btn .btn:active {
    transform: scale(0.95);
}

/* Attractive Resolution Time Badge Styling */
.avg-resolution-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 20px;
    color: #495057;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.1);
}

.avg-resolution-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.avg-resolution-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    transition: left 0.5s;
}

.avg-resolution-badge:hover::before {
    left: 100%;
}

.avg-resolution-badge i {
    font-size: 14px;
    animation: pulse 2s infinite;
}

.resolution-label {
    font-size: 10px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.resolution-time {
    font-size: 13px;
    font-weight: 700;
    margin-left: 2px;
}

/* Large badge for All Time overview */
.avg-resolution-badge-large {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 25px;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.1);
}

.avg-resolution-badge-large:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.25);
}

.avg-resolution-badge-large::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    transition: left 0.5s;
}

.avg-resolution-badge-large:hover::before {
    left: 100%;
}

.avg-resolution-badge-large i {
    font-size: 16px;
    animation: pulse 2s infinite;
}

.resolution-label-large {
    font-size: 11px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.resolution-time-large {
    font-size: 15px;
    font-weight: 700;
    margin-left: 3px;
}

/* Light gradient backgrounds */
.bg-light-primary {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    border-color: #007bff !important;
}

.bg-light-info {
    background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%) !important;
    border-color: #17a2b8 !important;
}

.bg-light-warning {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%) !important;
    border-color: #ffc107 !important;
}

.bg-light-success {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
    border-color: #28a745 !important;
}

.bg-light-secondary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-color: #6c757d !important;
}

/* Pulse animation for icons */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Hide on mobile if needed */
@media (max-width: 768px) {
    .floating-add-btn {
        bottom: 20px;
        right: 20px;
    }
    
    .floating-add-btn .btn {
        width: 50px;
        height: 50px;
    }
}

/* Animation for the floating button */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.floating-add-btn .btn {
    animation: float 3s ease-in-out infinite;
}

.floating-add-btn .btn:hover {
    animation: none;
}

/* Job ID Dashboard styling */
.job_id_dashboard {
    font-size: 14px;
    color: #007bff;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.job_id_dashboard b {
    font-weight: 700;
    color: #0056b3;
}

/* Assigned User Button styling (same as tasks) */
.btn_assigned_user {
    width: 120px !important;
    font-size: 14px;
    border-radius: 60px;
    background-color: rgb(244, 243, 243) !important;
    border: 1px solid rgb(244, 243, 243);
    color: #333;
    cursor: default;
}

.btn_assigned_user:hover {
    opacity: 0.8;
}

/* Light highlighted cards for Today and Yesterday */
.bg-light-primary {
    background-color: #e3f2fd !important; /* Very light blue */
}

.bg-light-info {
    background-color: #e0f7fa !important; /* Very light cyan */
}

.today-card-light {
    border-left: 4px solid #007bff !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.15) !important;
    transition: all 0.3s ease;
}

.yesterday-card-light {
    border-left: 4px solid #17a2b8 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.15) !important;
    transition: all 0.3s ease;
}

.today-card-light:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.25) !important;
    background-color: #bbdefb !important; /* Slightly darker on hover */
}

.yesterday-card-light:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.25) !important;
    background-color: #b2ebf2 !important; /* Slightly darker on hover */
}

/* Enhanced header styling for Today and Yesterday */
.icon-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.total-count-badge {
    text-align: center;
}

.badge-lg {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

.today-header, .yesterday-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 0.75rem;
    margin-bottom: 1rem !important;
}

/* Enhanced border styling */
.border-primary {
    border-color: #007bff !important;
}

.border-info {
    border-color: #17a2b8 !important;
}
</style>

<script>
$(document).ready(function() {
    // Initialize floating button tooltip
    $('#floating_quick_add').tooltip();
    
    // Enhanced search functionality
    $('#ta_search_list').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        var hasResults = false;

        $('.ta_search_item').each(function() {
            var text = $(this).text().toLowerCase();
            if (text.indexOf(value) > -1) {
                $(this).show();
                hasResults = true;
            } else {
                $(this).hide();
            }
        });

        if (hasResults) {
            $('#ta_no_result').hide();
        } else {
            $('#ta_no_result').show();
        }
    });

    // Clear search functionality for original view
    $('#clear_search_original').on('click', function() {
        $('#ta_search_list').val('');
        $('.ta_search_item').show();
        $('#ta_no_result').hide();
    });


    
    // Show floating button always (remove scroll-based hiding)
    $('.floating-add-btn').show();
});

// AJAX modal function for quick actions
function show_ajax_modal(url, title) {
    $.ajax({
        url: url,
        type: 'GET',
        success: function(response) {
            $('#ajax_modal_content').html(response);
            $('#ajax_modal_title').text(title);
            $('#ajax_modal').modal('show');
        },
        error: function() {
            alert('Error loading content');
        }
    });
}
</script>