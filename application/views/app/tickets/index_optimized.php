  <div class="mt-2" style="margin:14px;margin-top:5px!important;">
        <!-- Simple Attractive Overview -->
        <div class="p-2">
            <!-- Optimized All Time Summary -->
            <div class="row mb-3">
                <div class="col-12">
                    <?php
                    $all_time_total = ($overview['all_time']['total'] ?? 0);
                    $all_time_closed = ($overview['all_time']['closed'] ?? 0);
                    $all_time_progress = $all_time_total > 0 ? round(($all_time_closed / $all_time_total) * 100) : 0;
                    ?>
                    <div class="bg-white rounded shadow-sm py-3 px-4 border-left-primary">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <h6 class="text-primary mb-0 font-weight-bold">All Time</h6>
                                <small class="text-muted">Overview</small>
                            </div>
                            <div class="col-md-7">
                                <div class="row text-center">
                                    <div class="col-3">
                                        <h5 class="mb-0 text-dark font-weight-bold"><?= number_format($overview['all_time']['total'] ?? 0) ?></h5>
                                        <small class="text-muted">Total</small>
                                    </div>
                                    <div class="col-3">
                                        <h5 class="mb-0 text-success font-weight-bold"><?= number_format($overview['all_time']['closed'] ?? 0) ?></h5>
                                        <small class="text-muted">Closed</small>
                                    </div>
                                    <div class="col-3">
                                        <h5 class="mb-0 text-warning font-weight-bold"><?= number_format($overview['all_time']['assigned'] ?? 0) ?></h5>
                                        <small class="text-muted">Assigned</small>
                                    </div>
                                    <div class="col-3">
                                        <h5 class="mb-0 text-danger font-weight-bold"><?= number_format($overview['all_time']['not_assigned'] ?? 0) ?></h5>
                                        <small class="text-muted">Pending</small>
                                    </div>
                                </div>
                                <?php if (isset($overview['all_time']['avg_resolution_time']) && $overview['all_time']['avg_resolution_time'] !== 'N/A'): ?>
                                <div class="row text-center mt-3 pt-2 border-top">
                                    <div class="col-12">
                                        <div class="avg-resolution-badge-large bg-light-secondary">
                                            <i class="fas fa-chart-line text-secondary"></i>
                                            <span class="resolution-label-large">Overall Avg Resolution</span>
                                            <span class="resolution-time-large"><?= $overview['all_time']['avg_resolution_time'] ?></span>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="d-flex align-items-center justify-content-center">
                                    <div class="position-relative mr-3">
                                        <svg width="60" height="60" class="circular-progress-small">
                                            <circle cx="30" cy="30" r="25" stroke="#e9ecef" stroke-width="4" fill="none"></circle>
                                            <circle cx="30" cy="30" r="25" stroke="#28a745" stroke-width="4" fill="none"
                                                    stroke-dasharray="<?= 2 * 3.14159 * 25 ?>"
                                                    stroke-dashoffset="<?= 2 * 3.14159 * 25 * (1 - $all_time_progress / 100) ?>"
                                                    stroke-linecap="round"
                                                    transform="rotate(-90 30 30)">
                                            </circle>
                                        </svg>
                                        <div class="position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                            <small class="font-weight-bold text-success"><?= $all_time_progress ?>%</small>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-success font-weight-bold"><?= $all_time_progress ?>%</div>
                                        <small class="text-muted">Complete</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Time Period Cards -->
            <div class="row">
                <!-- Today -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="bg-white rounded shadow-sm p-3 h-100">
                        <div class="d-flex justify-content-between align-items-center mb-3 p-2 rounded" style="background-color: #f8f9ff;">
                            <h6 class="text-primary mb-0 font-weight-bold">Today</h6>
                            <span class="badge badge-primary font-weight-bold" style="font-size: 16px; padding: 8px 12px;"><?= $overview['today']['created'] ?? 0 ?></span>
                        </div>
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="text-success">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['today']['closed'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Closed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-warning">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['today']['assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Assigned</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-danger">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['today']['not_assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Pending</small>
                                </div>
                            </div>
                        </div>
                        <?php
                        $today_total = ($overview['today']['created'] ?? 0);
                        $today_closed = ($overview['today']['closed'] ?? 0);
                        $today_progress = $today_total > 0 ? round(($today_closed / $today_total) * 100) : 0;
                        ?>
                        <div class="progress" style="height: 8px; border-radius: 10px;">
                            <div class="progress-bar bg-success"
                                 style="width: <?= $today_progress ?>%; border-radius: 10px;"
                                 title="<?= $today_progress ?>% Complete">
                            </div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted font-weight-bold"><?= $today_progress ?>% Complete</small>
                        </div>
                        <?php if (isset($overview['today']['avg_resolution_time']) && $overview['today']['avg_resolution_time'] !== 'N/A'): ?>
                        <div class="text-center mt-3 pt-2 border-top">
                            <div class="avg-resolution-badge bg-light-primary">
                                <i class="fas fa-stopwatch text-primary"></i>
                                <span class="resolution-label">Avg Resolution</span>
                                <span class="resolution-time"><?= $overview['today']['avg_resolution_time'] ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Yesterday -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="bg-white rounded shadow-sm p-3 h-100">
                        <div class="d-flex justify-content-between align-items-center mb-3 p-2 rounded" style="background-color: #f0fffe;">
                            <h6 class="text-info mb-0 font-weight-bold">Yesterday</h6>
                            <span class="badge badge-info font-weight-bold" style="font-size: 16px; padding: 8px 12px;"><?= $overview['yesterday']['created'] ?? 0 ?></span>
                        </div>
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="text-success">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['yesterday']['closed'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Closed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-warning">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['yesterday']['assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Assigned</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-danger">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['yesterday']['not_assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Pending</small>
                                </div>
                            </div>
                        </div>
                        <?php
                        $yesterday_total = ($overview['yesterday']['created'] ?? 0);
                        $yesterday_closed = ($overview['yesterday']['closed'] ?? 0);
                        $yesterday_progress = $yesterday_total > 0 ? round(($yesterday_closed / $yesterday_total) * 100) : 0;
                        ?>
                        <div class="progress" style="height: 8px; border-radius: 10px;">
                            <div class="progress-bar bg-success"
                                 style="width: <?= $yesterday_progress ?>%; border-radius: 10px;"
                                 title="<?= $yesterday_progress ?>% Complete">
                            </div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted font-weight-bold"><?= $yesterday_progress ?>% Complete</small>
                        </div>
                        <?php if (isset($overview['yesterday']['avg_resolution_time']) && $overview['yesterday']['avg_resolution_time'] !== 'N/A'): ?>
                        <div class="text-center mt-3 pt-2 border-top">
                            <div class="avg-resolution-badge bg-light-info">
                                <i class="fas fa-stopwatch text-info"></i>
                                <span class="resolution-label">Avg Resolution</span>
                                <span class="resolution-time"><?= $overview['yesterday']['avg_resolution_time'] ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Last 7 Days -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="bg-white rounded shadow-sm p-3 h-100">
                        <div class="d-flex justify-content-between align-items-center mb-3 p-2 rounded" style="background-color: #fffef0;">
                            <h6 class="text-warning mb-0 font-weight-bold">Last 7 Days</h6>
                            <span class="badge badge-warning font-weight-bold" style="font-size: 16px; padding: 8px 12px;"><?= $overview['last_7_days']['created'] ?? 0 ?></span>
                        </div>
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="text-success">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['last_7_days']['closed'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Closed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-warning">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['last_7_days']['assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Assigned</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-danger">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['last_7_days']['not_assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Pending</small>
                                </div>
                            </div>
                        </div>
                        <?php
                        $week_total = ($overview['last_7_days']['created'] ?? 0);
                        $week_closed = ($overview['last_7_days']['closed'] ?? 0);
                        $week_progress = $week_total > 0 ? round(($week_closed / $week_total) * 100) : 0;
                        ?>
                        <div class="progress" style="height: 8px; border-radius: 10px;">
                            <div class="progress-bar bg-success"
                                 style="width: <?= $week_progress ?>%; border-radius: 10px;"
                                 title="<?= $week_progress ?>% Complete">
                            </div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted font-weight-bold"><?= $week_progress ?>% Complete</small>
                        </div>
                        <?php if (isset($overview['last_7_days']['avg_resolution_time']) && $overview['last_7_days']['avg_resolution_time'] !== 'N/A'): ?>
                        <div class="text-center mt-3 pt-2 border-top">
                            <div class="avg-resolution-badge bg-light-warning">
                                <i class="fas fa-stopwatch text-warning"></i>
                                <span class="resolution-label">Avg Resolution</span>
                                <span class="resolution-time"><?= $overview['last_7_days']['avg_resolution_time'] ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Last 30 Days -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="bg-white rounded shadow-sm p-3 h-100">
                        <div class="d-flex justify-content-between align-items-center mb-3 p-2 rounded" style="background-color: #f0fff8;">
                            <h6 class="text-success mb-0 font-weight-bold">Last 30 Days</h6>
                            <span class="badge badge-success font-weight-bold" style="font-size: 16px; padding: 8px 12px;"><?= $overview['last_30_days']['created'] ?? 0 ?></span>
                        </div>
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="text-success">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['last_30_days']['closed'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Closed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-warning">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['last_30_days']['assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Assigned</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-danger">
                                    <h5 class="mb-0 font-weight-bold"><?= $overview['last_30_days']['not_assigned'] ?? 0 ?></h5>
                                    <small class="font-weight-bold">Pending</small>
                                </div>
                            </div>
                        </div>
                        <?php
                        $month_total = ($overview['last_30_days']['created'] ?? 0);
                        $month_closed = ($overview['last_30_days']['closed'] ?? 0);
                        $month_progress = $month_total > 0 ? round(($month_closed / $month_total) * 100) : 0;
                        ?>
                        <div class="progress" style="height: 8px; border-radius: 10px;">
                            <div class="progress-bar bg-success"
                                 style="width: <?= $month_progress ?>%; border-radius: 10px;"
                                 title="<?= $month_progress ?>% Complete">
                            </div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted font-weight-bold"><?= $month_progress ?>% Complete</small>
                        </div>
                        <?php if (isset($overview['last_30_days']['avg_resolution_time']) && $overview['last_30_days']['avg_resolution_time'] !== 'N/A'): ?>
                        <div class="text-center mt-3 pt-2 border-top">
                            <div class="avg-resolution-badge bg-light-success">
                                <i class="fas fa-stopwatch text-success"></i>
                                <span class="resolution-label">Avg Resolution</span>
                                <span class="resolution-time"><?= $overview['last_30_days']['avg_resolution_time'] ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search & Filter Options -->
        <div class="card card-secondary mt-0 shadow-pro" style="margin:14px;">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-search"></i> Search & Filter Options
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">

                <!-- Filter Section -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filter_status">Status</label>
                            <select id="filter_status" class="form-control">
                                <option value="">All Status</option>
                                <option value="new">New</option>
                                <option value="assigned">Assigned</option>
                                <option value="closed">Closed</option>
                                <option value="on_hold">On Hold</option>
                                <option value="re_open">Re-Open</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filter_priority">Priority</label>
                            <select id="filter_priority" class="form-control">
                                <option value="">All Priorities</option>
                                <option value="critical">Critical</option>
                                <option value="high">High</option>
                                <option value="medium">Medium</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filter_project">Project</label>
                            <select id="filter_project" class="form-control select2">
                                <option value="">All Projects</option>
                                <?php if (isset($projects)): ?>
                                    <?php foreach ($projects as $project_id => $project_title): ?>
                                        <option value="<?= $project_id ?>"><?= htmlspecialchars($project_title) ?></option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filter_assigned">Assigned To</label>
                            <select id="filter_assigned" class="form-control select2">
                                <option value="">All Users</option>
                                <option value="unassigned">Unassigned</option>
                                <?php if (isset($users)): ?>
                                    <?php foreach ($users as $user_id => $user_name): ?>
                                        <option value="<?= $user_id ?>"><?= htmlspecialchars($user_name) ?></option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                </div>
                <!-- Date Filters and Action Buttons in Same Row -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filter_date_from">Date From</label>
                            <input type="date" id="filter_date_from" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filter_date_to">Date To</label>
                            <input type="date" id="filter_date_to" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>&nbsp;</label> <!-- Empty label for alignment -->
                            <div class="d-block">
                                <button type="button" id="apply_filters" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Apply Filters
                                </button>
                                <button type="button" id="clear_filters" class="btn btn-secondary ml-2">
                                    <i class="fas fa-times"></i> Clear All
                                </button>
                                <button type="button" id="refresh_table" class="btn btn-info ml-2">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Optimized Tickets Table with DataTables -->
        <div class="card card-primary row mt-3 shadow-pro" style="margin:14px;">
            <div class="card-body">

                <!-- Search Section with Add Buttons -->
                <div class="row mb-3">
                    <div class="col-md-9">
                        <div class="form-group">
                            <label for="enhanced_search" class="sr-only">Search Tickets</label>
                            <div class="input-group input-group-lg">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                </div>
                                <input type="text" id="enhanced_search" class="form-control form-control-lg"
                                       placeholder="Search by ticket ID (TKT123), title, description, project, or assigned user..."
                                       style="font-size: 16px; height: 48px;">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary btn-lg" type="button" id="clear_search">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <div class="d-flex justify-content-end">
                                <?php if (has_permission('tickets/add')): ?>
                                <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/') ?>/?page_name=tickets/add', 'Add Ticket')"
                                        class="btn btn-primary btn-lg mr-2">
                                    <i class="fas fa-plus"></i> Add Ticket
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <table id="tickets_datatable" class="table table-bordered table-striped" style="width:100%">
                    <thead class="table-light">
                    <tr>
                        <th>Ticket ID</th>
                        <th>Title</th>
                        <th>Details</th>
                        <th>Status</th>
                        <th style="width: 50px;">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    <!-- Data will be loaded via AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Floating Quick Add Button -->
<?php if (has_permission('tickets/add')): ?>
<div class="floating-add-btn">
    <button id="floating_quick_add" class="btn btn-success btn-lg rounded-circle shadow-lg" 
            title="Quick Add Ticket"
            onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tickets/ajax_add', 'Quick Add Ticket')">
        <i class="fas fa-plus fa-lg"></i>
    </button>
</div>
<?php endif; ?>

<style>
/* Optimized Design Styles */
.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.rounded-lg {
    border-radius: 0.5rem !important;
}

/* Optimized circular progress */
.circular-progress-small {
    transform: rotate(-90deg);
}

.circular-progress-small circle {
    transition: stroke-dashoffset 0.5s ease-in-out;
}

/* Compact all-time overview */
.py-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
}

/* Font weight optimization */
.font-weight-bold {
    font-weight: 600 !important;
}

/* Card hover effects */
.bg-white:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

/* Badge styling */
.badge {
    font-size: 0.9em;
    padding: 0.4em 0.8em;
}

/* Progress bar styling */
.progress {
    background-color: #f8f9fa;
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Enhanced search section styling */
.search-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Enhanced search input styling */
.input-group-lg .form-control {
    height: 48px !important;
    font-size: 16px !important;
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.input-group-lg .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: translateY(-1px);
}

.input-group-lg .input-group-text {
    height: 48px !important;
    font-size: 16px !important;
    background-color: #e9ecef;
    border-color: #ced4da;
}

.input-group-lg .btn {
    height: 48px !important;
    font-size: 16px !important;
}

/* Add button styling */
.btn-add-actions {
    min-width: 120px;
    height: 48px;
}

/* Attractive Resolution Time Badge Styling */
.avg-resolution-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 20px;
    color: #495057;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.1);
}

.avg-resolution-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.avg-resolution-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    transition: left 0.5s;
}

.avg-resolution-badge:hover::before {
    left: 100%;
}

.avg-resolution-badge i {
    font-size: 14px;
    animation: pulse 2s infinite;
}

.resolution-label {
    font-size: 10px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.resolution-time {
    font-size: 13px;
    font-weight: 700;
    margin-left: 2px;
}

/* Large badge for All Time overview */
.avg-resolution-badge-large {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 25px;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.1);
}

.avg-resolution-badge-large:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.25);
}

.avg-resolution-badge-large::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    transition: left 0.5s;
}

.avg-resolution-badge-large:hover::before {
    left: 100%;
}

.avg-resolution-badge-large i {
    font-size: 16px;
    animation: pulse 2s infinite;
}

.resolution-label-large {
    font-size: 11px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.resolution-time-large {
    font-size: 15px;
    font-weight: 700;
    margin-left: 3px;
}

/* Light gradient backgrounds */
.bg-light-primary {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    border-color: #007bff !important;
}

.bg-light-info {
    background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%) !important;
    border-color: #17a2b8 !important;
}

.bg-light-warning {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%) !important;
    border-color: #ffc107 !important;
}

.bg-light-success {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
    border-color: #28a745 !important;
}

.bg-light-secondary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-color: #6c757d !important;
}

/* Pulse animation for icons */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.btn-add-actions .fas {
    margin-right: 0.5rem;
}

/* Button alignment */
.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.375rem;
}

/* Job ID Dashboard styling */
.job_id_dashboard {
    font-size: 14px;
    color: #007bff;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.job_id_dashboard b {
    font-weight: 700;
    color: #0056b3;
}

/* Assigned User Button styling (same as tasks) */
.btn_assigned_user {
    width: 120px !important;
    font-size: 14px;
    border-radius: 60px;
    background-color: rgb(244, 243, 243) !important;
    border: 1px solid rgb(244, 243, 243);
    color: #333;
    cursor: default;
}

.btn_assigned_user:hover {
    opacity: 0.8;
}

/* Light highlighted cards for Today and Yesterday */
.bg-light-primary {
    background-color: #e3f2fd !important; /* Very light blue */
}

.bg-light-info {
    background-color: #e0f7fa !important; /* Very light cyan */
}

.today-card-light {
    border-left: 4px solid #007bff !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.15) !important;
    transition: all 0.3s ease;
}

.yesterday-card-light {
    border-left: 4px solid #17a2b8 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.15) !important;
    transition: all 0.3s ease;
}

.today-card-light:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.25) !important;
    background-color: #bbdefb !important; /* Slightly darker on hover */
}

.yesterday-card-light:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.25) !important;
    background-color: #b2ebf2 !important; /* Slightly darker on hover */
}

/* Enhanced header styling for Today and Yesterday */
.icon-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.total-count-badge {
    text-align: center;
}

.badge-lg {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

.today-header, .yesterday-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 0.75rem;
    margin-bottom: 1rem !important;
}

/* Enhanced border styling */
.border-primary {
    border-color: #007bff !important;
}

.border-info {
    border-color: #17a2b8 !important;
}

/* Floating button styles - Always visible */
.floating-add-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    display: block !important; /* Always visible */
}

.floating-add-btn .btn {
    width: 60px;
    height: 60px;
    border-radius: 50% !important;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.floating-add-btn .btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.floating-add-btn .btn:active {
    transform: scale(0.95);
}

/* Hide on mobile if needed */
@media (max-width: 768px) {
    .floating-add-btn {
        bottom: 20px;
        right: 20px;
    }

    .floating-add-btn .btn {
        width: 50px;
        height: 50px;
    }
}

/* Animation for the floating button */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.floating-add-btn .btn {
    animation: float 3s ease-in-out infinite;
}

.floating-add-btn .btn:hover {
    animation: none;
}

/* DataTables performance optimizations */
.dataTables_wrapper .dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 1em 0;
    background: rgba(255,255,255,0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    /* Stack all-time overview on mobile */
    .col-md-2, .col-md-7, .col-md-3 {
        margin-bottom: 1rem;
    }

    /* Smaller circular progress on mobile */
    .circular-progress-small {
        width: 50px;
        height: 50px;
    }

    .circular-progress-small circle {
        r: 20;
        cx: 25;
        cy: 25;
    }

    /* Adjust font sizes on mobile */
    h5 {
        font-size: 1.1rem;
    }

    h6 {
        font-size: 1rem;
    }

    /* Search section mobile adjustments */
    .col-md-9, .col-md-3 {
        margin-bottom: 1rem;
    }

    /* Stack buttons on mobile */
    .d-flex {
        flex-direction: column;
    }

    .btn-lg {
        margin-bottom: 0.5rem;
        margin-right: 0 !important;
    }

    /* Adjust search input on mobile */
    .input-group-lg .form-control {
        font-size: 16px !important; /* Prevent zoom on iOS */
    }
}

/* Subtle Table Improvements */
.table-light th {
    font-weight: 500;
    font-size: 13px;
    padding: 12px 10px;
}

.table-light th i {
    opacity: 0.7;
}

/* Subtle row hover effect */
#tickets_datatable tbody tr:hover {
    background-color: #f8f9fa;
}

/* Improved spacing for table cells */
#tickets_datatable td {
    padding: 10px;
    vertical-align: middle;
}

/* Better badge styling */
.badge {
    font-size: 11px;
    font-weight: 500;
}

/* Overdue indicator */
.overdue-ticket {
    border-left: 3px solid #dc3545;
    background-color: #fff5f5;
}
</style>

<script>
$(document).ready(function() {
    // Initialize optimized DataTable for large datasets
    var table = $('#tickets_datatable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?= site_url('app/tickets/ajax_list') ?>",
            "type": "POST",
            "data": function(d) {
                // Add filter parameters
                d.filter_status = $('#filter_status').val();
                d.filter_priority = $('#filter_priority').val();
                d.filter_project = $('#filter_project').val();
                d.filter_assigned = $('#filter_assigned').val();
                d.filter_date_from = $('#filter_date_from').val();
                d.filter_date_to = $('#filter_date_to').val();
            }
        },
        "columns": [
            { "data": 0, "orderable": true, "searchable": true, "width": "15%" }, // Ticket ID
            { "data": 1, "width": "45%" }, // Title (with Project, Date, and Reported By)
            { "data": 2, "width": "20%" }, // Details (Type, Priority, Assigned To)
            { "data": 3, "width": "15%" }, // Status
            { "data": 4, "orderable": false, "searchable": false, "width": "5%" } // Actions
        ],
        "order": [[ 0, "desc" ]], // Order by Ticket ID descending (latest first)
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        "language": {
            "processing": "Loading tickets...",
            "emptyTable": "No tickets found",
            "info": "Showing _START_ to _END_ of _TOTAL_ tickets",
            "infoEmpty": "Showing 0 to 0 of 0 tickets",
            "infoFiltered": "(filtered from _MAX_ total tickets)",
            "search": "",
            "paginate": {
                "first": "First",
                "last": "Last",
                "next": "Next",
                "previous": "Previous"
            }
        },
        "dom": '<"row"<"col-sm-6"l><"col-sm-6">>rtip', // Remove default search box
        "responsive": true,
        "deferRender": true,
        "stateSave": true,
        "searching": true,
        "drawCallback": function(settings) {
            // Debug information
            console.log('DataTables Info:', settings.json);
            console.log('Total Records:', settings.json ? settings.json.recordsTotal : 'N/A');
            console.log('Filtered Records:', settings.json ? settings.json.recordsFiltered : 'N/A');
        }
    });

    // Enhanced search functionality
    $('#enhanced_search').on('keyup', function() {
        var searchValue = $(this).val();
        table.search(searchValue).draw();
    });

    // Clear search button
    $('#clear_search').on('click', function() {
        $('#enhanced_search').val('');
        table.search('').draw();
    });

    // Filter functionality
    $('#apply_filters').on('click', function() {
        table.ajax.reload();
    });

    // Clear filters
    $('#clear_filters').on('click', function() {
        $('#filter_status').val('');
        $('#filter_priority').val('');
        $('#filter_project').val('');
        $('#filter_assigned').val('');
        $('#filter_date_from').val('');
        $('#filter_date_to').val('');
        table.ajax.reload();
    });

    // Refresh table
    $('#refresh_table').on('click', function() {
        table.ajax.reload();
    });



    // Auto-apply filters when changed
    $('#filter_status, #filter_priority, #filter_project, #filter_assigned').on('change', function() {
        table.ajax.reload();
    });

    // Date filters
    $('#filter_date_from, #filter_date_to').on('change', function() {
        table.ajax.reload();
    });

    // Initialize floating button tooltip
    $('#floating_quick_add').tooltip();
    
    // Show floating button always (remove scroll-based hiding)
    $('.floating-add-btn').show();
});

// AJAX modal function for quick actions
function show_ajax_modal(url, title) {
    $.ajax({
        url: url,
        type: 'GET',
        success: function(response) {
            $('#ajax_modal_content').html(response);
            $('#ajax_modal_title').text(title);
            $('#ajax_modal').modal('show');
        },
        error: function() {
            alert('Error loading content');
        }
    });
}
</script>
