<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class MY_User_agent extends CI_User_agent {

    public function __construct() {
        parent::__construct();
    }

    /**
     * Test for a tablet device.
     * @return bool
     */
    public function is_tablet() {
        // Add common tablet strings here, this is a simplistic example
        $tablet_agents = array('iPad', 'Android', 'Kindle', 'Tablet', 'Surface');
        
        foreach ($tablet_agents as $device) {
            if (stripos($this->agent, $device) !== false) {

                // Check if device is Android and not a mobile phone
                if ($device === 'Android' && stripos($this->agent, 'mobile') === false) {
                    return true;
                } else if ($device !== 'Android') {
                    return true;
                }
            }
        }
        return false;
    }
}
