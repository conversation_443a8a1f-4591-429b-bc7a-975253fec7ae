<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

defined('BASEPATH') OR exit('No direct script access allowed');

if (! function_exists('get_settings')) {
	function get_settings($key = '') {
		$CI	=&	get_instance();
		$CI->load->database();
		$CI->db->where('key', $key);
		return $CI->db->get('settings')->row('value');
	}
}

if (!function_exists('set_alert')){
	function set_alert($type, $message){
		$CI = & get_instance();
		$CI->session->set_flashdata('flash_type', $type);
		$CI->session->set_flashdata('flash_message', ucfirst($message));
	}
}

if (!function_exists('set_input_value')){
	function set_input_value($value, $default = null){
        return empty($value) ? $default : $value;
	}
}

/**
 * REMOVE EXCEL ICON
 */
if (! function_exists('remove_excel_icon')) {
	function remove_excel_icon($string) {
		$string = str_replace("_x000d_", "<br>", $string);
		$string = str_replace("_x000D_", "<br>", $string);
		return $string;
	}
}

/**
 * SORT ARRAY BY KEY
 */
if (! function_exists('sort_array_by_key')){
    function sort_array_by_key($array, $array_key){
        $is_stock = array_column($array, $array_key);
        array_multisort($is_stock, SORT_DESC, $array);
        return $array;
    }
}


if (!function_exists('show_alert')){
	function show_alert(){
		$CI = & get_instance();
		if (isset($_SESSION['flash_message']) && isset($_SESSION['flash_type'])){
			$THE_MESSAGE = $CI->session->flashdata('flash_message');
			$THE_TYPE = $CI->session->flashdata('flash_type');
			echo "setTimeout( function(){";
			switch ($THE_TYPE){
				case 'message_success':
					echo "toastr.success('{$THE_MESSAGE}')";
					break;
				case 'message_error':
					echo "toastr.error('{$THE_MESSAGE}')";
					break;
				case 'message_warning':
					echo "toastr.warning('{$THE_MESSAGE}')";
					break;
				case 'message_info':
					echo "toastr.info('{$THE_MESSAGE}')";
					break;
				default:
					echo "toastr.info('{$THE_MESSAGE}')";
			}
			echo "}  , 300 );";
			unset($_SESSION['flash_message']);
			unset($_SESSION['flash_type']);
		}
	}

	// Send Email
	if (!function_exists('send_mail')){
		function send_mail($subject, $message, $email)
		{
			if(!empty($message) && !empty($subject) && !empty($email)){
				$ch = curl_init();
				curl_setopt($ch, CURLOPT_URL, "https://trogonmedia.com/send_email_api/iame_verification.php");
				curl_setopt($ch, CURLOPT_POST, 1);// set post data to true
				curl_setopt($ch, CURLOPT_POSTFIELDS,"message={$message}&subject={$subject}&email={$email}");   // post data
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				$json = curl_exec($ch);
				curl_close ($ch);
				log_message('error', json_encode($json, JSON_PRETTY_PRINT));
			}
		}
	}

    if (!function_exists('get_date_array')){
        function get_date_array($from_date, $to_date){
            $dates = [];

            $current_date = new DateTime($from_date);
            $end_date = new DateTime($to_date);

            while ($current_date <= $end_date) {
                $dates[] = $current_date->format('Y-m-d');
                $current_date->modify('+1 day');
            }

            return $dates;
        }
    }


    if (!function_exists('get_time_duration')){
        function get_time_duration($start_time, $end_time){
            // Create two DateTime objects
            $start_time = new DateTime($start_time);
            $end_time = new DateTime($end_time);

            // Convert DateTime objects to timestamps
            $start_time_stamp = $start_time->getTimestamp();
            $end_time_stamp = $end_time->getTimestamp();

            // Calculate the difference in seconds
            return $end_time_stamp - $start_time_stamp;
        }
    }

    if (!function_exists('get_time_from_seconds')){
        function get_time_from_seconds($seconds){

            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            $seconds = $seconds % 60;

            return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
//            return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        }
    }


    if (!function_exists('duration_to_time')){
        function duration_to_time($duration){
            $parts = explode(":", $duration);
            return $parts[0].':'.$parts[1];
        }
    }

    if (!function_exists('print_daily_time')){
        function print_daily_time($duration){
            list($hours, $minutes) = explode(":", $duration);

            // Convert the duration to seconds
            $duration_seconds = ($hours * 3600) + ($minutes * 60);

            // Convert 8 hours to seconds
            $time_limit_seconds = 7 * 3600;

            if ($duration_seconds < $time_limit_seconds) {
                return '<span class="text-danger"><b>'.duration_to_time($duration).'</b> &nbsp;<small>Hours</small></span>';
            } else {
                return '<span class="text-primary"><b>'.duration_to_time($duration).'</b> &nbsp;<small>Hours</small></span>';
            }
        }
    }

    if (!function_exists('print_break_time')){
        function print_break_time($duration){
            list($hours, $minutes) = explode(":", $duration);

            // Convert the duration to seconds
            $duration_seconds = ($hours * 3600) + ($minutes * 60);

            // Convert 8 hours to seconds
            $time_limit_seconds = 1 * 3600;
            $time_limit_seconds_danger = 1 * 3900;

            if ($duration_seconds > $time_limit_seconds_danger) {
                return '<span class="text-danger"><b>' .duration_to_time($duration).'</b> &nbsp;<small>Hours</small></span>';
            }else if ($duration_seconds > $time_limit_seconds) {
                return '<span class="text-primary"><b>' .duration_to_time($duration).'</b> &nbsp;<small>Hours</small></span>';
            } else {
                return '<span style="color: #16a265"><b>' .duration_to_time($duration).'</b> &nbsp;<small>Hours</small></span>';
            }
        }
    }

    if (!function_exists('print_daily_diff')){
        function print_daily_diff($duration){
            list($hours, $minutes) = explode(":", $duration);

            // Convert the duration to seconds
            $duration_seconds = ($hours * 3600) + ($minutes * 60);

            // Convert 8 hours to seconds
            $time_limit_seconds = 7 * 3600;

            // get duration difference
            $time_difference = $duration_seconds - $time_limit_seconds;


            if ($time_difference >= 0){
                return '<span style="color: #16a265"><b>+' .duration_to_time(get_time_from_seconds($time_difference)).'</b> &nbsp;<small>Hours</small></span>';
            }else{
                return '<span class="text-danger"><b>-'.duration_to_time(get_time_from_seconds(abs($time_difference))).'</b> &nbsp;<small>Hours</small></span>';
            }
        }
    }

    if (!function_exists('print_punch_time')){
        function print_punch_time($punch_time, $punch_type, $format_break = false){
            if (empty($punch_time)){
                return '';
            }
            if ($format_break){
                $format_time = DateTime::createFromFormat('H:i:s', $punch_time)->format('g:i');
                $format_type = DateTime::createFromFormat('H:i:s', $punch_time)->format('A');

                $punch_time_formatted = "{$format_time}<br><small>{$format_type}</small>";
            }else{
                $punch_time_formatted = DateTime::createFromFormat('H:i:s', $punch_time)->format('g:i A');
            }


            if ($punch_type == 1){
                list($punch_hours, $punch_minutes) = explode(":", $punch_time);
                $punch_seconds = ($punch_hours * 3600) + ($punch_minutes * 60);

                // punch limit
                list($limit_hours, $limit_minutes) = explode(":", '09:30');
                $limit_seconds = ($limit_hours * 3600) + ($limit_minutes * 60);

                if ($punch_seconds > $limit_seconds){
                    return "<div class='text-danger'>{$punch_time_formatted}<br><small>(Late Coming)</small></div>";
                }else{
                    return $punch_time_formatted;
                }
            }elseif ($punch_type == 8){
                list($punch_hours, $punch_minutes) = explode(":", $punch_time);
                $punch_seconds = ($punch_hours * 3600) + ($punch_minutes * 60);

                // punch limit
                list($limit_hours, $limit_minutes) = explode(":", '17:30');
                $limit_seconds = ($limit_hours * 3600) + ($limit_minutes * 60);

                if ($punch_seconds < $limit_seconds){
                    return "<div class='text-danger'>{$punch_time_formatted}<br><small>(Early Going)</small></div>";
                }else{
                    return $punch_time_formatted;
                }
            }else{
                return $punch_time_formatted;
            }
        }
    }

    if (!function_exists('is_late_coming')){
        function is_late_coming($log_data){
            $log_data = $log_data[1];
            if (!empty($log_data)){
                list($punch_hours, $punch_minutes) = explode(":", $log_data);
                $punch_seconds = ($punch_hours * 3600) + ($punch_minutes * 60);

                // punch limit
                list($limit_hours, $limit_minutes) = explode(":", '09:31');
                $limit_seconds = ($limit_hours * 3600) + ($limit_minutes * 60);

                if ($punch_seconds > $limit_seconds){
                    return TRUE;
                }else{
                    return FALSE;
                }
            }else{
                return FALSE;
            }

        }
    }

    if (!function_exists('is_early_going')){
        function is_early_going($log_data){
            $log_data = $log_data[8];
            if(!empty($log_data)){
                list($punch_hours, $punch_minutes) = explode(":", $log_data);
                $punch_seconds = ($punch_hours * 3600) + ($punch_minutes * 60);

                // punch limit
                list($limit_hours, $limit_minutes) = explode(":", '17:30');
                $limit_seconds = ($limit_hours * 3600) + ($limit_minutes * 60);

                if ($punch_seconds < $limit_seconds){
                    return TRUE;
                }else{
                    return FALSE;
                }
            }else{
                return TRUE;
            }

        }
    }


}

if (!function_exists('add_duration')){
    function add_duration($duration_arr, $return_type = 'duration'){
        $totalSeconds = 0;
        foreach ($duration_arr as $duration) {
            // Directly parse the duration into hours, minutes, and seconds
            list($hours, $minutes, $seconds) = sscanf($duration, "%d:%d:%d");
            // Sum all times in seconds
            $totalSeconds += $hours * 3600 + $minutes * 60 + $seconds;
        }
        if ($return_type == 'duration'){
            return gmdate('H:i:s', $totalSeconds);
        }elseif($return_type == 'seconds'){
            return $totalSeconds;
        }
    }
}

if (!function_exists('time_to_seconds')){
    function time_to_seconds($duration){
        // Split the time string by colon to extract hours, minutes, and seconds
        $parts = explode(':', $duration);

        // Check if the time format is correct
        if (count($parts) !== 3) {
            throw new InvalidArgumentException("Invalid time format. Please use HH:MM:SS format.");
        }

        // Extract hours, minutes, and seconds
        list($hours, $minutes, $seconds) = $parts;

        // Convert the time to seconds
        $total_seconds = $hours * 3600 + $minutes * 60 + $seconds;

        return $total_seconds ?? 0;
    }
}

if (!function_exists('format_time_from_seconds')){
    function format_time_from_seconds($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        $formattedTime = '';

        if ($hours > 0) {
            $formattedTime .= $hours . 'h ';
        }

        if ($minutes > 0) {
            $formattedTime .= $minutes . 'm';
        }

        return trim($formattedTime);
    }
}


if (!function_exists('get_week_dates')){
    function get_week_dates($current_date = null) {
        // Get the current date
        if(empty($current_date)){
            $current_date = date('Y-m-d');
        }

        // Check if today is Sunday
        if (date('N', strtotime($current_date)) == 7) {
            // If today is Sunday, get next Monday and next Saturday
            $start_date = date('Y-m-d', strtotime('next Monday', strtotime($current_date)));
            $end_date = date('Y-m-d', strtotime('next Saturday', strtotime($current_date)));
        } else {
            // Otherwise, get this week's Monday and Saturday
            $start_date = date('Y-m-d', strtotime('this week Monday', strtotime($current_date)));
            $end_date = date('Y-m-d', strtotime('this week Saturday', strtotime($current_date)));
        }

        return ['start_date' => $start_date, 'end_date' => $end_date];
    }
}

if ( ! function_exists('get_months_from_dates')){
    function get_months_from_dates($start_date, $end_date){
        $startDate = new DateTime($start_date);
        $endDate = new DateTime($end_date);

        $interval = new DateInterval('P1M');

        $period = new DatePeriod($startDate, $interval, $endDate);

        $months_array = [];
        foreach ($period as $date) {
            $months_array[] = $date->format('Y-m');
        }
        return $months_array;
    }
}

if ( ! function_exists('format_estimated_time')){
    function format_estimated_time($minutes){
        if (empty($minutes) || $minutes <= 0) {
            return '<span class="text-muted">Not set</span>';
        }

        $hours = floor($minutes / 60);
        $remaining_minutes = $minutes % 60;

        $formatted = '';
        if ($hours > 0) {
            $formatted .= $hours . 'h ';
        }
        if ($remaining_minutes > 0) {
            $formatted .= $remaining_minutes . 'm';
        }

        return '<span class="badge badge-info">' . trim($formatted) . '</span>';
    }
}
