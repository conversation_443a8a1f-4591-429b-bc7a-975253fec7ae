<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

defined('BASEPATH') OR exit('No direct script access allowed');

if (! function_exists('get_task_type')) {
    function get_task_type($task_type = '') {
        switch (strtolower($task_type)) {
            case 'new':
                return '<span class="badge bg-info-lighten text-info" style="width: 80px;padding: 5px">NEW</span>';
            case 'support':
                return '<span class="badge bg-warning-lighten text-danger" style="width: 80px;padding: 5px">QUERIES</span>';
            case 'queries':
                return '<span class="badge bg-warning-lighten text-danger" style="width: 80px;padding: 5px">QUERIES</span>';
            case 'bug':
                return '<span class="badge bg-danger-lighten text-danger" style="width: 80px;padding: 5px">BUG</span>';
            case 'quick_support':
                return '<span class="badge bg-danger" style="width: 100px;padding: 5px">QUICK SUPPORT</span>';
            case 'critical_bug':
                return '<span class="badge bg-danger text-danger" style="width: 80px;padding: 5px">CRITICAL BUG</span>';
            case 'meeting':
                return '<span class="badge bg-primary" style="width: 80px;padding: 5px">MEETING</span>';
        }
    }
}


if (! function_exists('get_task_priority')) {
    function get_task_priority($task_priority = '') {
        switch (strtolower($task_priority)) {
            case 'critical':
                return '<span class="badge bg-danger text-white" style="width: 80px;padding: 5px">CRITICAL</span>';
            case 'high':
                return '<span class="badge bg-danger-lighten text-danger" style="width: 80px;padding: 5px">HIGH</span>';
            case 'medium':
                return '<span class="badge bg-warning-lighten text-danger" style="width: 80px;padding: 5px">MEDIUM</span>';
            case 'low':
                return '<span class="badge bg-info-lighten text-info" style="width: 80px;padding: 5px">LOW</span>';
        }
    }
}

if (! function_exists('get_task_status')) {
    function get_task_status($task_status = '') {
        switch (strtolower($task_status)) {
            case 'pending':
                return '<span class="badge bg-danger-lighten text-danger" style="width: 80px;padding: 5px">PENDING</span>';
            case 'assigned':
                return '<span class="badge bg-info-lighten text-info" style="width: 80px;padding: 5px">ASSIGNED</span>';
            case 'testing':
                return '<span class="badge bg-secondary-lighten text-dark" style="width: 80px;padding: 5px">TESTING</span>';
            case 'on_hold':
                return '<span class="badge bg-warning-lighten text-danger" style="width: 80px;padding: 5px">ON HOLD</span>';
            case 'completed':
                return '<span class="badge bg-success-lighten text-success" style="width: 80px;padding: 5px">COMPLETED</span>';
        }
    }
}

if (! function_exists('get_task_status_button')) {
    function get_task_status_button($task_status = '', $item_id = 0) {
        $action_url = site_url("app/modal/popup/get/{$item_id}/?page_name=tasks/task_status");
        $onclick = "onclick=\"show_ajax_modal('{$action_url}', 'Change Status')\"";
        switch (strtolower($task_status)) {
            case 'pending':
                return '<span class="status_btn badge bg-danger-lighten text-danger" '.$onclick.' style="width: 90px;padding: 5px"><small><i class="fas fa-pencil-alt"></i></small> PENDING</span>';
            case 'assigned':
                return '<span class="status_btn badge bg-info-lighten text-info" '.$onclick.' style="width: 90px;padding: 5px"><small><i class="fas fa-pencil-alt"></i></small> ASSIGNED</span>';
            case 'testing':
                return '<span class="status_btn badge bg-secondary-lighten text-dark" '.$onclick.' style="width: 90px;padding: 5px"><small><i class="fas fa-pencil-alt"></i></small> TESTING</span>';
            case 'on_hold':
                return '<span class="status_btn badge bg-warning-lighten text-danger" '.$onclick.' style="width: 90px;padding: 5px"><small><i class="fas fa-pencil-alt"></i></small> ON HOLD</span>';
            case 'completed':
                return '<span class="status_btn badge bg-success-lighten text-success" '.$onclick.' style="width: 90px;padding: 5px"><small><i class="fas fa-pencil-alt"></i></small> COMPLETED</span>';
        }
    }
}

if (! function_exists('get_task_color')) {
    function get_task_color($task_status = '') {
        switch (strtolower($task_status)) {
            case 'pending':
                return 'bg-danger-lighten';
            case 'assigned':
                return 'bg-info-lighten';
            case 'testing':
                return 'bg-secondary-lighten';
            case 'on_hold':
                return 'bg-warning-lighten';
            case 'completed':
                return 'bg-success-lighten';
        }
    }
}

if (! function_exists('get_todo_priority')) {
    function get_todo_priority($todo_priority = '') {
        switch (strtolower($todo_priority)) {
            case 'high':
                return '<span class="badge bg-danger-lighten text-danger" style="width: 80px;padding: 5px">HIGH</span>';
            case 'medium':
                return '<span class="badge bg-warning-lighten text-danger" style="width: 80px;padding: 5px">MEDIUM</span>';
            case 'low':
                return '<span class="badge bg-info-lighten text-info" style="width: 80px;padding: 5px">LOW</span>';
        }
    }
}

if (! function_exists('get_todo_status')) {
    function get_todo_status($todo_status = '') {
        switch (strtolower($todo_status)) {
            case 'pending':
                return '<span class="badge bg-danger-lighten text-danger" style="width: 80px;padding: 5px">PENDING</span>';
            case 'assigned':
                return '<span class="badge bg-warning-lighten text-danger" style="width: 80px;padding: 5px">ASSIGNED</span>';
            case 'completed':
                return '<span class="badge bg-success-lighten text-success" style="width: 80px;padding: 5px">COMPLETED</span>';
        }
    }
}



// get due date
if (! function_exists('get_due_date')) {
    function get_due_date($due_date) {
        $current_date = date('Y-m-d');
        $due_date_display = DateTime::createFromFormat('Y-m-d', $due_date)->format('d-m-Y');
        if ($due_date < $current_date) {
            return "<span class='text-danger p-1' style='font-size: 14px;font-weight: 600; border-radius: 5px;'><small><i class=\"bi bi-calendar\"></i></small> {$due_date_display}</span>";
        }elseif ($due_date == $current_date) {
            return "<span class='text-info p-1' style='font-size: 14px;font-weight: 600; border-radius: 5px;'><small><i class=\"bi bi-calendar\"></i></small> {$due_date_display}</span>";
        }else{
            return "<span class='text-info p-1' style='font-size: 14px;font-weight: 600'><small><i class=\"bi bi-calendar\"></i></small> {$due_date_display}</span>";
        }
    }
}

// get due date
if (! function_exists('get_due_date_mobile')) {
    function get_due_date_mobile($due_date) {
        $current_date = date('Y-m-d');
        $due_date_display = DateTime::createFromFormat('Y-m-d', $due_date)->format('d-m-Y');
        if ($due_date < $current_date) {
            return "<span class='text-warning p-1' style='font-size: 13px;font-weight: 600; border-radius: 5px;'><small><i class=\"bi bi-calendar\"></i></small> {$due_date_display}</span>";
        }elseif ($due_date == $current_date) {
            return "<span class='text-info p-1' style='font-size: 13px;font-weight: 600; border-radius: 5px;'><small><i class=\"bi bi-calendar\"></i></small> {$due_date_display}</span>";
        }else{
            return "<span class='text-muted p-1' style='font-size: 13px;font-weight: 600'><small><i class=\"bi bi-calendar\"></i></small> {$due_date_display}</span>";
        }
    }
}

// get get_task_bg_employee
if (! function_exists('get_task_bg_employee')) {
    function get_task_bg_employee($task_status, $due_date) {
        if ($task_status == 'assigned' && $due_date < date('Y-m-d')){
            return "bg-danger";
        }elseif($task_status == 'assigned'){
            return "bg-info";
        }elseif($task_status == 'testing'){
            return "bg-dark-lighten";
        }elseif($task_status == 'completed'){
            return "bg-success-lighten";
        }elseif($task_status == 'on_hold'){
            return "bg-warning";
        }
    }
}

// get get_todo_bg_employee
if (! function_exists('get_todo_bg_employee')) {
    function get_todo_bg_employee($todo_status, $due_date) {
        if ($todo_status == 'assigned' && $due_date < date('Y-m-d')){
            return "bg-danger";
        }elseif($todo_status == 'assigned') {
            return "bg-danger-lighten";
        }elseif($todo_status == 'completed'){
            return "bg-success-lighten";
        }
    }
}

if (! function_exists('get_color_code')) {
    function get_color_code($rand_number) {
        $color_classes = [
            ["bg_color" => "#F8BBD0", "text_color" => "#94416E"],
            ["bg_color" => "#F3E5F5", "text_color" => "#A291B2"],
            ["bg_color" => "#E1BEE7", "text_color" => "#875885"],
            ["bg_color" => "#D1C4E9", "text_color" => "#656283"],
            ["bg_color" => "#F0F4C3", "text_color" => "#ADAE70"],
            ["bg_color" => "#FFF9C4", "text_color" => "#757038"],
            ["bg_color" => "#FFECB3", "text_color" => "#CCAE66"],
            ["bg_color" => "#FFE0B2", "text_color" => "#CC9066"],
            ["bg_color" => "#C5CAE9", "text_color" => "#7477A4"],
            ["bg_color" => "#BBDEFB", "text_color" => "#6E77B6"],
            ["bg_color" => "#B3E5FC", "text_color" => "#67A1BF"],
            ["bg_color" => "#B2EBF2", "text_color" => "#66AEBD"],
            ["bg_color" => "#B2DFDB", "text_color" => "#365f52"],
            ["bg_color" => "#C8E6C9", "text_color" => "#75A387"],
            ["bg_color" => "#DCEDC8", "text_color" => "#8BB083"],
            ["bg_color" => "#FFCCBC", "text_color" => "#CC7A74"],
            ["bg_color" => "#D7CCC8", "text_color" => "#867776"],
            ["bg_color" => "#F5F5F5", "text_color" => "#ADADAD"],
            ["bg_color" => "#CFD8DC", "text_color" => "#809192"],
            ["bg_color" => "#ECEFF1", "text_color" => "#A9B3B5"],
        ];

        // generate a random index
        $index = $rand_number % count($color_classes);
        // Return the color at the calculated index
        return $color_classes[$index];
    }
}


if (! function_exists('get_color_code_light')) {
    function get_color_code_light($rand_number) {
        $color_classes = [
            ["bg_color" => "#F0F8FF", "text_color" => "#000080"],
            ["bg_color" => "#FAEBD7", "text_color" => "#8B4513"],
            ["bg_color" => "#F5F5DC", "text_color" => "#8B0000"],
            ["bg_color" => "#FFF0F5", "text_color" => "#8B008B"],
            ["bg_color" => "#FFFACD", "text_color" => "#008000"],
            ["bg_color" => "#E0FFFF", "text_color" => "#8B0000"],
            ["bg_color" => "#FAFAD2", "text_color" => "#B22222"],
            ["bg_color" => "#D3D3D3", "text_color" => "#000000"],
            ["bg_color" => "#FFB6C1", "text_color" => "#800000"],
            ["bg_color" => "#FFFFE0", "text_color" => "#00008B"],
            ["bg_color" => "#F5FFFA", "text_color" => "#8B008B"],
            ["bg_color" => "#FFE4E1", "text_color" => "#8B4513"],
            ["bg_color" => "#FDF5E6", "text_color" => "#6B8E23"],
            ["bg_color" => "#FFEFD5", "text_color" => "#8B0000"],
            ["bg_color" => "#FFDAB9", "text_color" => "#8B008B"],
            ["bg_color" => "#FFF5EE", "text_color" => "#8B0000"],
            ["bg_color" => "#FFFAFA", "text_color" => "#000080"],
            ["bg_color" => "#F5F5F5", "text_color" => "#000000"],
            ["bg_color" => "#FFFAF0", "text_color" => "#6B8E23"],
            ["bg_color" => "#F0FFF0", "text_color" => "#8B0000"],
        ];


        // generate a random index
        $index = $rand_number % count($color_classes);
        // Return the color at the calculated index
        return $color_classes[$index];
    }
}

if (! function_exists('get_project_title')) {
    function get_project_title($project_title) {
        // Generate a pseudo-random number by hashing the text
        $hash = md5($project_title);
        // Convert the hash into an integer
        $intHash = base_convert(substr($hash, 0, 8), 16, 10);

        $color_code = get_color_code($intHash);

        return "<span class='badge p-1 m-0 project_title_badge' style='border-radius:1px 15px 15px 1px; min-width: 120px;padding: 5px 3px!important; font-size:12px!important; background-color: {$color_code['bg_color']}; color: {$color_code['text_color']}'>".strtoupper($project_title)."</span>";
    }
}

if (! function_exists('get_project_title_secondary')) {
    function get_project_title_secondary($project_title) {
        // Generate a pseudo-random number by hashing the text
        $hash = md5($project_title);
        // Convert the hash into an integer
        $intHash = base_convert(substr($hash, 0, 8), 16, 10);

        $color_code = get_color_code($intHash);

        return "<span class='badge p-2' style='border-radius:20px; min-width: 120px;padding: 10px; margin:5px 4px!important; font-size:13px!important; background-color: {$color_code['bg_color']}; color: {$color_code['text_color']}'>".strtoupper($project_title)."</span>";
    }
}

if (! function_exists('get_project_title_third')) {
    function get_project_title_third($project_title) {
        // Generate a pseudo-random number by hashing the text
        $hash = md5($project_title);
        // Convert the hash into an integer
        $intHash = base_convert(substr($hash, 0, 8), 16, 10);

        $color_code = get_color_code($intHash);

        return "<span class='badge p-1' style='border-left:2px solid #b429ec; margin:3px 2px!important; font-size:13px!important; color: {$color_code['text_color']}'>" .strtoupper($project_title)."</span>";
    }
}

if (! function_exists('get_employee_name')) {
    function get_employee_name($employee_name) {
        // Generate a pseudo-random number by hashing the text
        $hash = md5($employee_name);
        // Convert the hash into an integer
        $intHash = base_convert(substr($hash, 0, 8), 16, 10);

        $color_code = get_color_code_light($intHash);

        return "<span class='badge p-1' style='border-radius:2px; font-weight:500!important; min-width: 120px;padding: 5px; font-size:13px!important; background-color: {$color_code['bg_color']}; color: {$color_code['text_color']}'> <small><i class=\"bi bi-person-circle\"></i></small> ".strtoupper($employee_name)."</span>";
    }
}


if (! function_exists('time_to_duration')) {
    function time_to_duration($timeString) {
        $timeString = substr($timeString, 0, -3);
        return !empty($timeString) ? $timeString : '00:00';
    }
}

if (!function_exists('get_work_calendar_details')){
    function get_work_calendar_details($monthYear) {
        [$month, $year] = explode('-', $monthYear);

        $date = new DateTime("$year-$month-01");

        $daysInMonth = $date->format('t');

        $startDay = $date->format('w');

        $date->modify('-1 day');
        $daysInPreviousMonth = $date->format('t');

        $date->modify('first day of this month');

        return [
            'days_in_month' => $daysInMonth,
            'start_day' => $startDay,
            'days_in_previous_month' => $daysInPreviousMonth,
        ];
    }
}


if (!function_exists('get_shorten_name')){
    function get_shorten_name($name) {
        // Split the full name by spaces
        $nameParts = explode(' ', $name);
        $numParts = count($nameParts);

        // If there is only one part, return it as is
        if ($numParts === 1) {
            return $name;
        }

        // Shorten the first part of the name to its initial
        $shortenedName = strtoupper($nameParts[0][0]) . '.';

        // Add the remaining parts unchanged
        for ($i = 1; $i < $numParts; $i++) {
            $shortenedName .= ' ' . $nameParts[$i];
        }

        return $shortenedName;
    }
}




// Add these functions to your existing ui_helper.php file

/**
 * Get ticket status badge
 */
if (! function_exists('get_ticket_status')) {
    function get_ticket_status($ticket_status = '') {
        switch (strtolower($ticket_status)) {
            case 'new':
                return '<span class="badge bg-info-lighten text-info" style="width: 80px;padding: 5px">NEW</span>';
            case 'assigned':
                return '<span class="badge bg-warning-lighten text-warning" style="width: 80px;padding: 5px">ASSIGNED</span>';
            case 'closed':
                return '<span class="badge bg-success-lighten text-success" style="width: 80px;padding: 5px">CLOSED</span>';
            case 'on_hold':
                return '<span class="badge bg-secondary-lighten text-secondary" style="width: 80px;padding: 5px">ON HOLD</span>';
            case 're_open':
                return '<span class="badge bg-danger-lighten text-danger" style="width: 80px;padding: 5px">RE-OPEN</span>';
            default:
                return '<span class="badge bg-light text-dark" style="width: 80px;padding: 5px">UNKNOWN</span>';
        }
    }
}

/**
 * Get ticket status with action button
 */
if (! function_exists('get_ticket_status_button')) {
    function get_ticket_status_button($ticket_status = '', $item_id = 0) {
        $action_url = site_url("app/modal/popup/get/{$item_id}/?page_name=tickets/change_status");
        $onclick = "onclick=\"show_ajax_modal('{$action_url}', 'Change Status')\"";
        
        switch (strtolower($ticket_status)) {
            case 'new':
                return '<span class="status_btn badge bg-info-lighten text-info" '.$onclick.' style="width: 90px;padding: 5px;cursor:pointer"><small><i class="fas fa-pencil-alt"></i></small> NEW</span>';
            case 'assigned':
                return '<span class="status_btn badge bg-warning-lighten text-warning" '.$onclick.' style="width: 90px;padding: 5px;cursor:pointer"><small><i class="fas fa-pencil-alt"></i></small> ASSIGNED</span>';
            case 'closed':
                return '<span class="status_btn badge bg-success-lighten text-success" '.$onclick.' style="width: 90px;padding: 5px;cursor:pointer"><small><i class="fas fa-pencil-alt"></i></small> CLOSED</span>';
            case 'on_hold':
                return '<span class="status_btn badge bg-secondary-lighten text-secondary" '.$onclick.' style="width: 90px;padding: 5px;cursor:pointer"><small><i class="fas fa-pencil-alt"></i></small> ON HOLD</span>';
            case 're_open':
                return '<span class="status_btn badge bg-danger-lighten text-danger" '.$onclick.' style="width: 90px;padding: 5px;cursor:pointer"><small><i class="fas fa-pencil-alt"></i></small> RE-OPEN</span>';
            default:
                return '<span class="status_btn badge bg-light text-dark" '.$onclick.' style="width: 90px;padding: 5px;cursor:pointer"><small><i class="fas fa-pencil-alt"></i></small> UNKNOWN</span>';
        }
    }
}

/**
 * Get ticket priority color
 */
if (! function_exists('get_ticket_priority_color')) {
    function get_ticket_priority_color($priority = '') {
        switch (strtolower($priority)) {
            case 'critical':
                return 'bg-dark';
            case 'high':
                return 'bg-danger-lighten';
            case 'medium':
                return 'bg-warning-lighten';
            case 'low':
                return 'bg-info-lighten';
            default:
                return 'bg-light';
        }
    }
}

/**
 * Get ticket type color
 */
if (! function_exists('get_ticket_type_color')) {
    function get_ticket_type_color($type = '') {
        switch (strtolower($type)) {
            case 'new':
                return 'bg-info-lighten';
            case 'bug':
                return 'bg-danger-lighten';
            case 'support':
                return 'bg-warning-lighten';
            case 'quick_support':
                return 'bg-danger';
            case 'queries':
                return 'bg-warning-lighten';
            case 'critical_bug':
                return 'bg-danger';
            default:
                return 'bg-light';
        }
    }
}

/**
 * Get formatted ticket ID
 */
if (! function_exists('get_formatted_ticket_id')) {
    function get_formatted_ticket_id($ticket_id) {
        return '#' . str_pad($ticket_id, 6, '0', STR_PAD_LEFT);
    }
}

/**
 * Get ticket age in days
 */
if (! function_exists('get_ticket_age')) {
    function get_ticket_age($ticket_date) {
        // Handle NULL or empty ticket dates
        if (empty($ticket_date) || $ticket_date === '0000-00-00 00:00:00') {
            return '<span class="text-muted">No date set</span>';
        }

        $ticket_date = new DateTime($ticket_date);
        $current_date = new DateTime();
        $interval = $ticket_date->diff($current_date);

        if ($interval->days == 0) {
            return '<span class="text-success">Today</span>';
        } elseif ($interval->days == 1) {
            return '<span class="text-info">1 day ago</span>';
        } elseif ($interval->days <= 7) {
            return '<span class="text-warning">' . $interval->days . ' days ago</span>';
        } else {
            return '<span class="text-danger">' . $interval->days . ' days ago</span>';
        }
    }
}

/**
 * Calculate ticket resolution time with detailed hours/minutes display
 */
if (! function_exists('get_ticket_resolution_time')) {
    function get_ticket_resolution_time($ticket_date, $close_date) {
        // Handle NULL or empty dates
        if (empty($ticket_date) || $ticket_date === '0000-00-00 00:00:00' ||
            empty($close_date) || $close_date === '0000-00-00 00:00:00') {
            return 'N/A';
        }

        $ticket_date = new DateTime($ticket_date);
        $close_date = new DateTime($close_date);
        $interval = $ticket_date->diff($close_date);

        // Calculate resolution time display
        if ($interval->days > 0) {
            $resolution_display = $interval->days . ' day(s)';
            if ($interval->h > 0 || $interval->i > 0) {
                $resolution_display .= ', ' . $interval->h . 'h ' . $interval->i . 'm';
            }
        } else {
            // Same day resolution - show hours and minutes
            if ($interval->h > 0) {
                $resolution_display = $interval->h . ' hour(s)';
                if ($interval->i > 0) {
                    $resolution_display .= ', ' . $interval->i . ' minute(s)';
                }
            } else if ($interval->i > 0) {
                $resolution_display = $interval->i . ' minute(s)';
            } else {
                $resolution_display = 'Less than 1 minute';
            }
        }

        return $resolution_display;
    }
}
?>
