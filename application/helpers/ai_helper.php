<?php
function getTaskTimeData($input) {
    $apiKey = '********************************************************************************************************************************************************************';

    $url = 'https://api.openai.com/v1/chat/completions';

    $data = [
        "model" => "gpt-4.1-nano",
        "messages" => [
            [
                "role" => "system",
                "content" => "Extract time entries from the user's input and return them as structured JSON. 
                You should Finetune user remarks to a meaningful remarks (Correct grammer, little descriptive for meaning. 
                Also include html tags formatting like paragraph for remarks). 
                You should return time taken, start time, end time in 00:00:00 (HH:MM:SS) format."
            ],
            [
                "role" => "user",
                "content" => $input
            ]
        ],
        "tools" => [
            [
                "type" => "function",
                "function" => [
                    "name" => "extract_task_data",
                    "description" => "Extract task time logs from input string",
                    "parameters" => [
                        "type" => "object",
                        "properties" => [
                            "entries" => [
                                "type" => "array",
                                "items" => [
                                    "type" => "object",
                                    "properties" => [
                                        "task_id" => ["type" => "integer"],
                                        "start_time" => ["type" => "string", "format" => "time"],
                                        "end_time" => ["type" => "string", "format" => "time"],
                                        "time_taken" => ["type" => "string", "format" => "time"],
                                        "remarks" => ["type" => "string", "format" => "html", "description" => "Provide remarks in html format"]
                                    ],
                                    "required" => ["task_id", "start_time", "end_time", "time_taken", "remarks"]
                                ]
                            ]
                        ],
                        "required" => ["entries"]
                    ]
                ]
            ]
        ],
        "tool_choice" => [
            "type" => "function",
            "function" => ["name" => "extract_task_data"]
        ]
    ];

    $headers = [
        'Authorization: Bearer ' . $apiKey,
        'Content-Type: application/json'
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        throw new Exception('cURL error: ' . curl_error($ch));
    }

    curl_close($ch);

    if ($httpCode != 200) {
        throw new Exception('API error: ' . $response);
    }

    $responseData = json_decode($response, true);

    if (isset($responseData['choices'][0]['message']['tool_calls'][0]['function']['arguments'])) {
        $structured = json_decode($responseData['choices'][0]['message']['tool_calls'][0]['function']['arguments'], true);
        return $structured['entries']; // Return just the list
    } else {
        throw new Exception('Unexpected API response format');
    }
}

