<?php

// get the list of cpanel accounts
function get_cpanel_accounts($whm_username, $token, $ip_address){
    $token = base64_decode($token);
    $query = "https://{$ip_address}:2087/json-api/listaccts?api.version=1";
        
    $curl = curl_init();                                    
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
        "Authorization: whm $whm_username:$token"
    ));
    curl_setopt($curl, CURLOPT_URL, $query);
    
    $result = curl_exec($curl);
    if ($result == false) {
        error_log('Curl error: ' . curl_error($curl));
        curl_close($curl);
        die('An error occurred while fetching the cPanel accounts.');
    }
    
    curl_close($curl);
    
    $data = json_decode($result, true);
    return $data;
}

// get the cpanel addon domains
function get_cpanel_addon_domains($whm_username, $token, $ip_address, $cpanel_username){
    $token = base64_decode($token);
    $query = "https://{$ip_address}:2087/json-api/cpanel?cpanel_jsonapi_user=$cpanel_username&cpanel_jsonapi_apiversion=2&cpanel_jsonapi_module=AddonDomain&cpanel_jsonapi_func=listaddondomains";
    
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
        "Authorization: whm $whm_username:$token"
    ));
    curl_setopt($curl, CURLOPT_URL, $query);
    
    $result = curl_exec($curl);
    if ($result == false) {
        error_log('Curl error: ' . curl_error($curl));
        curl_close($curl);
        die('An error occurred while fetching addon domains.');
    }
    
    curl_close($curl);
    
    $data = json_decode($result, true);
    
    return $data;
}

// update cpanel password
function update_cpanel_password($whm_username, $token, $ip_address, $cpanel_username, $new_password){
    $token = base64_decode($token);
    $query = "https://{$ip_address}:2087/json-api/passwd?api.version=1&user=$cpanel_username&password=" . urlencode($new_password);

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
        "Authorization: whm $whm_username:$token"
    ));
    curl_setopt($curl, CURLOPT_URL, $query);

    $result = curl_exec($curl);
    if ($result == false) {
        error_log('Curl error: ' . curl_error($curl));
        curl_close($curl);
        return 'An error occurred while changing the password.';
    }

    curl_close($curl);
    $data = json_decode($result, true);

    if (isset($data['metadata']['result'])) {
        return true;
    } else {
        return false;
    }
}

// generate password
function generate_secure_password($length = 15) {
    $lowercase = 'abcdefghijklmnopqrstuvwxyz';
    $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $numbers = '0123456789';
    $symbols = '!@#$%&';
    $combined = $lowercase . $uppercase . $numbers . $symbols;
    $password = '';
    $password .= $lowercase[rand(0, strlen($lowercase) - 1)];
    $password .= $uppercase[rand(0, strlen($uppercase) - 1)];
    $password .= $numbers[rand(0, strlen($numbers) - 1)];
    $password .= $symbols[rand(0, strlen($symbols) - 1)];
    for ($i = strlen($password); $i < $length; $i++) {
        $password .= $combined[rand(0, strlen($combined) - 1)];
    }
    $password = str_shuffle($password);
    return $password;
}