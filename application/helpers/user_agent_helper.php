<?php
if (!function_exists('is_mobile')){
    function is_mobile(){
        $CI =& get_instance();
        $CI->load->library('user_agent');
        return $CI->agent->is_mobile();
    }
}

if (!function_exists('is_tablet')){
    function is_tablet(){
        $CI =& get_instance();
        $CI->load->library('user_agent');
        return $CI->agent->is_tablet();
    }
}

if (!function_exists('is_robot')){
    function is_robot(){
        $CI =& get_instance();
        $CI->load->library('user_agent');
        return $CI->agent->is_robot();
    }
}

if (!function_exists('get_browser')){
    function get_browser(){
        $CI =& get_instance();
        $CI->load->library('user_agent');
        return $CI->agent->browser().' '. $CI->agent->version();
    }
}