<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Video_uploader extends CI_Controller {
    
    public function stream($videoKey)
    {
        $endpoint = 'https://4d1c8bc90125c73e32267b50f661590f.r2.cloudflarestorage.com/' . $videoKey;
        $authToken = 'Bearer 1b9Uz2NieDCXAiia63uU13cb5QpDcICdbSUbDzl3';

        // Initialize a cURL session
        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization' => $authToken
        ]);

        // Check if there is a range specified in the HTTP header
        if (isset($_SERVER['HTTP_RANGE'])) {
            curl_setopt($ch, CURLOPT_RANGE, str_replace('bytes=', '', $_SERVER['HTTP_RANGE']));
        }

        // Execute the request
        $response = curl_exec($ch);
        $info = curl_getinfo($ch);
        $httpCode = $info['http_code'];
        $contentType = $info['content_type'];
        curl_close($ch);

        // If response is not successful, return an error
        if ($httpCode !== 200 && $httpCode !== 206) {
            $this->output
                 ->set_status_header($httpCode)
                 ->set_content_type($contentType)
                 ->set_output('Error fetching video');
            return;
        }

        // Parse headers to separate content from actual data
        list($headers, $content) = explode("\r\n\r\n", $response, 2);

        // If partial content, respect the range request
        if ($httpCode === 206) {
            list($part1, $range) = explode("\r\n", $headers);
            header("HTTP/1.1 206 Partial Content");
            header("Accept-Ranges: bytes");
            header("Content-Range: $range");
        }

        // Stream the content
        $this->output
             ->set_status_header($httpCode)
             ->set_content_type($contentType)
             ->set_output($content);
    }

    public function index()
    {
        $videoUrl = $this->input->get('video_url');
        if (!$videoUrl) {
            $videoUrl = "https://pms.trogon.info/assets/logo/logo.png";
        }

        $videoData = $this->fetchVideo($videoUrl);
        if (!$videoData) {
            echo "Failed to fetch video.";
            return;
        }

        $result = $this->pushToCloudflare($videoData);
        echo $result ? "Upload successful." : "Failed to upload.";
    }

    private function fetchVideo($url)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_BINARYTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);

        return $response ? $response : false;
    }

    private function pushToCloudflare($videoData)
    {
        $endpoint = 'https://4d1c8bc90125c73e32267b50f661590f.r2.cloudflarestorage.com/trogon-video';
        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Authorization: Bearer 1b9Uz2NieDCXAiia63uU13cb5QpDcICdbSUbDzl3']);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $videoData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);

        return $response ? true : false;
    }
}
