<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: PMS
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: https://trogonmedia.com
 */

class Project_phases extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('project_phases_m');
        $this->load->model('users_m');
    }

    public function index() {
        $this->data['users'] = array_column($this->users_m->get([], ['id', 'name'])->result_array(), 'name', 'id');
        $this->data['list_items'] = $this->project_phases_m->get(null, null, ['key' => 'phase_order', 'direction' => 'asc'])->result_array();
        $this->data['page_title'] = 'Project Phases';
        $this->data['page_name'] = 'project_phases/index';
        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'short_description' => $this->input->post('short_description'),
                'is_deliverable' => $this->input->post('is_deliverable') ? 1 : 0,
                'phase_order' => $this->input->post('phase_order') ? intval($this->input->post('phase_order')) : 0,
                'user_id' => json_encode($this->input->post('user_id') ?? []),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->project_phases_m->insert($data);
            set_alert('message_success', 'Project Phase Added Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function edit($item_id){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'short_description' => $this->input->post('short_description'),
                'is_deliverable' => $this->input->post('is_deliverable') ? 1 : 0,
                'phase_order' => $this->input->post('phase_order') ? intval($this->input->post('phase_order')) : 0,
                'user_id' => json_encode($this->input->post('user_id') ?? []),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->project_phases_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Project Phase Updated Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->project_phases_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Project Phase Deleted Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    /**
     * Update the order of phases via AJAX
     */
    public function update_order(){
        $response = ['status' => 'error', 'message' => 'No data received'];
        
        if ($this->input->post('phases')) {
            $phases = $this->input->post('phases');
            
            $this->db->trans_start();
            
            foreach ($phases as $phase) {
                if (isset($phase['id']) && isset($phase['order'])) {
                    $this->project_phases_m->update(
                        ['phase_order' => intval($phase['order'])], 
                        ['id' => intval($phase['id'])]
                    );
                }
            }
            
            $this->db->trans_complete();
            
            if ($this->db->trans_status() === FALSE) {
                $response = ['status' => 'error', 'message' => 'Database error occurred'];
            } else {
                $response = ['status' => 'success', 'message' => 'Order updated successfully'];
            }
        }
        
        echo json_encode($response);
        exit;
    }
}