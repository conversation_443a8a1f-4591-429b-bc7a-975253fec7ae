<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Projects extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('projects_m');
        $this->load->model('project_type_m');
        $this->load->model('clients_m');
        $this->load->model('users_m');
        $this->load->model('teams_m');
    }

    public function index() {
        $users = $this->users_m->get()->result_array();
        $this->data['users'] = array_column($users, 'name', 'id');
        
        $this->data['client_list']      = array_column($this->clients_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['project_type']     = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['list_items']       = $this->projects_m->get_project_list();
        $this->data['status_count']     = $this->projects_m->get_status_count($this->data['list_items']);
        $this->data['teams']        = array_column($this->teams_m->get()->result_array(), 'title', 'id');
        $this->data['page_title']   = 'Projects';
        $this->data['page_name']    = 'projects/index';
        $this->load->view('app/index', $this->data);
    }
    public function new_projects() {
        $users = $this->users_m->get()->result_array();
        $this->data['users'] = array_column($users, 'name', 'id');

        $this->data['client_list']      = array_column($this->clients_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['project_type']     = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');

        $this->db->where('is_delivered', 0);
        $this->db->or_where('is_delivered', null);
        $this->data['list_items']       = $this->projects_m->get_project_list();
        $this->data['status_count']     = $this->projects_m->get_status_count($this->data['list_items']);
        $this->data['teams']        = array_column($this->teams_m->get()->result_array(), 'title', 'id');

        $this->data['page_title']   = 'Projects';
        $this->data['page_name']    = 'projects/new_projects';
        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $project_teams = $this->input->post('project_teams');
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'tester_id' => $this->input->post('tester_id'),
                'project_lead_id' => $this->input->post('project_lead_id'),
                'project_teams' => json_encode($project_teams),
                'client_id' => $this->input->post('client_id'),
                'project_type' => $this->input->post('project_type'),
                'is_delivered' => $this->input->post('is_delivered') == 1 ? 1 : 0,
                'phone' => $this->input->post('phone'),
                'email' => $this->input->post('email'),
                'url_web' => $this->input->post('url_web'),
                'url_android' => $this->input->post('url_android'),
                'url_ios' => $this->input->post('url_ios'),
                'start_date' => set_input_value($this->input->post('start_date')),
                'project_date' => set_input_value($this->input->post('project_date')),
                'project_logo' => $this->input->post('project_logo'),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->projects_m->insert($data);
            set_alert('message_success', 'Project Added Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function edit($item_id){
        if ($this->input->post()){
            $project_teams = $this->input->post('project_teams');
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'tester_id' => $this->input->post('tester_id'),
                'project_lead_id' => $this->input->post('project_lead_id'),
                'project_teams' => json_encode($project_teams),
                'client_id' => $this->input->post('client_id'),
                'project_type' => $this->input->post('project_type'),
                'is_delivered' => $this->input->post('is_delivered') == 1 ? 1 : 0,
                'phone' => $this->input->post('phone'),
                'email' => $this->input->post('email'),
                'url_web' => $this->input->post('url_web'),
                'url_android' => $this->input->post('url_android'),
                'url_ios' => $this->input->post('url_ios'),
                'start_date' => set_input_value($this->input->post('start_date')),
                'project_date' => set_input_value($this->input->post('project_date')),
                'project_logo' => $this->input->post('project_logo'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->projects_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Project Updated Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->projects_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Project Deleted Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }
}
