<?php
class Employee_report extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('users_m');
        $this->load->model('projects_m');
        $this->load->model('work_report_m');
        $this->load->model('work_assign_m');
        $this->load->model('attendance_m');
        $this->load->model('time_log_m');
        $this->load->model('employee_report_m');
        ini_set('pcre.backtrack_limit', 100000000);
        ini_set('memory_limit', '2048M');
    }

    public function index(){
        $user_id = $this->input->get('user_id');
        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');
        if(!empty($start_date) && !empty($end_date) && !empty($user_id)){
            $this->data['report'] = $this->employee_report_m->get_report($start_date, $end_date, $user_id);
        }
        // get users
        $this->data['users'] = $this->users_m->get([
            'employee_status' => 1, 'is_employee' => 1
        ], null, ['key' => 'name', 'direction' => 'ASC'])->result_array();
        $this->data['users'] = array_column($this->data['users'], 'name', 'id');

        $this->data['page_title']   = 'Employee Report';
        $this->data['page_name']    = 'employee_report/index';
        $this->load->view('app/index', $this->data);
    }
    public function get_index_pdf($start_date, $end_date, $user_id){
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_left' => 3,
            'margin_right' => 3,
            'margin_top' => 3,
            'margin_bottom' => 3,
        ]);

        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

        $data['report'] = $this->employee_report_m->get_report($start_date, $end_date, $user_id);

        $mpdf->AddPage();
        $html = $this->load->view('app/employee_report/index_pdf', $data, true);
        $mpdf->WriteHTML($html);
        $start_date = DateTime::createFromFormat('Y-m-d', $start_date)->format('d-m-Y');
        $end_date = DateTime::createFromFormat('Y-m-d', $end_date)->format('d-m-Y');
        $pdf_url = "uploads/employee_report/Employee_Report_{$start_date}_{$end_date}_{$user_id}.pdf";

        $mpdf->Output($pdf_url,'F');
        if ($_GET['view']){
            $mpdf->Output();
        }
        return base_url($pdf_url);
    }
    public function index1(){
        $user_id = $this->input->get('user_id');
        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');
        if(!empty($start_date) && !empty($end_date) && !empty($user_id)){
            $report = $this->employee_report_m->get_report($start_date, $end_date, $user_id, 1);
        }
        echo json_encode($report);
    }

    // get employee work report
    public function work_rpeort(){
        $user_id = $this->input->get('user_id');
        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');
        if(!empty($start_date) && !empty($end_date) && !empty($user_id)){
            $report = $this->employee_report_m->get_employee_work_report($start_date, $end_date, $user_id);
        }

        // get users
        $this->data['users'] = $this->users_m->get([
            'employee_status' => 1, 'is_employee' => 1
        ], null, ['key' => 'name', 'direction' => 'ASC'])->result_array();
        $this->data['users'] = array_column($this->data['users'], 'name', 'id');

        $this->data['user_details'] = $this->users_m->get(['id' => $user_id])->row_array();

        $this->data['report'] = $report;
        $this->data['page_title']   = 'Employee Report';
        $this->data['page_name']    = 'employee_report/work_report';
        $this->load->view('app/index', $this->data);
    }
}