<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Teams extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('teams_m');
        $this->load->model('users_m');
        $this->load->model('projects_m');
        $this->load->model('project_type_m');
    }

    public function index() {
        $users = $this->users_m->get()->result_array();
        $this->data['users'] = array_column($users, 'name', 'id');
        $this->data['teams'] = $this->get_teams_with_projects();
        $this->data['project_type'] = array_column($this->project_type_m->get()->result_array(), 'title', 'id');
        $this->data['page_title']   = 'Teams';
        $this->data['page_name']    = 'teams/index';
        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'team_type' => $this->input->post('team_type'),
                'team_lead_id' => $this->input->post('team_lead_id'),
                'team_members' => json_encode($this->input->post('team_members')),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->teams_m->insert($data);
            set_alert('message_success', 'Team Added Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function edit($item_id){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'team_type' => $this->input->post('team_type'),
                'team_lead_id' => $this->input->post('team_lead_id'),
                'team_members' => json_encode($this->input->post('team_members')),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->teams_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Team Updated Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->teams_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Team Deleted Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    // get teams by project
    private function get_teams_with_projects(){
        $teams = $this->teams_m->get()->result_array();
        foreach ($teams as $key => $team) {
            $teams[$key]['projects'] = $this->projects_m->get_projects_by_team_id($team['id']);
        }
        return $teams;
    }
}
