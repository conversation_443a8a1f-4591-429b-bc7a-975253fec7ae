<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: PMS
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: https://trogonmedia.com
 */

class Aptitude_summary extends App_Controller {
    public function __construct() {
        parent::__construct();
        $this->load->model('aptitude_candidates_m');
        $this->load->model('aptitude_candidate_answers_m');
        $this->load->model('aptitude_questions_m');
        $this->load->helper('date');
    }

    /**
     * Main index page for aptitude summary
     * Lists all candidates with date and district filters
     */
    public function index() {
        // Get filter parameters
        $start_date = $this->input->get('start_date') ?: date('Y-m-01');
        $end_date = $this->input->get('end_date') ?: date('Y-m-t');
        $district = $this->input->get('district') ?: '';

        // Build where clause based on filters
        $where = [];

        if (!empty($start_date) && !empty($end_date)) {
            $where['DATE(created_at) >='] = $start_date;
            $where['DATE(created_at) <='] = $end_date;
        }

        if (!empty($district)) {
            $where['district'] = $district;
        }

        // Get all candidates based on filters
        $candidates = $this->aptitude_candidates_m->get(
            $where,
            null,
            ['key' => 'created_at', 'direction' => 'DESC']
        )->result_array();

        // Get unique districts for the filter dropdown
        $districts = $this->db->select('DISTINCT(district) as district')
            ->from('aptitude_candidates')
            ->order_by('district', 'ASC')
            ->get()
            ->result_array();

        // Prepare data for the view
        $this->data['candidates'] = $candidates;
        $this->data['districts'] = array_column($districts, 'district');
        $this->data['start_date'] = $start_date;
        $this->data['end_date'] = $end_date;
        $this->data['selected_district'] = $district;

        // Load the view
        $this->data['page_title'] = 'Aptitude Test Summary';
        $this->data['page_name'] = 'aptitude_summary/index';
        $this->load->view('app/index', $this->data);
    }



    /**
     * Update candidate ratings and remarks
     * Handles AJAX request to update candidate evaluation data
     */
    public function update_ratings() {
        // Check if this is an AJAX request
        if (!$this->input->is_ajax_request()) {
            echo json_encode(['status' => 'error', 'message' => 'Invalid request method']);
            return;
        }

        $candidate_id = $this->input->post('candidate_id');

        // Check if candidate exists
        $candidate = $this->aptitude_candidates_m->get(['id' => $candidate_id])->row_array();

        if (empty($candidate)) {
            echo json_encode(['status' => 'error', 'message' => 'Candidate not found']);
            return;
        }

        // Prepare data for update
        $update_data = [
            'rating_total' => $this->input->post('rating_total'),
            'rating_ai' => $this->input->post('rating_ai'),
            'rating_technical' => $this->input->post('rating_technical'),
            'rating_communication' => $this->input->post('rating_communication'),
            'rating_interviewer' => $this->input->post('rating_interviewer'),
            'remarks' => $this->input->post('remarks'),
            'remarks_interviewer' => $this->input->post('remarks_interviewer'),
            'suggested_questions' => $this->input->post('suggested_questions'),
            'updated_by' => get_user_id(),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update candidate data
        $result = $this->aptitude_candidates_m->update($update_data, ['id' => $candidate_id]);

        if ($result) {
            echo json_encode(['status' => 'success', 'message' => 'Candidate ratings updated successfully']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to update candidate ratings']);
        }
    }

    /**
     * 
     */
    public function process_answers($candidate_id = 0){

        $apiKey = '********************************************************************************************************************************************************************'; // Replace this with your actual API key

        // 1. Define the complete question set with options
        $questions = <<<TEXT
        Evaluate the candidate's answers to the following PHP Aptitude Test. You're assessing a potential intern for a PHP Developer role who should be:
        - Technically curious and open to AI,
        - Communicative,
        - Adaptable and positive.
        - Provide AI score based on prompt written in (13th question), if not provided answer for this question then reduce atleast 5 points for AI.

        Here are the questions:

        1. What's the difference between `#header { … }` and `.header { … }` in CSS?
            a) `#header` targets id, `.header` targets class  (Correct)
            b) `#header` targets class, `.header` targets id  
            c) `#header` can apply to multiple, `.header` to one  
            d) No difference  

        2. Which method would you use to select the element with id="main"?
            a) document.getElementById("main")  (Correct)
            b) document.querySelectorAll("#main")  
            c) document.getElementsByClassName("main")  
            d) document.getElementsByTagName("main")  

        3. In a row of trees, every third tree is an apple tree, every fifth is an orange tree. Which position is the first tree to bear both fruits?
            a) 15  (Correct)
            b) 30  
            c) 10  
            d) 5  

        4. You're given a task with unclear requirements and a tight deadline. What's your first step?
            a) Start coding  
            b) Ask clarifying questions and draft a quick spec  (Correct)
            c) Delegate to teammates  
            d) Propose extending deadline  

        5. What's the difference between `include` and `require` in PHP?
            a) include = fatal error; require = warning  
            b) require = fatal error; include = warning  (Correct)
            c) No difference  
            d) include can only load once  

        6. What will this output?  
        \$a = 5; echo \$a++ + ++\$a;
            a) 11  
            b) 12  
            c) 10  
            d) Undefined behavior  

        7. In 2–3 sentences, explain what "API" means to someone without a tech background.

        8. Briefly describe a time you faced a setback and how you stayed positive.

        9. Tell us about a new tool or technology you picked up on your own. How did you go about learning it?

        10. When processes or priorities change mid‑project, what do you do to adapt and keep things on track?

        11. What is database normalization? List the first three normal forms.

        12. Which is the best prompt to get a step-by-step guide to implement user authentication in PHP using JWT?
            a) "Tell me about PHP authentication"  
            b) "Explain JWT"  
            c) "Write a PHP script for authentication"  
            d) "Write a step‑by‑step tutorial in PHP that shows how to set up user registration, login, and protected routes using JWT..."  

        13. Write a well‑structured prompt for ChatGPT to design a relational DB schema for an e-commerce app. Include entities, fields, data types, relationships (up to 3NF), and table purposes.
        TEXT;

        $user_answers = $this->aptitude_candidate_answers_m->get(['candidate_id' => $candidate_id], ['question_id', 'answer'])->result_array();

        // 2. Collect candidate's answers from POST (already structured as array of objects)
        $rawAnswers = $user_answers ?? '[]';

        // 3. Format answers as a readable block
        $userAnswers = "Candidate's answers:\n";
        foreach ($rawAnswers as $entry) {
            $qNo = $entry['question_id'];
            $answer = trim($entry['answer']);
            $userAnswers .= "Q$qNo: $answer\n";
        }

        // 4. Prepare payload
        $data = [
            "model" => "gpt-4o",
            "messages" => [
                ["role" => "system", "content" => $questions],
                ["role" => "user", "content" => $userAnswers]
            ],
            "tools" => [[
                "type" => "function",
                "function" => [
                    "name" => "return_structured_review",
                    "parameters" => [
                        "type" => "object",
                        "properties" => [
                            "rating_total" => ["type" => "number"],
                            "rating_ai" => ["type" => "number"],
                            "rating_technical" => ["type" => "number"],
                            "rating_communication" => ["type" => "number"],
                            "suggested_questions" =>  ["type" => "string"],
                            "remarks" => ["type" => "string"]
                        ],
                        "required" => [
                            "rating_total", "rating_ai", "rating_technical",
                            "rating_communication", "suggested_questions", "remarks"
                        ]
                    ]
                ]
            ]],
            "tool_choice" => ["type" => "function", "function" => ["name" => "return_structured_review"]]
        ];

        // 5. Send to OpenAI API via curl
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer $apiKey",
            "Content-Type: application/json"
        ]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            // echo json_encode(["error" => curl_error($ch)]);
        } elseif ($httpCode !== 200) {
            // echo json_encode(["error" => "HTTP $httpCode: $response"]);
        } else {
            $result = json_decode($response, true);
            $candidate_analysis = $result['choices'][0]['message']['tool_calls'][0]['function']['arguments'] ?? null;
            $candidate_analysis = json_decode($candidate_analysis, true);
            

            $candidate_summary = [
                'rating_total' => $candidate_analysis['rating_total'] ?? null,
                'rating_ai' => $candidate_analysis['rating_ai'] ?? null,
                'rating_technical' => $candidate_analysis['rating_technical'] ?? null,
                'rating_communication' => $candidate_analysis['rating_communication'] ?? null,
                'suggested_questions' => $candidate_analysis['suggested_questions'] ?? null,
                'remarks' => $candidate_analysis['remarks'] ?? null,
                'status' => 'evaluated',
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // print_r($candidate_summary);

            $this->aptitude_candidates_m->update($candidate_summary, ['id' => $candidate_id]);
            log_message('error', $this->db->last_query());

            if ($candidate_analysis) {
                // echo json_encode($candidate_analysis);
            } else {
                echo json_encode(["error" => "No structured output received."]);
            }
        }

        curl_close($ch);
        set_alert('message_success', 'Generated Successfully!');
        redirect($_SERVER['HTTP_REFERER']);

    }
}
