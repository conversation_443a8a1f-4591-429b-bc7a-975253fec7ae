<?php

class Team_members extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('users_m');
    }

    public function index() {
        if (!has_permission('team_members/index')) {
            redirect('app/dashboard/index');
        }

        $this->data['page_title']    = 'Team Members';
        $this->data['page_name']     = 'team_members/index';
        $this->load->view('app/index', $this->data);

    }
}