<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Pending_report_employee extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('tasks_m');
        $this->load->model('users_m');
        $this->load->model('projects_m');
        $this->load->model('time_log_m');
        $this->load->model('attendance_m');
        $this->load->model('time_log_type_m');
    }

    public function index() {
        $this->data['list_items']   = $this->_pending_data();
        $this->data['page_title']   = 'Pending Report - Employee';
        $this->data['page_name']    = 'pending_report_employee/index';
        $this->load->view('app/index', $this->data);
    }

    private function _pending_data(){
        ini_set('display_errors', 0);
        $users_array = $this->users_m->get(
            ['is_employee' => 1, 'employee_status' => 1],
            ['id', 'name'],
            ['key' => 'users.name', 'direction' => 'ASC']
        )->result_array();
        $users = [];
        foreach ($users_array as $user){
            $users[$user['id']]['user_id'] = $user['id'];
            $users[$user['id']]['name'] = $user['name'];
            $users[$user['id']]['task_count'] = 0;
            $users[$user['id']]['todo_count'] = 0;
        }

        $pending_tasks = $this->tasks_m->get(
            ['task_status' => 'assigned'],
            ['tasks.id', 'tasks.task_status', 'tasks.user_id', 'tasks.task_priority']
        )->result_array();

        foreach ($pending_tasks as $task){
            if (isset($users[$task['user_id']]['task_count'])){
                $users[$task['user_id']]['task_count']++;
            }else{
                $users[$task['user_id']]['task_count'] = 1;
            }
        }

        $pending_todo = $this->todo_m->get(
            ['todo_status' => 'assigned'],
            ['todo.id', 'todo.todo_status', 'todo.user_id', 'todo.todo_priority']
        )->result_array();

        foreach ($pending_todo as $todo){
            if (isset($users[$task['user_id']]['task_count'])){
                $users[$todo['user_id']]['todo_count']++;
            }else{
                $users[$todo['user_id']]['todo_count'] = 1;
            }
        }
        return $users;
    }

    public function tasks($user_id){
        $this->data['user_details'] = $this->users_m->get(['id' => $user_id])->row_array();
        $this->data['projects'] = array_column($this->projects_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['users'] = array_column($this->users_m->get(null, ['id', 'name'])->result_array(), 'name', 'id');

        $employee_data = $this->_pending_data_employee($user_id);
        $this->data['tasks'] = $employee_data['tasks'];
        $this->data['todo'] = $employee_data['todo'];
        $this->data['tasks_pending_count'] = $employee_data['tasks_pending_count'];
        $this->data['tasks_pending_due_count'] = $employee_data['tasks_pending_due_count'];
        $this->data['todo_pending_count'] = $employee_data['todo_pending_count'];
        $this->data['todo_pending_due_count'] = $employee_data['todo_pending_due_count'];
        
        // get attendance data
        $from_date = date('Y-m-01');
        $from_date = date('2023-09-01');
        
        $to_date = date('Y-m-d');

        $this->data['dates_array']      = array_reverse(get_date_array($from_date, $to_date));
        $this->data['time_log']         = $this->time_log_m->get_employee_time_log_data($user_id, $from_date, $to_date);
        $this->data['attendance_data']  = $this->attendance_m->get_employee_attendance_data($user_id, $from_date, $to_date);
        $this->data['time_log_type']    = $this->time_log_type_m->get(['status' => 1])->result_array();

        $this->data['page_title']   = 'Pending Report - Employee';
        $this->data['page_name']    = 'pending_report_employee/dashboard';
        $this->load->view('app/index', $this->data);
    }

    private function _pending_data_employee($user_id): array {
        $data['tasks'] = $this->_get_tasks($user_id);

        // calculate pending task count
        $data['tasks_pending_count'] = 0;
        $data['tasks_pending_due_count'] = 0;
        foreach ($data['tasks'] as $key => $task){
            if ($task['task_status'] == 'assigned'){
                $data['tasks_pending_count'] ++;
                if ($task['due_date'] < date('Y-m-d')){
                    $data['tasks_pending_due_count'] ++;
                }
            }
        }

        $data['todo'] = $this->_get_todo($user_id);

        // calculate pending todo count
        $data['todo_pending_count'] = 0;
        $data['todo_pending_due_count'] = 0;
        foreach ($data['todo'] as $key => $todo){
            if ($todo['todo_status'] == 'assigned'){
                $data['todo_pending_count'] ++;
                if ($todo['due_date'] < date('Y-m-d')){
                    $data['todo_pending_due_count'] ++;
                }
            }
        }

        return $data;
    }

    // get tasks
    private function _get_tasks($user_id) {
        $date_limit = date('Y-m-d', strtotime('-5 weeks'));

        $this->db->group_start();
        $this->db->where('task_status', 'assigned');
        $this->db->or_where('due_date >=', $date_limit);
        $this->db->group_end();
        $tasks = $this->tasks_m->get(['user_id' => $user_id])->result_array();

        usort($tasks, function($a, $b) {
            if ($a['task_status'] == 'assigned' && $b['task_status'] != 'assigned') {
                return -1; // $a is assigned, $b is not, so $a should come first
            } else if ($a['task_status'] != 'assigned' && $b['task_status'] == 'assigned') {
                return 1; // $a is not assigned, $b is, so $b should come first
            } else {
                // If task_status is same (either both 'assigned' or both 'not assigned'), compare dates
                $dateA = new DateTime($a['due_date']);
                $dateB = new DateTime($b['due_date']);

                // Compare dates, newer date should come first
                return $dateB <=> $dateA;
            }
        });

        return $tasks;
    }

    // get todo
    private function _get_todo($user_id) {
        $date_limit = date('Y-m-d', strtotime('-5 weeks'));

        $this->db->group_start();
        $this->db->where('todo_status', 'assigned');
        $this->db->or_where('due_date >=', $date_limit);
        $this->db->group_end();
        $todo = $this->todo_m->get(['user_id' => $user_id])->result_array();

        usort($todo, function($a, $b) {
            if ($a['todo_status'] == 'assigned' && $b['todo_status'] != 'assigned') {
                return -1; // $a is assigned, $b is not, so $a should come first
            } else if ($a['todo_status'] != 'assigned' && $b['todo_status'] == 'assigned') {
                return 1; // $a is not assigned, $b is, so $b should come first
            } else {
                // If task_status is same (either both 'assigned' or both 'not assigned'), compare dates
                $dateA = new DateTime($a['due_date']);
                $dateB = new DateTime($b['due_date']);

                // Compare dates, newer date should come first
                return $dateB <=> $dateA;
            }
        });
        return $todo;
    }

}
