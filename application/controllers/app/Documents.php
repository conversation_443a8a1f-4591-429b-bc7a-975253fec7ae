<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Documents extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('documents_m');
        $this->load->model('projects_m');
        $this->load->model('users_m');
    }

    public function index() {
        if (!empty($this->input->get('start_date')) && !empty($this->input->get('end_date'))){
            $where['date(created_on) >='] = $this->input->get('start_date');
            $where['date(created_on) <='] = $this->input->get('end_date');
        }
        if ($this->input->get('project_id') > 0){
            $where['project_id'] = $this->input->get('project_id');
        }
        $this->data['projects']     = array_column($this->projects_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        
        $this->db->order_by('updated_on', 'desc');
        $this->data['list_items']   = $this->documents_m->get($where)->result_array();
        $this->data['page_title']   = 'Documents';
        $this->data['page_name']    = 'documents/index';
        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'content' => $this->input->post('content'),
                'project_id' => $this->input->post('project_id'),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->documents_m->insert($data);
            set_alert('message_success', 'Document Added Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function edit($item_id){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'content' => $this->input->post('content'),
                'project_id' => $this->input->post('project_id'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->documents_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Document Updated Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->documents_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Document Deleted Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function edit_content($item_id) {
        $this->data['projects'] = array_column($this->projects_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['edit_data'] = $this->documents_m->get(['id' => $item_id])->row_array();
        $this->data['page_title']   = 'Edit - '. $this->data['edit_content']['content'];
        $this->data['page_name']    = 'documents/edit_content';
        $this->load->view('app/index', $this->data);
    }

    public function ajax_save_content(){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'project_id' => $this->input->post('project_id'),
                'content' => $this->input->post('content'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->documents_m->update($data, ['id' => $this->input->post('task_id')]);
            echo true;
        }
        echo false;
    }
}
