<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Time_log extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('users_m');
        $this->load->model('time_log_m');
        $this->load->model('time_log_type_m');
        $this->load->model('attendance_m');
        $this->load->model('office_attendance_m');
    }

    public function daily_log(){
        if (has_permission('time_log/daily_log') || get_user_id() == 57){
            if (empty($this->input->get('log_date'))){
                redirect('app/time_log/daily_log/?log_date='.date('Y-m-d'));
            }
            $this->data['log_date']     = $this->input->get('log_date');
            $this->data['users']        = $this->users_m->get([
                'employee_code!=' => '', 'employee_status' => 1
            ])->result_array();

            $this->data['time_log']         = $this->time_log_m->get_time_log_data($this->input->get('log_date'));
            $this->data['attendance_data']  = $this->attendance_m->get_attendance_data($this->input->get('log_date'));
            $this->data['time_log_type']    = $this->time_log_type_m->get(['status' => 1])->result_array();

            $this->data['page_title']   = 'Daily Log';
            $this->data['page_name']    = 'time_log/daily_log';
            $this->load->view('app/index', $this->data);
        }else{
            redirect('app/dashboard/index/');
        }

    }



    public function save_time_log(){
        if ($this->input->post()){
            $time_log_type = $this->time_log_type_m->get(['id' => $this->input->post('log_type_id')])->row();

            $where = [
                'log_date' => $this->input->post('log_date'),
                'log_type_id' => $this->input->post('log_type_id'),
                'user_id' => $this->input->post('user_id')
            ];
            $time_log = $this->time_log_m->get($where);

            $data = $where;
            $data['log_time'] = $this->input->post('log_time');
            $data['type'] = $time_log_type->type;
            $data['updated_by'] = get_user_id();
            $data['updated_on'] = date('Y-m-d H:i:s');

            if ($time_log->num_rows() > 0) {
                $this->time_log_m->update($data, $where);
            }else{
                $data['created_by'] = get_user_id();
                $data['created_on'] = date('Y-m-d H:i:s');
                $this->time_log_m->insert($data);
            }
            $response = ['status' => 1, 'message' => 'Success!'];
        }else{
            $response = ['status' => 0, 'message' => 'Something went wrong!'];
        }
        echo json_encode($response);
    }

    public function save_attendance(){
        if ($this->input->post()){

            $where = [
                'date' => $this->input->post('date'),
                'user_id' => $this->input->post('user_id'),
            ];
            $time_log = $this->attendance_m->get($where);

            $data = $where;

            $data['attendance'] = $this->input->post('attendance');
            if ($this->input->post('attendance')!='OF'){
                $data['off_date'] = null;
            }
            if ($this->input->post('attendance')=='P'){
                $data['remarks'] = null;
            }
            $data['updated_by'] = get_user_id();
            $data['updated_on'] = date('Y-m-d H:i:s');

            if ($time_log->num_rows() > 0) {
                $this->attendance_m->update($data, $where);
            }else{
                $data['created_by'] = get_user_id();
                $data['created_on'] = date('Y-m-d H:i:s');
                $this->attendance_m->insert($data);
            }
            $response = ['status' => 1, 'message' => 'Success!'];
        }else{
            $response = ['status' => 0, 'message' => 'Something went wrong!'];
        }
        echo json_encode($response);
    }

    public function save_remarks(){
        if ($this->input->post()){

            $where = [
                'date' => $this->input->post('date'),
                'user_id' => $this->input->post('user_id'),
            ];
            $time_log = $this->attendance_m->get($where);

            $data = $where;

            $data['remarks'] = $this->input->post('remarks');
            $data['updated_by'] = get_user_id();
            $data['updated_on'] = date('Y-m-d H:i:s');

            if ($time_log->num_rows() > 0) {
                $this->attendance_m->update($data, $where);
            }else{
                $data['created_by'] = get_user_id();
                $data['created_on'] = date('Y-m-d H:i:s');
                $this->attendance_m->insert($data);
            }
            $response = ['status' => 1, 'message' => 'Success!'];
        }else{
            $response = ['status' => 0, 'message' => 'Something went wrong!'];
        }
        echo json_encode($response);
    }

    public function save_off_date(){
        if ($this->input->post()){

            $where = [
                'date' => $this->input->post('date'),
                'user_id' => $this->input->post('user_id'),
            ];
            $time_log = $this->attendance_m->get($where);

            $data = $where;

            $data['off_date'] = $this->input->post('off_date');
            $data['updated_by'] = get_user_id();
            $data['updated_on'] = date('Y-m-d H:i:s');

            if ($time_log->num_rows() > 0) {
                $this->attendance_m->update($data, $where);
            }else{
                $data['created_by'] = get_user_id();
                $data['created_on'] = date('Y-m-d H:i:s');
                $this->attendance_m->insert($data);
            }
            $response = ['status' => 1, 'message' => 'Success!'];
        }else{
            $response = ['status' => 0, 'message' => 'Something went wrong!'];
        }
        echo json_encode($response);
    }

    public function reset_time_log(){
        if ($this->input->post()){

            // delete time log
            $where = [
                'log_date' => $this->input->post('log_date'),
                'user_id' => $this->input->post('user_id'),
            ];
            $this->time_log_m->delete($where);

            // delete attendance
            $where = [
                'date' => $this->input->post('log_date'),
                'user_id' => $this->input->post('user_id'),
            ];
            $this->attendance_m->delete($where);

            
            $response = ['status' => 1, 'message' => 'Success!'];
        }else{
            $response = ['status' => 0, 'message' => 'Something went wrong!'];
        }
        echo json_encode($response);
    }

    public function save_publish_status(){
        if ($this->input->post()){

            $office_attendance = $this->attendance_m->get(['date' => $this->input->post('date')]);

            $data['updated_by'] = get_user_id();
            $data['updated_on'] = date('Y-m-d H:i:s');

            if ($office_attendance->num_rows() > 0){
                $this->office_attendance_m->update($data, [
                    'date' => $this->input->post('date'),
                    'publish_status' => $this->input->post('publish_status'),
                ]);
            }else{
                $data['date'] = $this->input->post('date');
                $data['publish_status'] = $this->input->post('publish_status');
                $data['created_by'] = get_user_id();
                $data['created_on'] = date('Y-m-d H:i:s');

                $this->office_attendance_m->insert($data);
            }
            $response = ['status' => 1, 'message' => 'Success!'];

        }else{
            $response = ['status' => 0, 'message' => 'Something went wrong!'];
        }
        echo json_encode($response);
    }
}
