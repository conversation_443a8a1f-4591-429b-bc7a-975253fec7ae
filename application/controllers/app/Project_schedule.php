<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Project_schedule extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('projects_m');
        $this->load->model('project_schedule_m');
        $this->load->model('project_phases_m');
        $this->load->model('users_m');
    }

    public function index() {
        $project_id = $this->input->get('project_id');
        
        if (empty($project_id)) {
            return redirect(base_url('app/projects/new_projects'));
        }
        
        // Get filter dates
        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');
        
        // Build where conditions for project_schedule
        $schedule_where = ['project_id' => $project_id];
        
        if (!empty($start_date) && !empty($end_date)) {
            $schedule_where['date >='] = $start_date;
            $schedule_where['date <='] = $end_date;
        } elseif (!empty($start_date)) {
            $schedule_where['date'] = $start_date;
        } elseif (!empty($end_date)) {
            $schedule_where['date'] = $end_date;
        }
        
        // Get all phases ordered by phase_order
        $phases = $this->project_phases_m->get(null, null, ['key' => 'phase_order', 'direction' => 'asc'])->result_array();
        
        // Get scheduled phases for this project with filters
        $scheduled_phases = $this->project_schedule_m->get(['project_id' => $project_id])->result_array();
        
        // Create a lookup map for quick access to scheduled phase data
        $scheduled_map = [];
        foreach ($scheduled_phases as $scheduled) {
            $scheduled_map[$scheduled['phase_id']] = $scheduled;
        }
        
        // Combine phase data with schedule data
        $list_items = [];
        foreach ($phases as $phase) {
            $item = $phase;
            
            // Default values for non-scheduled phases
            $item['is_scheduled'] = false;
            $item['status'] = 'pending';
            $item['date'] = null;
            $item['remarks'] = '';
            $item['user_id'] = [];
            
            // If this phase is scheduled for this project
            if (isset($scheduled_map[$phase['id']])) {
                $schedule_data = $scheduled_map[$phase['id']];
                
                // Apply date filters if needed
                if ((!empty($start_date) || !empty($end_date)) && 
                    (!isset($schedule_data['date']) || empty($schedule_data['date']) ||
                     ($start_date && $schedule_data['date'] < $start_date) ||
                     ($end_date && $schedule_data['date'] > $end_date))) {
                    // Skip if doesn't match filters
                    continue;
                }
                
                // Override defaults with scheduled data
                $item['is_scheduled'] = true;
                $item['status'] = $schedule_data['status'];
                $item['date'] = $schedule_data['date'];
                $item['remarks'] = $schedule_data['remarks'];
                
                // Convert JSON user_id to array
                if (isset($schedule_data['user_id']) && !is_array($schedule_data['user_id'])) {
                    $item['user_id'] = json_decode($schedule_data['user_id'], true) ?: [];
                } else if (isset($schedule_data['user_id'])) {
                    $item['user_id'] = [$schedule_data['user_id']];
                }
                
                // Copy any other fields from schedule_data that are needed
                if (isset($schedule_data['id'])) {
                    $item['schedule_id'] = $schedule_data['id'];
                }
            }
            
            $list_items[] = $item;
        }
        
        // Sort by phase_order (should already be sorted, but just to be sure)
        usort($list_items, function($a, $b) {
            return $a['phase_order'] - $b['phase_order']; 
        });
        
        $this->data['list_items'] = $list_items;
        
        // Get projects for dropdown
        $projects = $this->projects_m->get(null, ['id', 'title'])->result_array();
        $this->data['projects'] = array_column($projects, 'title', 'id');
        
        // Get users for display
        $users = $this->users_m->get(['employee_status' => 1, 'is_employee' => 1], ['id', 'name'])->result_array();
        $this->data['users'] = array_column($users, 'name', 'id');
        $this->data['page_title'] = 'Project Schedule';
        $this->data['page_name'] = 'project_schedule/index';
        $this->load->view('app/index', $this->data);
    }

    public function index1() {
        $where = [];


        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');

        if (!empty($start_date) && !empty($end_date)){
            $where['date <='] = $start_date;
            $where['date >='] = $end_date;
        }elseif(!empty($start_date)){
            $where['date'] = $start_date;
        }elseif(!empty($end_date)){
            $where['date'] = $end_date;
        }

        if ($this->input->get('project_id')){
            $where['project_id'] = $this->input->get('project_id');
            $this->data['list_items']   = $this->project_schedule_m->get($where)->result_array();
        }else{
            return redirect(base_url('app/projects/new_projects'));
        }



        $projects = $this->projects_m->get(null, ['id', 'title'])->result_array();
        $this->data['projects']     = array_column($projects, 'title', 'id');

        $users = $this->users_m->get(['employee_status' => 1, 'is_employee' => 1], ['id', 'name'])->result_array();
        $this->data['users']     = array_column($users, 'name', 'id');

        $this->data['page_title']   = 'Project Schedule';
        $this->data['page_name']    = 'project_schedule/index';
        $this->load->view('app/index', $this->data);
    }

    /**
     * Save project schedule from the modal form
     */
    public function save_schedule() {
        $response = ['status' => 'error', 'message' => 'Invalid request'];
        
        if ($this->input->post()) {
            $project_id = $this->input->post('project_id');
            $phase_applicable = $this->input->post('phase_applicable');
            $phases_data = $this->input->post('phases');
            
            // Only proceed if we have valid project ID
            if (!empty($project_id)) {
                $this->db->trans_start();
                
                // Process each phase
                if (is_array($phase_applicable)) {
                    foreach ($phase_applicable as $phase_id => $is_applicable) {
                        $phase_id = intval($phase_id);
                        
                        // Check if this phase is already in the schedule
                        $existing = $this->project_schedule_m->get([
                            'project_id' => $project_id,
                            'phase_id' => $phase_id
                        ])->row();
                        
                        if ($is_applicable == '1') {
                            // Phase is applicable - create or update
                            $schedule_data = [
                                'project_id' => $project_id,
                                'phase_id' => $phase_id,
                                'title' => '', // Will be filled from the project phase title
                                'remarks' => isset($phases_data[$phase_id]['remarks']) ? $phases_data[$phase_id]['remarks'] : '',
                                'date' => isset($phases_data[$phase_id]['date']) ? $phases_data[$phase_id]['date'] : null,
                                'user_id' => isset($phases_data[$phase_id]['users']) ? json_encode($phases_data[$phase_id]['users']) : '[]',
                                'updated_by' => get_user_id(),
                                'updated_on' => date('Y-m-d H:i:s')
                            ];
                            
                            // Get phase title
                            $phase = $this->project_phases_m->get(['id' => $phase_id])->row_array();
                            if ($phase) {
                                $schedule_data['title'] = $phase['title'];
                            }
                            
                            if ($existing) {
                                // Update existing schedule record
                                $this->project_schedule_m->update($schedule_data, [
                                    'id' => $existing->id
                                ]);
                            } else {
                                // Insert new schedule record
                                $schedule_data['created_by'] = get_user_id();
                                $schedule_data['created_on'] = date('Y-m-d H:i:s');
                                $this->project_schedule_m->insert($schedule_data);
                            }
                        } else {
                            // Phase is not applicable - delete if exists
                            if ($existing) {
                                $this->project_schedule_m->delete(['id' => $existing->id]);
                            }
                        }
                    }
                }
                
                $this->db->trans_complete();
                
                if ($this->db->trans_status() === FALSE) {
                    $response = ['status' => 'error', 'message' => 'Database error occurred while saving schedule'];
                } else {
                    $response = ['status' => 'success', 'message' => 'Project schedule saved successfully'];
                }
            }
        }
        
        echo json_encode($response);
        exit;
    }

    /**
     * Update a single schedule item (Ajax)
     */
    public function update_schedule_item($item_id) {
        $response = ['status' => 'error', 'message' => 'Failed to update schedule item'];
        
        if ($this->input->post()) {
            // Verify the item exists
            $schedule_item = $this->project_schedule_m->get(['id' => $item_id])->row_array();
            
            if (!$schedule_item) {
                $response['message'] = 'Schedule item not found';
            } else {
                $data = [
                    'status' => $this->input->post('status'),
                    'remarks' => $this->input->post('remarks'),
                    'updated_by' => get_user_id(),
                    'updated_on' => date('Y-m-d H:i:s')
                ];
                
                // Process user IDs
                $user_ids = $this->input->post('user_id');
                if ($user_ids) {
                    $data['user_id'] = json_encode($user_ids);
                } else {
                    $data['user_id'] = null;
                }
                
                // Update the record
                $update_success = $this->project_schedule_m->update($data, ['id' => $item_id]);
                
                if ($update_success) {
                    $response = [
                        'status' => 'success',
                        'message' => 'Schedule updated successfully'
                    ];
                }
            }
        }
        
        // Return JSON response
        echo json_encode($response);
        exit;
    }
}
