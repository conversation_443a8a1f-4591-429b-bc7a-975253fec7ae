<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: PMS
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: https://trogonmedia.com
 */

class Tickets extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('tickets_m');
        $this->load->model('projects_m');
        $this->load->model('users_m');
    }

    /**
     * Display tickets listing page
     */
    public function index() {
        // Load performance configuration
        $this->load->config('tickets_performance');

        // Get active users ordered by name
        $users = $this->users_m->get(['employee_status' => 1, 'work_assign' => 1], ['id', 'name'], ['key' => 'name', 'direction' => 'asc'])->result_array();
        $this->data['users'] = array_column($users, 'name', 'id');

        // Get projects ordered by title
        $projects = $this->projects_m->get(null, ['id', 'title'], ['key' => 'title', 'direction' => 'asc'])->result_array();
        $this->data['projects'] = array_column($projects, 'title', 'id');

        // Get total count for display and performance decision
        $this->data['total_tickets'] = $this->tickets_m->get_total_tickets();

        // Get tickets overview for different time periods
        $this->data['overview'] = $this->tickets_m->get_tickets_overview();

        // Use optimized status counting for large datasets (kept for backward compatibility)
        $this->data['status_count'] = $this->tickets_m->get_status_count();
        $this->data['priority_count'] = $this->tickets_m->get_priority_count();

        // Determine which view to use based on ticket count and configuration
        $use_optimized = $this->config->item('tickets_use_optimized_view');
        $auto_threshold = $this->config->item('tickets_performance')['auto_optimize_threshold'];

        if ($use_optimized || $this->data['total_tickets'] > $auto_threshold) {
            // Use optimized DataTables view for large datasets
            $this->data['page_name'] = 'tickets/index_optimized';
            $this->data['page_title'] = 'Tickets (Optimized)';
        } else {
            // Use original view for smaller datasets - get latest tickets first
            $this->data['list_items'] = $this->tickets_m->get_ticket_list(null, null, null);
            $this->data['page_name'] = 'tickets/index';
            $this->data['page_title'] = 'Tickets';
        }

        $this->load->view('app/index', $this->data);
    }

    /**
     * AJAX endpoint for DataTables (optimized for large datasets)
     */
    public function ajax_list() {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        $request_data = $this->input->post();
        $result = $this->tickets_m->get_tickets_datatable($request_data);

        // Format data for DataTables
        $data = [];
        foreach ($result['data'] as $key => $item) {
            $row = [];

            // Ticket ID in TK-{id} format with job_id_dashboard class
            $ticket_id_formatted = '<div class="mb-1 job_id_dashboard">TK-<b>' . $item['id'] . '</b></div>';
            $row[] = $ticket_id_formatted;

            // Project title above ticket title with date (combined column)
            $project_title = $item['project_title'] ?? 'N/A';
            $project_formatted = '';
            if ($project_title !== 'N/A') {
                $project_formatted = '<span style="background-color:#efefef;font-size: 13px;border-left: 2px solid #999;color: blueviolet;padding-left: 5px">' .
                                   strtoupper(htmlspecialchars($project_title)) . '</span><br>';
            }

            // Title with task styling, ticket date, and reported by (clickable)
            $ticket_date = (!empty($item['ticket_date']) && $item['ticket_date'] !== '0000-00-00 00:00:00') ?
                          date('d-m-Y', strtotime($item['ticket_date'])) : 'N/A';
            $reported_by = $item['reported_by'] ?? 'N/A';

            $title = $project_formatted .
                    '<div class="p-2 text-muted" style="font-size: 16px!important; font-weight: bold; background-color: rgba(250,247,250,0.81); border-radius: 2px;">' .
                    '<a href="' . base_url('app/tickets/view/' . $item['id']) . '" class="text-decoration-none text-dark" style="font-weight: bold;">' .
                    htmlspecialchars($item['title']) .
                    '</a>' .
                    '<div class="d-inline-block float-right" style="font-size: 12px; color: #666;">' .
                    '<i class="fas fa-calendar"></i> ' . $ticket_date .
                    '</div>' .
                    '</div>' .
                    '<div class="mt-1">' .
                    '<small class="text-muted">Reported by: <strong>' . htmlspecialchars($reported_by) . '</strong></small>' .
                    '</div>';
            $row[] = $title;

            // Combined Details column (Priority, Assigned To)
            $details = '<div class="details-container">';

            // Priority
            $details .= '<div class="mb-2">';
            $details .= '<small class="text-muted">Priority:</small><br>';
            $priority_badge = '';
            switch($item['priority']) {
                case 'critical':
                    $priority_badge = '<span class="badge badge-danger">Critical</span>';
                    break;
                case 'high':
                    $priority_badge = '<span class="badge badge-warning">High</span>';
                    break;
                case 'medium':
                    $priority_badge = '<span class="badge badge-info">Medium</span>';
                    break;
                case 'low':
                    $priority_badge = '<span class="badge badge-secondary">Low</span>';
                    break;
                default:
                    $priority_badge = '<span class="badge badge-light">' . ucfirst($item['priority']) . '</span>';
            }
            $details .= $priority_badge;
            $details .= '</div>';

            // Assigned To
            $details .= '<div>';
            $details .= '<small class="text-muted">Assigned:</small><br>';
            $assigned_user = $item['assigned_user_name'] ?? null;
            if (!empty($assigned_user)) {
                $details .= '<span class="badge bg-light text-dark border">';
                $details .= '<i class="fas fa-user me-2"></i> ' . htmlspecialchars($assigned_user);
                $details .= '</span>';
            } else {
                $details .= '<span class="text-muted fst-italic">Unassigned</span>';
            }
            $details .= '</div>';

            $details .= '</div>';
            $row[] = $details;

            // Status badge with larger font size
            $status_badge = '';
            switch($item['status']) {
                case 'new':
                    $status_badge = '<span class="badge badge-info" style="font-size: 13px; padding: 6px 12px;">NEW</span>';
                    break;
                case 'assigned':
                    $status_badge = '<span class="badge badge-warning" style="font-size: 13px; padding: 6px 12px;">ASSIGNED</span>';
                    break;
                case 'closed':
                    $status_badge = '<span class="badge badge-success" style="font-size: 13px; padding: 6px 12px;">CLOSED</span>';
                    break;
                case 'on_hold':
                    $status_badge = '<span class="badge badge-secondary" style="font-size: 13px; padding: 6px 12px;">ON HOLD</span>';
                    break;
                case 're_open':
                    $status_badge = '<span class="badge badge-danger" style="font-size: 13px; padding: 6px 12px;">RE-OPEN</span>';
                    break;
                default:
                    $status_badge = '<span class="badge badge-light" style="font-size: 13px; padding: 6px 12px;">UNKNOWN</span>';
            }
            $row[] = $status_badge;

            // Action buttons aligned vertically to save space
            $actions = '<div class="btn-group-vertical" role="group">';

            $actions .= '<a href="' . base_url('app/tickets/view/' . $item['id']) . '" class="btn btn-outline-success btn-sm mb-1" title="View Details" style="width: 35px; height: 28px;">
                           <i class="fas fa-eye"></i>
                       </a>';

            // Show "Assign Developer" button if ticket is unassigned
            if (empty($item['user_id']) || $item['user_id'] == 0) {
                $actions .= '<button onclick="show_ajax_modal(\'' . site_url('app/modal/popup/get/' . $item['id'] . '/?page_name=tickets/quick_actions') . '\', \'Assign Developer\')"
                                    class="btn btn-outline-info btn-sm mb-1" title="Assign Developer" style="width: 35px; height: 28px;">
                                <i class="fas fa-user-plus"></i>
                            </button>';
            }

            $actions .= '<button onclick="show_ajax_modal(\'' . site_url('app/modal/popup/get/' . $item['id'] . '/?page_name=tickets/quick_actions') . '\', \'Quick Actions\')"
                                class="btn btn-outline-warning btn-sm mb-1" title="Quick Actions" style="width: 35px; height: 28px;">
                            <i class="fas fa-bolt"></i>
                        </button>';

            if (has_permission('tickets/edit')) {
                $actions .= '<button onclick="show_large_modal(\'' . site_url('app/modal/popup/get/' . $item['id'] . '/?page_name=tickets/edit') . '\', \'Edit Ticket\')"
                                    class="btn btn-outline-primary btn-sm mb-1" title="Edit" style="width: 35px; height: 28px;">
                                <i class="fas fa-pencil-alt"></i>
                            </button>';
            }

            if (has_permission('tickets/delete')) {
                $actions .= '<button onclick="confirm_modal(\'' . base_url("app/tickets/delete/{$item['id']}/") . '\')"
                                    class="btn btn-outline-danger btn-sm" title="Delete" style="width: 35px; height: 28px;">
                                <i class="fas fa-trash"></i>
                            </button>';
            }

            $actions .= '</div>';
            $row[] = $actions;
            $data[] = $row;
        }

        $response = [
            'draw' => intval($request_data['draw']),
            'recordsTotal' => $result['recordsTotal'],
            'recordsFiltered' => $result['recordsFiltered'],
            'data' => $data
        ];

        echo json_encode($response);
    }

    /**
     * Performance comparison page
     */
    public function performance() {
        $this->data['total_tickets'] = $this->tickets_m->get_total_tickets();
        $this->data['page_title'] = 'Performance Analysis';
        $this->data['page_name'] = 'tickets/performance_comparison';
        $this->load->view('app/index', $this->data);
    }

    /**
     * Debug method to check ticket counts
     */
    public function debug_count() {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        $total_tickets = $this->tickets_m->get_total_tickets();

        // Test with empty request data
        $test_request = [
            'draw' => 1,
            'start' => 0,
            'length' => 25,
            'search' => ['value' => ''],
            'order' => [['column' => 0, 'dir' => 'desc']]
        ];

        $result = $this->tickets_m->get_tickets_datatable($test_request);

        $debug_info = [
            'total_tickets_direct' => $total_tickets,
            'datatable_total' => $result['recordsTotal'],
            'datatable_filtered' => $result['recordsFiltered'],
            'data_count' => count($result['data']),
            'first_ticket_id' => !empty($result['data']) ? $result['data'][0]['id'] : 'No data',
            'last_ticket_id' => !empty($result['data']) ? end($result['data'])['id'] : 'No data'
        ];

        echo json_encode($debug_info);
    }



    /**
     * Add new ticket
     */
    public function add(){
        if ($this->input->post()){
            $files = $this->input->post('files') ?? [];
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'files' => json_encode($files),
                'project_id' => $this->input->post('project_id'),
                'status' => $this->input->post('status') ?? 'new',
                'priority' => $this->input->post('priority'),
                'estimated_time' => $this->input->post('estimated_time') ?: NULL,
                'type' => $this->input->post('type'),
                'ticket_via' => $this->input->post('ticket_via'),
                'ticket_date' => date('Y-m-d H:i:s'),
                'reported_to' => $this->input->post('reported_to'),
                'reported_by' => $this->input->post('reported_by'),
                'user_id' => $this->input->post('user_id') ?: NULL,
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            
            $ticket_id = $this->tickets_m->insert($data);

            // Add initial history entry
            $this->load->model('ticket_history_m');
            $history_data = [
                'ticket_id' => $ticket_id,
                'user_id' => get_user_id(),
                'remarks' => 'Ticket created',
                'files' => json_encode([]),
                'status' => $data['status'],
                'created_at' => date('Y-m-d H:i:s')
            ];
            $this->ticket_history_m->insert($history_data);

            // Check if request is AJAX
            if ($this->input->is_ajax_request()) {
                $response = [
                    'status' => 'success',
                    'message' => 'Ticket Added Successfully!',
                    'ticket_id' => $ticket_id,
                    'redirect' => false
                ];
                echo json_encode($response);
                exit;
            } else {
                set_alert('message_success', 'Ticket Added Successfully!');
                redirect($_SERVER['HTTP_REFERER']);
            }
        }
        
        // If not POST request, redirect
        if (!$this->input->post()) {
            redirect($_SERVER['HTTP_REFERER']);
        }
    }

    /**
     * AJAX method to add ticket
     */
    public function ajax_add(){
        $response = ['status' => 'error', 'message' => 'Invalid request'];
        
        if ($this->input->post() && $this->input->is_ajax_request()){
            // Validation
            $this->load->library('form_validation');
            $this->form_validation->set_rules('title', 'Title', 'required|trim');
            $this->form_validation->set_rules('project_id', 'Project', 'required|numeric');
            $this->form_validation->set_rules('type', 'Type', 'required');
            $this->form_validation->set_rules('priority', 'Priority', 'required');
            
            if ($this->form_validation->run() == FALSE) {
                $response = [
                    'status' => 'error',
                    'message' => validation_errors()
                ];
            } else {
                try {
                    $files = $this->input->post('files') ?? [];
                    $data = [
                        'title' => $this->input->post('title'),
                        'description' => $this->input->post('description'),
                        'files' => json_encode($files),
                        'project_id' => $this->input->post('project_id'),
                        'status' => $this->input->post('status') ?? 'new',
                        'priority' => $this->input->post('priority'),
                        'estimated_time' => $this->input->post('estimated_time') ?: NULL,
                        'type' => $this->input->post('type'),
                        'ticket_via' => $this->input->post('ticket_via'),
                        'ticket_date' => date('Y-m-d H:i:s'),
                        'reported_to' => $this->input->post('reported_to'),
                        'reported_by' => $this->input->post('reported_by'),
                        'user_id' => $this->input->post('user_id') ?: NULL,
                        'created_by' => get_user_id(),
                        'updated_by' => get_user_id(),
                        'created_on' => date('Y-m-d H:i:s'),
                        'updated_on' => date('Y-m-d H:i:s'),
                    ];
                    
                    $ticket_id = $this->tickets_m->insert($data);

                    // Add initial history entry
                    $this->load->model('ticket_history_m');
                    $history_data = [
                        'ticket_id' => $ticket_id,
                        'user_id' => get_user_id(),
                        'remarks' => 'Ticket created',
                        'files' => json_encode([]),
                        'status' => $data['status'],
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    $this->ticket_history_m->insert($history_data);

                    $response = [
                        'status' => 'success',
                        'message' => 'Ticket added successfully!',
                        'ticket_id' => $ticket_id,
                        'data' => $data
                    ];
                } catch (Exception $e) {
                    $response = [
                        'status' => 'error',
                        'message' => 'Failed to add ticket: ' . $e->getMessage()
                    ];
                }
            }
        }
        
        echo json_encode($response);
        exit;
    }

    /**
     * Edit existing ticket
     */
    public function edit($item_id){
        if ($this->input->post()){
            $files = $this->input->post('files') ?? [];
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'files' => json_encode($files),
                'project_id' => $this->input->post('project_id'),
                'status' => $this->input->post('status'),
                'priority' => $this->input->post('priority'),
                'estimated_time' => $this->input->post('estimated_time') ?: NULL,
                'type' => $this->input->post('type'),
                'ticket_via' => $this->input->post('ticket_via'),
                'ticket_date' => $this->input->post('ticket_date'),
                'reported_to' => $this->input->post('reported_to'),
                'reported_by' => $this->input->post('reported_by'),
                'user_id' => $this->input->post('user_id'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];

            // Set close date if status is closed
            if ($this->input->post('status') == 'closed') {
                $data['close_date'] = date('Y-m-d H:i:s');
                $data['closed_by'] = get_user_id();
            } elseif ($this->input->post('status') != 'closed' && $current_ticket && $current_ticket['status'] == 'closed') {
                // If changing from closed to another status, clear close date
                $data['close_date'] = NULL;
                $data['closed_by'] = NULL;
            }

            // Get current ticket data before update for comparison
            $current_ticket = $this->tickets_m->get_ticket_list(['tickets.id' => $item_id])[0] ?? null;

            $this->tickets_m->update($data, ['id' => $item_id]);

            // Add history entry for ticket edit
            $this->load->model('ticket_history_m');

            // Check if status was changed
            if ($current_ticket && $current_ticket['status'] !== $this->input->post('status')) {
                $status_labels = [
                    'new' => 'New',
                    'assigned' => 'Assigned',
                    'closed' => 'Closed',
                    'on_hold' => 'On Hold',
                    're_open' => 'Re-opened'
                ];

                $new_status_label = $status_labels[$this->input->post('status')] ?? $this->input->post('status');
                $remarks = "Updated ticket status to '{$new_status_label}'";

                // Check if user was assigned
                if ($this->input->post('user_id') && $current_ticket['user_id'] !== $this->input->post('user_id')) {
                    $assigned_user = $this->users_m->get(['id' => $this->input->post('user_id')])->row();
                    if ($assigned_user) {
                        $remarks .= " and assigned to {$assigned_user->name}";
                    }
                }
            } else {
                $remarks = 'Ticket details updated';

                // Check if only user assignment changed
                if ($current_ticket && $this->input->post('user_id') && $current_ticket['user_id'] !== $this->input->post('user_id')) {
                    $assigned_user = $this->users_m->get(['id' => $this->input->post('user_id')])->row();
                    if ($assigned_user) {
                        $remarks = "Assigned ticket to {$assigned_user->name}";
                    }
                }
            }

            $history_data = [
                'ticket_id' => $item_id,
                'user_id' => get_user_id(),
                'remarks' => $remarks,
                'files' => json_encode([]),
                'status' => $this->input->post('status'),
                'created_at' => date('Y-m-d H:i:s')
            ];
            $this->ticket_history_m->insert($history_data);

            set_alert('message_success', 'Ticket Updated Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    /**
     * Delete ticket
     */
    public function delete($item_id){
        if ($item_id > 0){
            $this->tickets_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Ticket Deleted Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    /**
     * Change ticket status
     */
    public function change_status($item_id){
        if ($this->input->post()){
            // Get current ticket data to compare status
            $current_ticket = $this->tickets_m->get_ticket_list(['tickets.id' => $item_id])[0] ?? null;
            $new_status = $this->input->post('status');

            $data = [
                'status' => $new_status,
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];

            // Set close date if status is closed
            if ($new_status == 'closed') {
                $data['close_date'] = date('Y-m-d H:i:s');
                $data['closed_by'] = get_user_id();
            } elseif ($new_status != 'closed' && $current_ticket && $current_ticket['status'] == 'closed') {
                // If changing from closed to another status, clear close date
                $data['close_date'] = NULL;
                $data['closed_by'] = NULL;
            }

            $this->tickets_m->update($data, ['id' => $item_id]);

            // Add history entry for status change
            if ($current_ticket && $current_ticket['status'] !== $new_status) {
                $this->load->model('ticket_history_m');

                $status_labels = [
                    'new' => 'New',
                    'assigned' => 'Assigned',
                    'closed' => 'Closed',
                    'on_hold' => 'On Hold',
                    're_open' => 'Re-opened'
                ];

                $old_status_label = $status_labels[$current_ticket['status']] ?? $current_ticket['status'];
                $new_status_label = $status_labels[$new_status] ?? $new_status;

                $history_data = [
                    'ticket_id' => $item_id,
                    'user_id' => get_user_id(),
                    'remarks' => "Updated ticket status to '{$new_status_label}'",
                    'files' => json_encode([]),
                    'status' => $new_status,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $this->ticket_history_m->insert($history_data);
            }

            set_alert('message_success', 'Ticket Status Updated Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    /**
     * View ticket details
     */
    public function view($item_id) {
        $this->data['ticket'] = $this->tickets_m->get_ticket_list(['tickets.id' => $item_id])[0] ?? null;

        if (!$this->data['ticket']) {
            show_404();
        }

        // Load ticket history model and get history
        $this->load->model('ticket_history_m');
        $this->data['ticket_history'] = $this->ticket_history_m->get_ticket_history($item_id);

        $this->data['users'] = array_column($this->users_m->get()->result_array(), 'name', 'id');
        $this->data['page_title'] = 'Ticket Details';
        $this->data['page_name'] = 'tickets/view';
        $this->load->view('app/index', $this->data);
    }

    /**
     * Display ticket reports and statistics
     */
    public function report() {
        $tickets = $this->tickets_m->get_ticket_list();
        $this->data['status_count'] = $this->tickets_m->get_status_count($tickets);
        $this->data['priority_count'] = $this->tickets_m->get_priority_count($tickets);
        
        // Get recent tickets (last 10)
        $this->data['recent_tickets'] = array_slice($tickets, 0, 10);
        
        $this->data['page_title'] = 'Ticket Reports';
        $this->data['page_name'] = 'tickets/report';
        $this->load->view('app/index', $this->data);
    }

    /**
     * Add comment to ticket history
     */
    public function add_comment($ticket_id) {
        if ($this->input->post() && $this->input->is_ajax_request()) {
            $this->load->library('form_validation');
            $this->form_validation->set_rules('remarks', 'Comment', 'required|trim');

            // If status update is requested, validate the new status
            if ($this->input->post('update_status')) {
                $this->form_validation->set_rules('new_status', 'New Status', 'required');
            }

            if ($this->form_validation->run() == FALSE) {
                $response = [
                    'status' => 'error',
                    'message' => validation_errors()
                ];
            } else {
                try {
                    $this->load->model('ticket_history_m');

                    // Get current ticket data for status comparison
                    $current_ticket = $this->tickets_m->get_ticket_list(['tickets.id' => $ticket_id])[0] ?? null;
                    if (!$current_ticket) {
                        throw new Exception('Ticket not found');
                    }

                    $update_status = $this->input->post('update_status');
                    $new_status = $this->input->post('new_status');
                    $comment_text = $this->input->post('remarks');

                    // Start database transaction
                    $this->db->trans_start();

                    // Add the comment to history
                    $files = $this->input->post('files') ?? [];
                    $comment_data = [
                        'ticket_id' => $ticket_id,
                        'user_id' => get_user_id(),
                        'remarks' => $comment_text,
                        'files' => json_encode($files),
                        'status' => $current_ticket['status'], // Always record current status
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    $this->ticket_history_m->insert($comment_data);

                    $success_message = 'Comment added successfully!';

                    // Update status if requested
                    if ($update_status && $new_status && $new_status !== $current_ticket['status']) {
                        // Update ticket status in tickets table
                        $ticket_update_data = [
                            'status' => $new_status,
                            'updated_by' => get_user_id(),
                            'updated_on' => date('Y-m-d H:i:s'),
                        ];

                        // Set close date if status is closed
                        if ($new_status == 'closed') {
                            $ticket_update_data['close_date'] = date('Y-m-d');
                            $ticket_update_data['closed_by'] = get_user_id();
                        }

                        $this->tickets_m->update($ticket_update_data, ['id' => $ticket_id]);

                        // Add status change to history
                        $status_labels = [
                            'new' => 'New',
                            'assigned' => 'Assigned',
                            'closed' => 'Closed',
                            'on_hold' => 'On Hold',
                            're_open' => 'Re-opened'
                        ];

                        $old_status_label = $status_labels[$current_ticket['status']] ?? $current_ticket['status'];
                        $new_status_label = $status_labels[$new_status] ?? $new_status;

                        $status_history_data = [
                            'ticket_id' => $ticket_id,
                            'user_id' => get_user_id(),
                            'remarks' => "Status changed from '{$old_status_label}' to '{$new_status_label}'",
                            'files' => json_encode([]),
                            'status' => $new_status, // Record the new status
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                        $this->ticket_history_m->insert($status_history_data);

                        $success_message = 'Comment added and status updated successfully!';
                    }

                    // Complete transaction
                    $this->db->trans_complete();

                    if ($this->db->trans_status() === FALSE) {
                        throw new Exception('Database transaction failed');
                    }

                    $response = [
                        'status' => 'success',
                        'message' => $success_message
                    ];
                } catch (Exception $e) {
                    // Rollback transaction on error
                    $this->db->trans_rollback();
                    $response = [
                        'status' => 'error',
                        'message' => 'Failed to process request: ' . $e->getMessage()
                    ];
                }
            }

            echo json_encode($response);
            return;
        }

        show_404();
    }

    /**
     * AJAX method for quick actions
     */
    public function quick_action() {
        $response = ['status' => 'error', 'message' => 'Invalid request'];
        
        if ($this->input->post() && $this->input->is_ajax_request()) {
            $action = $this->input->post('action');
            $ticket_id = $this->input->post('ticket_id');
            
            if (!$ticket_id || !$action) {
                $response = ['status' => 'error', 'message' => 'Missing required parameters'];
                echo json_encode($response);
                exit;
            }
            
            try {
                switch ($action) {
                    case 'update_status':
                        $status = $this->input->post('status');
                        if (!$status) {
                            throw new Exception('Status is required');
                        }

                        // Get current ticket for comparison
                        $current_ticket = $this->tickets_m->get_ticket_list(['tickets.id' => $ticket_id])[0] ?? null;
                        if (!$current_ticket) {
                            throw new Exception('Ticket not found');
                        }

                        $data = [
                            'status' => $status,
                            'updated_by' => get_user_id(),
                            'updated_on' => date('Y-m-d H:i:s')
                        ];

                        // Set close date if status is closed
                        if ($status == 'closed') {
                            $data['close_date'] = date('Y-m-d H:i:s');
                            $data['closed_by'] = get_user_id();
                        } elseif ($status != 'closed' && $current_ticket && $current_ticket['status'] == 'closed') {
                            // If changing from closed to another status, clear close date
                            $data['close_date'] = NULL;
                            $data['closed_by'] = NULL;
                        }

                        $this->tickets_m->update($data, ['id' => $ticket_id]);

                        // Add history entry if status changed
                        if ($current_ticket['status'] !== $status) {
                            $this->load->model('ticket_history_m');
                            $status_labels = [
                                'new' => 'New',
                                'assigned' => 'Assigned',
                                'closed' => 'Closed',
                                'on_hold' => 'On Hold',
                                're_open' => 'Re-opened'
                            ];

                            $new_status_label = $status_labels[$status] ?? $status;

                            $history_data = [
                                'ticket_id' => $ticket_id,
                                'user_id' => get_user_id(),
                                'remarks' => "Updated ticket status to '{$new_status_label}'",
                                'files' => json_encode([]),
                                'status' => $status,
                                'created_at' => date('Y-m-d H:i:s')
                            ];
                            $this->ticket_history_m->insert($history_data);
                        }

                        $response = ['status' => 'success', 'message' => 'Status updated successfully'];
                        break;
                        
                    case 'assign_user':
                        $user_id = $this->input->post('user_id');
                        if (!$user_id) {
                            throw new Exception('User ID is required');
                        }

                        // Get user name for history
                        $assigned_user = $this->users_m->get(['id' => $user_id])->row();
                        if (!$assigned_user) {
                            throw new Exception('User not found');
                        }

                        $data = [
                            'user_id' => $user_id,
                            'status' => 'assigned',
                            'updated_by' => get_user_id(),
                            'updated_on' => date('Y-m-d H:i:s')
                        ];

                        $this->tickets_m->update($data, ['id' => $ticket_id]);

                        // Add history entry
                        $this->load->model('ticket_history_m');
                        $history_data = [
                            'ticket_id' => $ticket_id,
                            'user_id' => get_user_id(),
                            'remarks' => "Assigned ticket to {$assigned_user->name}",
                            'files' => json_encode([]),
                            'status' => 'assigned',
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                        $this->ticket_history_m->insert($history_data);

                        $response = ['status' => 'success', 'message' => 'User assigned successfully'];
                        break;
                        
                    case 'update_priority':
                        $priority = $this->input->post('priority');
                        if (!$priority) {
                            throw new Exception('Priority is required');
                        }

                        // Get current ticket for status
                        $current_ticket = $this->tickets_m->get_ticket_list(['tickets.id' => $ticket_id])[0] ?? null;
                        if (!$current_ticket) {
                            throw new Exception('Ticket not found');
                        }

                        $data = [
                            'priority' => $priority,
                            'updated_by' => get_user_id(),
                            'updated_on' => date('Y-m-d H:i:s')
                        ];

                        $this->tickets_m->update($data, ['id' => $ticket_id]);

                        // Add history entry
                        $this->load->model('ticket_history_m');
                        $priority_labels = [
                            'low' => 'Low',
                            'medium' => 'Medium',
                            'high' => 'High',
                            'urgent' => 'Urgent'
                        ];
                        $priority_label = $priority_labels[$priority] ?? $priority;

                        $history_data = [
                            'ticket_id' => $ticket_id,
                            'user_id' => get_user_id(),
                            'remarks' => "Updated ticket priority to '{$priority_label}'",
                            'files' => json_encode([]),
                            'status' => $current_ticket['status'],
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                        $this->ticket_history_m->insert($history_data);

                        $response = ['status' => 'success', 'message' => 'Priority updated successfully'];
                        break;
                        
                    case 'delete':
                        if (has_permission('tickets/delete')) {
                            $this->tickets_m->delete(['id' => $ticket_id]);
                            $response = ['status' => 'success', 'message' => 'Ticket deleted successfully'];
                        } else {
                            throw new Exception('You do not have permission to delete tickets');
                        }
                        break;
                        
                    default:
                        throw new Exception('Invalid action');
                }
                
            } catch (Exception $e) {
                $response = ['status' => 'error', 'message' => $e->getMessage()];
            }
        }
        
        echo json_encode($response);
        exit;
    }
}