<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Users extends App_Controller{
    public function __construct () {
        parent::__construct();
        if(!has_permission('users/index')){
            redirect('app/dashboard/index');
        }
        $this->load->model('users_m');
        $this->load->model('roles_m');
    }

    public function index() {
        // Get filter parameters
        $status = $this->input->get('status', TRUE) ?? '1';
        $role = $this->input->get('role', TRUE) ?? 'all';

        // Build where conditions
        $where = ['status!=' => 0];
        if ($status !== 'all') {
            $where['employee_status'] = $status;
        }
        if ($role !== 'all' && is_numeric($role)) {
            $where['role_id'] = $role;
        }

        // Get data
        $this->data['roles'] = array_column($this->roles_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['list_items'] = $this->users_m->get($where)->result_array();
        $this->data['page_title'] = 'Users';
        $this->data['page_name'] = 'users/index';
        $this->data['current_status'] = $status;
        $this->data['current_role'] = $role;

        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $data = [
                'name' => $this->input->post('name'),
                'designation' => $this->input->post('designation'),
                'phone' => $this->input->post('phone'),
                'email' => $this->input->post('email'),
                'role_id' => $this->input->post('role_id'),
                'employee_code' => $this->input->post('employee_code'),
                'address' => $this->input->post('address'),
                'secondary_phone' => $this->input->post('secondary_phone'),
                'parent_name' => $this->input->post('parent_name'),
                'parent_phone' => $this->input->post('parent_phone'),
                'dob' => $this->input->post('dob'),
                'join_date' => $this->input->post('join_date'),
                'leave_date' => $this->input->post('leave_date'),
                'id_card_no' => $this->input->post('id_card_no'),
                'aadhaar_no' => $this->input->post('aadhaar_no'),
                'hid_number' => $this->input->post('hid_number'),
                'password' => sha1($this->input->post('password')),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->users_m->insert($data);
            set_alert('message_success', 'User Added Successfully!');
        }
        redirect('app/users/index');
    }

    public function edit($item_id){
        if ($this->input->post()){
            $data = [
                'name' => $this->input->post('name'),
                'designation' => $this->input->post('designation'),
                'phone' => $this->input->post('phone'),
                'email' => $this->input->post('email'),
                'role_id' => $this->input->post('role_id'),
                'employee_code' => $this->input->post('employee_code'),
                'address' => $this->input->post('address'),
                'secondary_phone' => $this->input->post('secondary_phone'),
                'parent_name' => $this->input->post('parent_name'),
                'parent_phone' => $this->input->post('parent_phone'),
                'dob' => $this->input->post('dob'),
                'join_date' => $this->input->post('join_date'),
                'leave_date' => $this->input->post('leave_date'),
                'id_card_no' => $this->input->post('id_card_no'),
                'aadhaar_no' => $this->input->post('aadhaar_no'),
                'hid_number' => $this->input->post('hid_number'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            
            $this->users_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'User Updated Successfully!');
        }
        redirect('app/users/index');
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->users_m->delete(['id' => $item_id]);
            set_alert('message_success', 'User Deleted Successfully!');
        }
        redirect('app/users/index');
    }

    // check if email is unique
    public function check_email_duplication() {
        $response = ['status' => 0, 'message' => 'Something went wrong!'];
        if($this->input->post('email')) {
            $user_id = $this->input->post('user_id');
            if(intval($user_id) > 0) {
                $user = $this->users_m->get(['id!=' => $user_id, 'email' => $this->input->post('email')]);
            } else {
                $user = $this->users_m->get(['email' => $this->input->post('email')]);
            }
            if ($user->num_rows() > 0){
                $response = ['status' => 0, 'message' => 'Email is already used!'];
            }else{
                $response = ['status' => 1, 'message' => 'Success'];
            }
        }
        echo json_encode($response);
    }

    // check if phone is unique
    public function check_phone_duplication() {
        $response = ['status' => 0, 'message' => 'Something went wrong!'];
        if($this->input->post('phone')) {
            $user_id = $this->input->post('user_id');
            if(intval($user_id) > 0) {
                $user = $this->users_m->get(['id!=' => $user_id, 'phone' => $this->input->post('phone')]);
            } else {
                $user = $this->users_m->get(['phone' => $this->input->post('phone')]);
            }
            if ($user->num_rows() > 0){
                $response = ['status' => 0, 'message' => 'Phone number is already used!'];
            }else{
                $response = ['status' => 1, 'message' => 'Success'];
            }
        }
        echo json_encode($response);
    }

    public function reset_password($item_id){
        if (has_permission('users/index') && $this->input->post()){
            $this->users_m->update(['password' => sha1($this->input->post('password'))], ['id' => $item_id]);
            set_alert('message_success', 'Password changed successfully!');
            redirect('app/users/index');
        }else{
            set_alert('message_error', 'Permission Denied!');
            redirect('app/dashboard/index');
        }
    }

    /**
     * Handle cropped image upload
     */
    public function upload_cropped_image() {
        // Check if user has permission
        if (!has_permission('users/index')) {
            echo json_encode(['success' => false, 'message' => 'Permission denied']);
            return;
        }

        // Use the global upload_file method
        $photo = $this->upload_file('employee_photo', 'photo');
        $item_id = $this->input->post('item_id');
        log_message('error', print_r($photo, true));

        if ($photo) {
            $data['photo'] = $photo['file'];
            $this->users_m->update($data, ['id' => $item_id]);
            // Return success response
            echo json_encode([
                'success' => true,
                'file_path' => $photo['file'],
                'message' => 'Image uploaded successfully'
            ]);
        } else {
            // Return error response
            echo json_encode([
                'success' => false,
                'message' => 'Failed to upload image'
            ]);
        }
    }
}
