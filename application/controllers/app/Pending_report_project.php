<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Pending_report_project extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('tasks_m');
        $this->load->model('projects_m');
    }

    public function index() {
        $where = [];
        $this->data['list_items']   = $this->_pending_data();
        $this->data['page_title']   = 'Pending Report - Projects';
        $this->data['page_name']    = 'pending_report_project/index';
        $this->load->view('app/index', $this->data);
    }

    private function _pending_data(){
        ini_set('display_errors', 0);
        $projects_array = $this->projects_m->get(null, ['id', 'title'])->result_array();
        $projects = [];
        foreach ($projects_array as $project){
            $projects[$project['id']]['title'] = $project['title'];
            $projects[$project['id']]['task_count'] = 0;
            $projects[$project['id']]['priority_high'] = 0;
            $projects[$project['id']]['priority_medium'] = 0;
            $projects[$project['id']]['priority_low'] = 0;
        }

        $pending_tasks = $this->tasks_m->get(
            ['task_status' => 'assigned'],
            ['tasks.id','tasks.project_id', 'tasks.task_status', 'tasks.user_id', 'tasks.task_priority']
        )->result_array();

        foreach ($pending_tasks as $task){
            $projects[$task['project_id']]['task_count']++;
            if ($task['task_priority'] == 'high'){
                $projects[$task['project_id']]['priority_high']++;
            }
            if ($task['task_priority'] == 'medium'){
                $projects[$task['project_id']]['priority_medium']++;
            }
            if ($task['task_priority'] == 'low'){
                $projects[$task['project_id']]['priority_low']++;
            }
        }

        $projects = sort_array_by_key($projects, 'task_count');

        return $projects;
    }

}
