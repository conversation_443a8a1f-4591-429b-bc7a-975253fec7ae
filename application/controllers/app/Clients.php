<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Clients extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('clients_m');

    }

    public function index() {
        $this->data['list_items']   = $this->clients_m->get(null, null, ['key' => 'title', 'direction' => 'ASC'])->result_array();
        $this->data['page_title']   = 'Clients';
        $this->data['page_name']    = 'clients/index';
        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'phone' => $this->input->post('phone'),
                'email' => $this->input->post('email'),
                'start_date' => empty($this->input->post('start_date')) ? null : $this->input->post('start_date'),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->clients_m->insert($data);
            set_alert('message_success', 'Client Added Successfully!');
        }
        redirect('app/clients/index');
    }

    public function edit($item_id){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'phone' => $this->input->post('phone'),
                'email' => $this->input->post('email'),
                'start_date' => empty($this->input->post('start_date')) ? null : $this->input->post('start_date'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->clients_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Client Updated Successfully!');
        }
        redirect('app/clients/index');
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->clients_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Client Deleted Successfully!');
        }
        redirect('app/clients/index');
    }
}
