<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Cpanel_accounts extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('cpanel_accounts_m');
        $this->load->model('cpanel_whm_m');
        $this->load->model('cpanel_domains_m');
        $this->load->model('cpanel_access_log_m');
    }

    public function index() {
        $this->data['whm_accounts']   = $this->cpanel_whm_m->get()->result_array();
        $this->data['cpanel_accounts_m']   = $this->cpanel_whm_m->get()->result_array();
        $this->data['page_title']   = 'Documents';
        $this->data['page_name']    = 'documents/index';
        $this->load->view('app/index', $this->data);
    }

    public function generate_cpanel_accounts(){
        $whm_accounts = $this->cpanel_whm_m->get()->result_array();
        foreach($whm_accounts as $whm){
            // get cpanel accounts

            $cpanel_accounts = get_cpanel_accounts($whm['username'], $whm['api_key'], $whm['ip_address']);
            $cpanel_accounts = $cpanel_accounts['data']['acct'];

            foreach ($cpanel_accounts as $cpanel){
                // delete if already
                $this->cpanel_accounts_m->delete(['ip_address' => $cpanel['ip_address']]);

                // update password
                $password = generate_secure_password(10);

                $is_update = update_cpanel_password($whm['username'], $whm['token'], $whm['ip_address'], $cpanel['user'], $password);
                // add new
                if($cpanel['domain']!='pms.trogon.info'){
                    $cpanel_insert = [
                        'ip_address' => $cpanel['ip'],
                        'username' => $cpanel['user'],
                        'password' => $password,
                        'domain' => $cpanel['domain'],
                        'created_by' => get_user_id(),
                        'updated_by' => get_user_id(),
                        'created_on' => date('Y-m-d H:i:s'),
                        'updated_on' => date('Y-m-d H:i:s'),
                    ];
                    $this->cpanel_accounts_m->insert($cpanel_insert);
                }

            }
        }
    }

}
