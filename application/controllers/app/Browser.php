<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Browser extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('app_usage_m');
    }


    public function save_progress(){
        if ($this->input->post()){
            $data = [
                'user_id' => get_user_id(),
                'window_title' => $this->input->post('window_title'),
                'url' => $this->input->post('url'),
                'duration' => $this->input->post('duration'),
                'created_on' => date('Y-m-d H:i:s')
            ];
            $this->app_usage_m->insert($data);
            $response = ['status' => true, 'message' => 'Success'];
        }else{
            $response = ['status' => false, 'message' => 'Invalid Request!'];
        }
        echo json_encode($response);
    }
}
