<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Products extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('products_m');
    }

    public function index() {
        $where = [];
        $this->data['list_items']   = $this->products_m->get($where)->result_array();
        $this->data['page_title']   = 'products';
        $this->data['page_name']    = 'products/index';
        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'created_on' => date('Y-m-d H:i:s'),
                'description' => $this->input->post('description'),

            ];
            $this->products_m->insert($data);
            set_alert('message_success', 'Products Added Successfully!');
        }
        redirect('app/products/index');
    }

    public function edit($item_id){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
            ];
            $this->products_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Products Updated Successfully!');
        }
        redirect('app/products/index');
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->products_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Products Deleted Successfully!');
        }
        redirect('app/products/index');
    }
}
