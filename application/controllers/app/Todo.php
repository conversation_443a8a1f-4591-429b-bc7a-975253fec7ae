<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Todo extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('todo_m');
        $this->load->model('todo_category_m');
        $this->load->model('users_m');
    }

    public function index() {
        if (!has_permission('todo/index')){
            redirect('app/dashboard/index');
        }

        $this->data['start_date'] = empty($this->input->get('start_date')) ? date('Y-m-01') : $this->input->get('start_date');
        $this->data['end_date'] = empty($this->input->get('end_date')) ? date('Y-m-t') : $this->input->get('end_date');

        $where['date(todo.due_date) >='] = $this->data['start_date'];
        $where['date(todo.due_date) <='] = $this->data['end_date'];

        if (!is_super_admin() && !is_project_manager()) {
            $where['created_by'] = get_user_id();
        }

        // get status wise count
        $this->data['status_count'] = [
            'all' => $this->todo_m->get_count($this->data['start_date'], $this->data['end_date']),
            'pending' => $this->todo_m->get_count($this->data['start_date'], $this->data['end_date'], ['todo_status' => 'pending']),
            'assigned' => $this->todo_m->get_count($this->data['start_date'], $this->data['end_date'], ['todo_status' => 'assigned']),
            'completed' => $this->todo_m->get_count($this->data['start_date'], $this->data['end_date'], ['todo_status' => 'completed']),
        ];

        if (!empty($this->input->get('todo_status')) && $this->input->get('todo_status') != 'all'){
            $where['todo.todo_status'] = $this->input->get('todo_status');
        }


        $this->data['list_items'] = $this->todo_m->get($where, null, ['key' => 'todo.id', 'direction' => 'desc'])->result_array();

        $this->data['todo_category'] = array_column($this->todo_category_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['users'] = array_column($this->users_m->get(null, ['id', 'name'])->result_array(), 'name', 'id');

        $this->data['page_title']    = 'Todo';
        $this->data['page_name']     = 'todo/index';
        $this->load->view('app/index', $this->data);
    }



    public function add(){
        if ($this->input->post()){
            $remark_files = array_column($this->upload_file_multiple('todo', 'remark_files'), 'file');
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'user_id' => $this->input->post('user_id'),
                'todo_status' => 'assigned',
                'todo_priority' => $this->input->post('todo_priority'),
                'todo_category_id' => $this->input->post('todo_category_id'),
                'due_date' => $this->input->post('due_date'),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            if (count($remark_files)){
                $data['remark_files'] = json_encode($remark_files);
            }
            $this->todo_m->insert($data);
            set_alert('message_success', 'Todo Added Successfully!');
        }
        redirect($_SESSION['HTTP_REFERER']);
    }

    public function edit($item_id){
        if ($this->input->post()){
            $remark_files = array_column($this->upload_file_multiple('todo', 'remark_files'), 'file');
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'user_id' => $this->input->post('user_id'),
                'todo_priority' => $this->input->post('todo_priority'),
                'todo_category_id' => $this->input->post('todo_category_id'),
                'due_date' => $this->input->post('due_date'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            if (count($remark_files)){
                // delete existing files
                $todo_files = json_decode($this->todo_m->get(['id' => $item_id])->row()->remark_files, true);
                if (is_array($todo_files)){
                    foreach ($todo_files as $file){
                        if (is_file($file)){
                            unlink($file);
                        }
                    }
                }

                $data['remark_files'] = json_encode($remark_files);
            }
            $this->todo_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Todo Updated Successfully!');
        }
        redirect($_SESSION['HTTP_REFERER']);
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->todo_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Todo Deleted Successfully!');
        }
        redirect('app/todo/index');
    }


    // update todo status employee
    public function employee_update_status($item_id){
        //update todo status
        $data = [
            'todo_status' => $this->input->post('todo_status'),
            'remarks' => $this->input->post('remarks'),
        ];
        $this->todo_m->update($data, ['id' => $item_id]);
        log_message('error', $this->db->last_query());

        set_alert('message_success', 'Todo Status Updated Successfully!');
        redirect($_SERVER['HTTP_REFERER']);
    }
}
