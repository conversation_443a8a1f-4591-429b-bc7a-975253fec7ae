<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class State extends App_Controller{
	public function __construct () {
		parent::__construct();
		$this->load->model('state_m');
		$this->load->model('country_m');
	}

	public function index() {
		$where = [];
		if ($this->input->get('country_id')){
			$where['country_id'] = $this->input->get('country_id');
		}
		$this->data['countries']   	= array_column($this->country_m->get()->result_array(), 'title', 'id');
		$this->data['list_items']   = $this->state_m->get($where)->result_array();
		$this->data['page_title']   = 'State';
		$this->data['page_name']    = 'state/index';
		$this->load->view('app/index', $this->data);
	}

	public function add(){
		if ($this->input->post()){
			$data = [
				'title' => $this->input->post('title'),
				'country_id' => $this->input->post('country_id'),
				'created_by' => get_user_id(),
				'updated_by' => get_user_id(),
				'created_on' => date('Y-m-d H:i:s'),
				'updated_on' => date('Y-m-d H:i:s'),
			];
			$this->state_m->insert($data);
			set_alert('message_success', 'State Added Successfully!');
		}
		redirect('app/state/index');
	}

	public function edit($item_id){
		if ($this->input->post()){
			$data = [
				'title' => $this->input->post('title'),
				'country_id' => $this->input->post('country_id'),
				'updated_by' => get_user_id(),
				'updated_on' => date('Y-m-d H:i:s'),
			];
			$this->state_m->update($data, ['id' => $item_id]);
			set_alert('message_success', 'State Updated Successfully!');
		}
		redirect('app/state/index');
	}

	public function delete($item_id){
		if ($item_id > 0){
			$this->state_m->delete(['id' => $item_id]);
			set_alert('message_success', 'State Deleted Successfully!');
		}
		redirect('app/state/index');
	}

	public function get_states_by_country(){
		$country_id = $this->input->get('country_id');
		$states = $this->state_m->get(['country_id' => $country_id])->result_array();
		echo "<option value=''>Select State</option>";
		foreach ($states as $state){
			echo "<option value='{$state['id']}'>{$state['title']}</option>";
		}
	}
}
