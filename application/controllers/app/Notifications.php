<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Notifications extends App_Controller{
	public function __construct () {
		parent::__construct();
		$this->load->model('notification_m');
		$this->load->model('role_m');
        if (!is_super_admin()) {
            redirect('app/dashboard/index');
        }
	}

	public function index() {
		$where = [];
		if ($this->input->get('filter_notification_type')){
			$where['notification_type'] = $this->input->get('filter_notification_type');
		}
		$this->data['list_items']   = $this->notification_m->get($where)->result_array();
		$this->data['page_title']   = 'Notifications';
		$this->data['page_name']    = 'notifications/index';
		$this->load->view('app/index', $this->data);
	}

	public function add(){
		if ($this->input->post()){
		  //  log_message('error',print_r($this->input->post(),true));
			$data = [
				'title'             => $this->input->post('title'),
				'content'           => $this->input->post('content'),
				'button_text'       => $this->input->post('button_text'),
				'button_link'       => $this->input->post('button_link'),
				'notification_type' => $this->input->post('notification_type') ,
				'role_id'           => 0,
				'user_id'           => get_user_id(),
				'created_by'        => get_user_id(),
				'updated_by'        => get_user_id(),
				'created_on'        => date('Y-m-d H:i:s'),
				'updated_on'        => date('Y-m-d H:i:s')
			];
			$this->notification_m->insert($data);
			set_alert('message_success', 'Notifications Added Successfully!');
		}
		redirect('app/Notifications/index');
	}

	public function edit($item_id){
		if ($this->input->post()){
			$data = [
				'title'             => $this->input->post('title'),
				'content'           => $this->input->post('content'),
				'button_text'       => $this->input->post('button_text'),
				'button_link'       => $this->input->post('button_link'),
				'notification_type' => $this->input->post('notification_type') ,
				'role_id'           => 0,
				'user_id'           => $this->input->post('user_id') ,
				'updated_by'        => get_user_id(),
				'updated_on'        => date('Y-m-d H:i:s'),
			];
			$this->notification_m->update($data, ['id' => $item_id]);
			set_alert('message_success', 'Notifications Updated Successfully!');
		}
		redirect('app/Notifications/index');
	}

	public function delete($item_id){
		if ($item_id > 0){
			$this->notification_m->delete(['id' => $item_id]);
			set_alert('message_success', 'Notifications Deleted Successfully!');
		}
		redirect('app/Notifications/index');
	}


}
