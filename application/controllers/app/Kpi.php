<?php

class K<PERSON> extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('users_m');
        $this->load->model('kpi_m');
    }

    public function overview() {
        if (empty($this->input->get('start_date'))){
            $start_date = date('Y-m-01');
            $end_date = date('Y-m-t');
            redirect("app/kpi/overview/?start_date={$start_date}&end_date={$end_date}");
        }else{
            $start_date = $this->input->get('start_date');
            $end_date = $this->input->get('end_date');
        }

        $this->data['kpi_report'] = $this->kpi_m->overview($start_date, $end_date);
        echo json_encode($this->data['kpi_report']);

//        $this->data['page_title']   = 'KPI Report';
//        $this->data['page_name']    = 'kpi/overview';
//        $this->load->view('app/index', $this->data);
    }

}