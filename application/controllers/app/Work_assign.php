<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Work_assign extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('work_assign_m');
        $this->load->model('users_m');
        $this->load->model('projects_m');
    }


    public function index(){

        if (empty($this->input->get('job_date'))){
            redirect('app/work_assign/index/?job_date='.date('Y-m-d'));
        }
        $job_date = $this->input->get('job_date');

        $this->data['users'] = $this->work_assign_m->get_jobs_by_date($job_date);

        if ($this->input->post()){
            $work_status = $this->input->post('work_status');
            $remarks = $this->input->post('remarks');
            $is_support = $this->input->post('is_support');
            foreach ($this->data['users'] as $user){
                // check if already for user
                $is_already = $this->work_assign_m->get(['job_date' => $job_date, 'user_id' => $user['id']])->num_rows() > 0;

                $work_assign = [
                    'user_id' => $user['id'],
                    'job_date' => $job_date,
                    'work_status' => $work_status[$user['id']],
                    'remarks' => $remarks[$user['id']],
                    'is_support' => $is_support[$user['id']] == 1 ? 1: 0,
                    'works' => !empty($user['tasks']) ? json_encode($user['tasks']) : NULL,
                    'created_by' => get_user_id(),
                    'created_on' => date('Y-m-d H:i:s'),
                ];

                if (!$is_already){
                    $this->work_assign_m->insert($work_assign);
                }else{
                    $this->work_assign_m->update($work_assign, ['job_date' => $job_date, 'user_id' => $user['id']]);
                }
            }
            if ($this->input->post('submit') == 'download'){
                $pdf_url = $this->daily_pdf($job_date);
                if (file_exists($pdf_url)) {
                    header('Content-Description: File Transfer');
                    header('Content-Type: application/octet-stream');
                    header('Content-Disposition: attachment; filename="' . basename($pdf_url) . '"');
                    header('Expires: 0');
                    header('Cache-Control: must-revalidate');
                    header('Pragma: public');
                    header('Content-Length: ' . filesize($pdf_url));
                    readfile($pdf_url);
                    exit;
                }
            }

        }

        $this->data['users'] = $this->work_assign_m->get_jobs_by_date($job_date);

        $this->data['projects'] = array_column($this->projects_m->get()->result_array(), 'title', 'id');

        $this->data['page_title']   = 'Work Assign';
        $this->data['page_name']    = 'work_assign/index';
        $this->load->view('app/index', $this->data);
    }

    public function daily_pdf($job_date){
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => [160, 1000],
            'margin_left' => 3,
            'margin_right' => 3,
            'margin_top' => 3,
            'margin_bottom' => 3,
        ]);

        $data['job_date'] = $job_date;

        $data['users'] = $this->work_assign_m->get_jobs_by_date($job_date);
        $data['projects'] = array_column($this->projects_m->get()->result_array(), 'title', 'id');

        $mpdf->AddPage();
        $html = $this->load->view('app/work_assign/work_assign_pdf', $data, true);
        $mpdf->WriteHTML($html);
        $job_date = DateTime::createFromFormat('Y-m-d', $job_date)->format('d-m-Y_l');
        $pdf_url = "uploads/work_assign/trogon_projects_{$job_date}.pdf";

        $mpdf->Output($pdf_url,'F');
        if ($_GET['view']){
            $mpdf->Output();
        }
        return $pdf_url;
    }

    public function resource_overview(){
        if (empty($this->input->get('job_date'))){
            redirect('app/work_assign/index/?job_date='.date('Y-m-d'));
        }
        $job_date = $this->input->get('job_date');

        $this->data['users'] = $this->work_assign_m->get_jobs_by_date($job_date);

        $this->data['projects'] = array_column($this->projects_m->get()->result_array(), 'title', 'id');

        $this->data['page_title']   = 'Resource Overview';
        $this->data['page_name']    = 'work_assign/resource_overview';
        $this->load->view('app/index', $this->data);
    }

    public function work_assign_bulk() {
        if ($this->input->post()){
            $job_date = $this->input->post('job_date');
            $tasks = $this->input->post('task');
            foreach ($tasks as $task_id => $task){
                $this->db->where('id', $task_id);
                $this->db->update('tasks', ['due_date' => $job_date]);
            }
            set_alert('message_success', 'Work Assigned Successfully!');
            redirect('app/work_assign/index/?job_date='.$job_date);
        }
    }


}
