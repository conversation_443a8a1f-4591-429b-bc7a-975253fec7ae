<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 * FILE PATH: application/controllers/app/Dashboard.php
 */

class Dashboard extends App_Controller{
    private $enable_awards = true;
	public function __construct () {
		parent::__construct();
	    $this->load->model('users_m');
	    $this->load->model('tasks_m');
	    $this->load->model('todo_m');
	    $this->load->model('projects_m');

	    $this->load->model('time_log_m');
	    $this->load->model('attendance_m');
	    $this->load->model('time_log_type_m');
	    $this->load->model('task_assign_m');
	    $this->load->model('work_report_m');
        $this->load->model('kpi_m');
        $this->load->model('app_logs_m');
    }

    public function test(){
        $this->data['page_name'] = 'dashboard_new/index';
		$this->load->view('app/index', $this->data);
    }

    public function lock(){
        
        if($this->input->post()){
            $explanation = $this->input->post('explanation');
            
            $this->db->where('user_id', get_user_id());
            $this->db->where('is_lock', 1);
            $this->db->update('dashboard_lock', [
                // 'is_lock' => 0, 
                'remarks' => $explanation,
                'updated_on' => date('Y-m-d H:i:s') 
                ]);
            
            // echo $this->db->last_query();
            
            set_alert('message_success', 'Your request is submitted!');
            redirect('app/dashboard/lock');
        }else{
            $this->db->where('user_id', get_user_id());
            $this->db->where('is_lock', 1);
            $is_lock = $this->db->get('dashboard_lock')->num_rows() > 0;
            if(!$is_lock){
                redirect('app/dashboard/index');
            }
        }
        
        // check reveiew
        $this->db->where('user_id', get_user_id());
        $this->db->where('is_lock', 1);
        $this->db->where('remarks!=', null);
        $this->data['is_review'] = $this->db->get('dashboard_lock')->num_rows() > 0;
        
        $this->data['page_title'] = 'Dashboard is blocked!';
        $this->data['page_name'] = 'lock/lock';
		$this->load->view('app/index', $this->data);
    }

	public function index($job_date = null) {
	    
	    // create some logs
	    $this->app_logs_m->create_log();
	    
        if (empty($job_date)){
            $job_date = date('Y-m-d');
        }
	    
	    if(is_hr() || get_user_id() == 45 || is_project_manager()){
	        $this->data['notifications'] = $this->check_notifications();
	    }

        if (has_permission('users/index') && $_GET['user_id'] > 0){
            $user_id = $_GET['user_id'];
        }else{
            $user_id = get_user_id();
        }

        $this->data['kpi_score']   = $this->get_kpi_score($user_id);
        


        if ($this->enable_awards || is_project_manager()){
            // $start_date = '2024-07-01';
            // $end_date = '2024-07-31';
            
            $month = '05-2025';
            $month_dateObj = DateTime::createFromFormat('m-Y', $month)->format('F Y');
            $this->data['award_month'] = strtoupper($month_dateObj);

            // get users
            $users = $this->users_m->get(
                ['employee_code!=' => '', 'employee_status' => 1],
                ['id', 'name', 'phone', 'employee_code']
            )->result_array();

//            // get time log
//            $time_log = $this->time_log_m->get_time_log_data_overview($start_date, $end_date);
//
//            // get attendance data
//            $attendance_data = $this->attendance_m->get_attendance_data_overview($start_date, $end_date);
            $this->db->order_by('id', 'DESC');
            $report = $this->db->get('employee_performance', ['month' => $month])->row()->data;

            $report = json_decode($report, true);

            foreach ($report as $key => $report_item){
                if($report_item['rank'] == 1){
                    $report_item['profile_image'] = $this->users_m->get(['id' => $report_item['id']], ['photo'])->row()->photo;
                    $this->data['performance']['best'] = $report_item;
                }

                if($report_item['id'] == $user_id){
                    $this->data['performance']['your'] = $report_item;
                    $this->data['performance']['your']['rank'] = $report_item['rank'];
                }
            }

        }
	    
        $this->data['projects'] = array_column($this->projects_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');

        $employee_data = $this->dashboard_data_employee($user_id);
        $this->data['tasks'] = $employee_data['tasks'];
        $this->data['todo'] = $employee_data['todo'];
        $this->data['tasks_pending_count'] = $employee_data['tasks_pending_count'];
        $this->data['tasks_pending_due_count'] = $employee_data['tasks_pending_due_count'];
        $this->data['todo_pending_count'] = $employee_data['todo_pending_count'];
        $this->data['todo_pending_due_count'] = $employee_data['todo_pending_due_count'];

        $this->data['users'] = array_column($this->users_m->get(null, ['id', 'name'])->result_array(), 'name', 'id');
        
        // get attendance data 
        $from_date = date('Y-m-01');
        $to_date = date('Y-m-d');

        $this->data['dates_array']      = array_reverse(get_date_array($from_date, $to_date));
        $this->data['time_log']         = $this->time_log_m->get_employee_time_log_data($user_id, $from_date, $to_date);
        $this->data['attendance_data']  = $this->attendance_m->get_employee_attendance_data($user_id, $from_date, $to_date);
        $this->data['time_log_type']    = $this->time_log_type_m->get(['status' => 1])->result_array();


        $this->data['log_in_time'] = $this->app_logs_m->get_log_in_time($user_id, $job_date);
		$this->data['job_date']   = $job_date;
		$this->data['user_details']   = $this->users_m->get(['id' => $user_id])->row_array();
		$this->data['data_sheet']   = $this->work_report_m->get_time_sheet($user_id, $job_date);

        // work calendar
        $this->data['work_calendar_month'] = DateTime::createFromFormat('Y-m-d', $job_date)->format('m-Y');
        $this->data['work_calendar'] = $this->work_report_m->get_monthly_overview_calendar($user_id, $this->data['work_calendar_month']);

        // get team members
        if (is_app_lead()){
            $this->data['team_members'] = $this->users_m->get_task_assign_users();
        }

		$this->data['page_title']   = 'Dashboard';
        if (is_mobile() || is_tablet()){
            $this->data['page_name']    = 'dashboard/index_mobile';
        }else{
            $this->data['page_name']    = 'dashboard/index_new';
        }
		$this->load->view('app/index', $this->data);
	}

    private function get_kpi_score($user_id){
        $start_date = date('Y-m-01');
        $end_date = date('Y-m-t');
        $kpi_report = $this->kpi_m->overview($start_date, $end_date);
        return $kpi_report[$user_id]['effective_kpi'] ?? 0;
    }

    private function dashboard_data_employee($user_id): array {
        $data['tasks'] = $this->_get_tasks($user_id);

        // calculate pending task count
        $data['tasks_pending_count'] = 0;
        $data['tasks_pending_due_count'] = 0;
        foreach ($data['tasks'] as $key => $task){
            if ($task['task_status'] == 'assigned' || $task['task_status'] == 'on_hold'){
                $data['tasks_pending_count'] ++;
                if ($task['due_date'] < date('Y-m-d')){
                    $data['tasks_pending_due_count'] ++;
                }
            }
        }

        $data['todo'] = $this->_get_todo($user_id);

        // calculate pending todo count
        $data['todo_pending_count'] = 0;
        $data['todo_pending_due_count'] = 0;
        foreach ($data['todo'] as $key => $todo){
            if ($todo['todo_status'] == 'assigned'){
                $data['todo_pending_count'] ++;
                if ($todo['due_date'] < date('Y-m-d')){
                    $data['todo_pending_due_count'] ++;
                }
            }
        }

        return $data;
    }

    // get tasks
    private function _get_tasks($user_id) {
        $date_limit = date('Y-m-d', strtotime('-10 weeks'));

        $this->db->group_start();
        $this->db->where('task_status', 'assigned');
        $this->db->or_where('due_date >=', $date_limit);
        $this->db->group_end();
        $tasks = $this->tasks_m->get(['user_id' => $user_id])->result_array();

        usort($tasks, function($a, $b) {
            if ($a['task_status'] == 'assigned' && $b['task_status'] != 'assigned') {
                return -1; // $a is assigned, $b is not, so $a should come first
            } else if ($a['task_status'] != 'assigned' && $b['task_status'] == 'assigned') {
                return 1; // $a is not assigned, $b is, so $b should come first
            } else if ($a['task_status'] != 'on_hold' && $b['task_status'] == 'on_hold') {
                return 1; // $a is not assigned, $b is, so $b should come first
            } else {
                // If task_status is same (either both 'assigned' or both 'not assigned'), compare dates
                $dateA = new DateTime($a['due_date']);
                $dateB = new DateTime($b['due_date']);

                // Compare dates, newer date should come first
                return $dateB <=> $dateA;
            }
        });

        return $tasks;
    }

    // get todo
    private function _get_todo($user_id) {
        $date_limit = date('Y-m-d', strtotime('-5 weeks'));

        $this->db->group_start();
        $this->db->where('todo_status', 'assigned');
        $this->db->or_where('due_date >=', $date_limit);
        $this->db->group_end();
        $todo = $this->todo_m->get(['user_id' => $user_id])->result_array();

        usort($todo, function($a, $b) {
            if ($a['todo_status'] == 'assigned' && $b['todo_status'] != 'assigned') {
                return -1; // $a is assigned, $b is not, so $a should come first
            } else if ($a['todo_status'] != 'assigned' && $b['todo_status'] == 'assigned') {
                return 1; // $a is not assigned, $b is, so $b should come first
            } else {
                // If task_status is same (either both 'assigned' or both 'not assigned'), compare dates
                $dateA = new DateTime($a['due_date']);
                $dateB = new DateTime($b['due_date']);

                // Compare dates, newer date should come first
                return $dateB <=> $dateA;
            }
        });
        return $todo;
    }

    private function _chart_data(): array {
        return [];
//        return [
//            'student_count' => $this->_chart_student_count(),
//            'school_count' => $this->_chart_school_count(),
//        ];
    }

    // student count chart
    private function _chart_student_count(){
        return [];

        // get classes list
        $classes = $this->users_m->get(null, ['id', 'name'])->result_array();

        // get class wise student list
        foreach ($classes as $key => $class){
            if (is_super_admin()){
                $classes[$key]['male'] = $this->users_m->get(['classes_id' => $class['id'], 'gender' => 'male'], ['count(id) as total_count'])->row()->total_count ?? 0;
                $classes[$key]['female'] = $this->users_m->get(['classes_id' => $class['id'], 'gender' => 'female'], ['count(id) as total_count'])->row()->total_count ?? 0;
            }else{
                $classes[$key]['male'] = $this->users_m->get(['classes_id' => $class['id'], 'gender' => 'male', 'school_id' => get_school_id()], ['count(id) as total_count'])->row()->total_count ?? 0;
                $classes[$key]['female'] = $this->users_m->get(['classes_id' => $class['id'], 'gender' => 'female', 'school_id' => get_school_id()], ['count(id) as total_count'])->row()->total_count ?? 0;
            }
        }
        return $classes;
    }

    // school count chart
    private function _chart_school_count(){

        // get school category count
        $school_categories = $this->school_category_m->get(null, ['id', 'title'])->result_array();

        // get class wise student list
        foreach ($school_categories as $key => $school_category){
            $school_categories[$key]['active'] = $this->school_m->get(['category_id' => $school_category['id'], 'status' => 1], ['count(id) as total_count'])->row()->total_count ?? 1;
            $school_categories[$key]['in_active'] = $this->school_m->get(['category_id' => $school_category['id'], 'status' => 0], ['count(id) as total_count'])->row()->total_count ?? 1;

        }
        return $school_categories;
    }

    public function under_construction(){
        $this->data['page_title']   = 'Dashboard';
        $this->data['page_name']    = 'dashboard/under_construction';
        $this->load->view('app/index', $this->data);
    }
    
    private function check_notifications(){
        $notifications = [];
        
        $start_date = date('m-d');
        $date = new DateTime();
        $date->modify('+5 days');
        $end_date = $date->format('m-d');
        
        $sql_dob = "SELECT * FROM users 
                WHERE 
                DATE_FORMAT(dob, '%m-%d') >= ? 
                AND 
                DATE_FORMAT(dob, '%m-%d') <= ? AND employee_status = 1";

        $query_dob = $this->db->query($sql_dob, array($start_date, $end_date));
        $notifications['dob_users'] = $query_dob->result_array();
        
        $today_date = date('Y-m-d');
        
        $sql_jod = "SELECT * FROM users 
                WHERE 
                DATE_FORMAT(join_date, '%m-%d') >= ? 
                AND 
                DATE_FORMAT(join_date, '%m-%d') <= ? AND employee_status = 1 AND is_employee = 1";

        $query_jod = $this->db->query($sql_jod, array($start_date, $end_date));
        $notifications['jod_users'] = $query_jod->result_array();
        
        return $notifications;
    }
}
