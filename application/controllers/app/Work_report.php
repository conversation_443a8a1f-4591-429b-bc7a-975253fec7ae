<?php

class Work_report extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('users_m');
        $this->load->model('projects_m');
        $this->load->model('work_report_m');
        $this->load->model('work_assign_m');
        $this->load->model('attendance_m');
        ini_set('pcre.backtrack_limit', 10000000);
        ini_set('memory_limit', '2048M');
    }

    public function overview_report() {
        if (empty($this->input->get('start_date'))){
            $end_date = date('Y-m-d');
            if (date('H') > 10){
                $start_date = $end_date;
            }else{
                $start_date = date('Y-m-d', strtotime("-1 day"));
            }
            redirect("app/work_report/overview_report/?start_date={$start_date}&end_date={$end_date}");
        }else{
            $start_date = $this->input->get('start_date');
            $end_date = $this->input->get('end_date');
        }
        $this->data['work_report'] = $this->work_report_m->get_time_sheet_overview($start_date, $end_date);
        $this->data['attendance'] = $this->work_assign_m->get_job_attendance_data($start_date, $end_date);
        $this->data['users'] = $this->users_m->get([
            'employee_status' => 1, 'work_report' => 1
        ], null, ['key' => 'name', 'direction' => 'ASC'])->result_array();

        $this->data['projects'] = $this->projects_m->get(null, ['id', 'title', 'project_type'])->result_array();
        $this->data['projects'] = array_column($this->data['projects'], 'title', 'id');

        $this->data['pdf_url']   = $this->get_overview_report_pdf($start_date, $end_date);
        $this->data['page_title']   = 'Work Report';
        $this->data['page_name']    = 'work_report/overview_report';
        $this->load->view('app/index', $this->data);
    }

    public function get_overview_report_pdf($start_date, $end_date){
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => [160, 1000],
            'margin_left' => 3,
            'margin_right' => 3,
            'margin_top' => 3,
            'margin_bottom' => 3,
        ]);

        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

        $data['work_report'] = $this->work_report_m->overview_report_data($start_date, $end_date);
        $data['attendance'] = $this->work_assign_m->get_job_attendance_data($start_date, $end_date);
        $data['users'] = $this->users_m->get([
            'employee_status' => 1, 'work_report' => 1
        ], null, ['key' => 'name', 'direction' => 'ASC'])->result_array();

        $mpdf->AddPage();
        $html = $this->load->view('app/work_report/overview_report_pdf', $data, true);
        $mpdf->WriteHTML($html);
        $start_date = DateTime::createFromFormat('Y-m-d', $start_date)->format('d-m-Y_l');
        $pdf_url = "uploads/work_report/Work_Report_{$start_date}.pdf";

        $mpdf->Output($pdf_url,'F');
        if ($_GET['view']){
            $mpdf->Output();
        }
        return base_url($pdf_url);
    }

    public function employee_report(){
        $user_id = $this->input->get('user_id');
        $project_id = $this->input->get('project_id');

        if (empty($this->input->get('start_date'))){
            $start_date = new DateTime('first day of this month');
            $end_date = new DateTime('last day of this month');

            $start_date = $start_date->format('Y-m-d');
            $end_date = $end_date->format('Y-m-d');

            redirect("app/work_report/employee_report/?start_date={$start_date}&end_date={$end_date}&user_id={$user_id}");
        }else{
            $start_date = $this->input->get('start_date');
            $end_date = $this->input->get('end_date');
        }
        if ($user_id > 0){
            $this->data['work_report'] = $this->work_report_m->employee_report($user_id, $start_date, $end_date, $project_id);
        }

        $this->data['projects'] = $this->projects_m->get(null, ['id', 'title', 'project_type'])->result_array();
        $this->data['projects'] = array_column($this->data['projects'], 'title', 'id');

        $this->data['users'] = $this->users_m->get(['employee_status' => 1, 'work_assign' => 1], ['id', 'name'], ['key' => 'name', 'direction' => 'ASC'])->result_array();
        $this->data['users'] = array_column($this->data['users'], 'name', 'id');
        $this->data['page_title']   = 'Work Report - '. ($this->data['users'][$user_id] ?? 'N/A');
        $this->data['page_name']    = 'work_report/employee_report';
        $this->load->view('app/index', $this->data);
    }

    public function employee_status_report() {
        // Check permissions - only project_manager, hr, and admin can access
        if (!is_project_manager() && !is_hr() && !is_admin()) {
            show_error('You do not have permission to access this report.', 403, 'Access Denied');
            return;
        }

        if (empty($this->input->get('start_date'))){
            $start_date = date('Y-m-01'); // First day of current month
            $end_date = date('Y-m-d'); // Today
            redirect("app/work_report/employee_status_report/?start_date={$start_date}&end_date={$end_date}");
        }else{
            $start_date = $this->input->get('start_date');
            $end_date = $this->input->get('end_date');
        }

        // Get employees with employee_status = 1 and work_report = 1
        $this->data['users'] = $this->users_m->get([
            'employee_status' => 1, 'work_report' => 1
        ], null, ['key' => 'name', 'direction' => 'ASC'])->result_array();

        // Get employee status report data
        $this->data['status_report'] = $this->work_report_m->get_employee_status_report($start_date, $end_date);

        // Get date range array
        $this->data['date_range'] = get_date_array($start_date, $end_date);

        $this->data['start_date'] = $start_date;
        $this->data['end_date'] = $end_date;
        $this->data['page_title'] = 'Monthly Work Status';
        $this->data['page_name'] = 'work_report/employee_status_report';
        $this->load->view('app/index', $this->data);
    }

    // Test method to verify data processing (can be removed in production)
    public function test_status_report() {
        if (!ENVIRONMENT === 'development') {
            show_404();
            return;
        }

        $start_date = '2024-12-01';
        $end_date = '2024-12-07';

        echo "<h2>Testing Employee Status Report Data Processing</h2>";
        echo "<p><strong>Date Range:</strong> {$start_date} to {$end_date}</p>";

        // Get test data
        $status_report = $this->work_report_m->get_employee_status_report($start_date, $end_date);
        $date_range = get_date_array($start_date, $end_date);

        echo "<h3>Date Range Array:</h3>";
        echo "<pre>" . print_r($date_range, true) . "</pre>";

        echo "<h3>Status Report Data (First 2 users):</h3>";
        $count = 0;
        foreach ($status_report as $user_id => $user_data) {
            if ($count >= 2) break;
            echo "<h4>User ID: {$user_id} - {$user_data['user_info']['name']}</h4>";
            echo "<p><strong>Summary:</strong></p>";
            echo "<ul>";
            echo "<li>Total Hours: {$user_data['summary']['total_hours_formatted']}</li>";
            echo "<li>Days Updated: {$user_data['summary']['days_updated']}</li>";
            echo "<li>Days Partially Updated: {$user_data['summary']['days_partially_updated']}</li>";
            echo "<li>Days Not Updated: {$user_data['summary']['days_not_updated']}</li>";
            echo "<li>Days Not Applicable: {$user_data['summary']['days_not_applicable']}</li>";
            echo "</ul>";

            echo "<p><strong>Daily Status (First 3 days):</strong></p>";
            $day_count = 0;
            foreach ($user_data['daily_status'] as $date => $day_data) {
                if ($day_count >= 3) break;
                echo "<li>{$date}: {$day_data['status']} - {$day_data['work_hours_formatted']} - {$day_data['work_status']}</li>";
                $day_count++;
            }
            echo "</ul>";
            $count++;
        }

        echo "<p><strong>Total Users Processed:</strong> " . count($status_report) . "</p>";
    }

}