<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Tasks extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('tasks_m');
        $this->load->model('task_assign_m');
        $this->load->model('projects_m');
        $this->load->model('project_type_m');
        $this->load->model('clients_m');
        $this->load->model('users_m');
    }

    public function index() {
        if (!has_permission('tasks/index')){
            redirect('app/dashboard/index');
        }

        $this->data['start_date'] = empty($this->input->get('start_date')) ? date('Y-m-01') : $this->input->get('start_date');
        $this->data['end_date'] = empty($this->input->get('end_date')) ? date('Y-m-t') : $this->input->get('end_date');
        $this->data['project_id'] = $this->input->get('project_id') > 0 ? $this->input->get('project_id') : 0;

        $where = [];

        if($this->input->get('project_id') > 0){
            $where['tasks.project_id'] = $this->input->get('project_id');
        }

        if($this->input->get('user_id') > 0 && $this->input->get('task_status')!='pending' && $this->input->get('task_status')!='on_hold' && $this->input->get('task_status')!='all'){
            $where['tasks.user_id'] = $this->input->get('user_id');
        }

        if ($this->input->get('task_status')!='pending' && $this->input->get('task_status')!='assigned' && $this->input->get('task_status')!='on_hold'){
            $where['date(tasks.due_date) >='] = $this->data['start_date'];
            $where['date(tasks.due_date) <='] = $this->data['end_date'];
        }

        $this->data['status_count'] = [
            'all' => $this->tasks_m->get_count($this->data['start_date'], $this->data['end_date'],[], $this->input->get('project_id')),
            'pending' => $this->tasks_m->get_count($this->data['start_date'], $this->data['end_date'], ['task_status' => 'pending'], $this->input->get('project_id')),
            'assigned' => $this->tasks_m->get_count($this->data['start_date'], $this->data['end_date'], ['task_status' => 'assigned'], $this->input->get('project_id'), $this->input->get('user_id')),
            'testing' => $this->tasks_m->get_count($this->data['start_date'], $this->data['end_date'], ['task_status' => 'testing'], $this->input->get('project_id'), $this->input->get('user_id')),
            'on_hold' => $this->tasks_m->get_count($this->data['start_date'], $this->data['end_date'], ['task_status' => 'on_hold'], $this->input->get('project_id')),
            'completed' => $this->tasks_m->get_count($this->data['start_date'], $this->data['end_date'], ['task_status' => 'completed'], $this->input->get('project_id'), $this->input->get('user_id')),
        ];

        if (!empty($this->input->get('task_status')) && $this->input->get('task_status') != 'all'){
            $where['tasks.task_status'] = $this->input->get('task_status');
        }
        

        $this->data['list_items']    = $this->tasks_m->get($where, null, ['key' => 'id', 'direction' => 'desc'])->result_array();
        

        $this->data['clients'] = array_column($this->clients_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['projects_list'] = $this->projects_m->get(null, ['id', 'title', 'project_type'])->result_array();
        $this->data['projects'] = array_column($this->data['projects_list'], 'title', 'id');
        $this->data['project_type'] = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');

        $this->db->where_not_in('role_id', [0, 1]);
        $this->db->where('employee_status', 1);
        $this->data['users'] = array_column($this->users_m->get(null, ['id', 'name'])->result_array(), 'name', 'id');

        if (is_technical_support()){
            $this->data['tester_projects'] = array_column($this->projects_m->get(['tester_id' => get_user_id()], ['id'])->result_array(), 'id');
        }

        $this->data['page_title']    = 'Tasks';
        if (is_project_manager() || is_technical_support()){
            $this->data['page_name']     = 'tasks/index_new';
        }else{
            $this->data['page_name']     = 'tasks/index';
        }
        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $client_id = $this->projects_m->get(['id' => $this->input->post('project_id')])->row()->client_id;
            $remark_files = array_column($this->upload_file_multiple('tasks', 'remark_files'), 'file');
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'project_id' => $this->input->post('project_id'),
                'client_id' => $client_id,
                'task_status' => 'pending',
                'task_priority' => $this->input->post('task_priority'),
                'task_type' => $this->input->post('task_type'),
                'due_date' => $this->input->post('due_date'),
                'required_time' => $this->input->post('required_time'),
                'requested_by' => $this->input->post('requested_by'),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            if (count($remark_files)){
                $data['remark_files'] = json_encode($remark_files);
            }
            $this->tasks_m->insert($data);
            $task_id = $this->db->insert_id();
            set_alert('message_success', 'Task Added Successfully!');

            // if the user does not have permission for tasks listing then assign to themselves
            if ($this->input->post('assign_user_id') > 0){
                $data = [
                    'user_id' => $this->input->post('assign_user_id'),
                    'task_status' => 'assigned',
                    'task_priority' => $this->input->post('task_priority'),
                    'task_type' => $this->input->post('task_type'),
                    'due_date' => $this->input->post('due_date'),
                    'required_time' => $this->input->post('required_time'),
                    'updated_by' => get_user_id(),
                    'updated_on' => date('Y-m-d H:i:s'),
                ];
                $this->tasks_m->update($data, ['id' => $task_id]);

                // check if already assigned
                $is_assigned = $this->task_assign_m->get(['task_id' => $task_id])->num_rows() > 0;

                // assign user
                $data_assign = [
                    'task_id' => $task_id,
                    'user_id' => $this->input->post('assign_user_id'),
                    'assigned_by' => get_user_id(),
                    'due_date' => $this->input->post('due_date'),
                    'required_time' => $this->input->post('required_time'),
                    'task_priority' => $this->input->post('task_priority'),
                    'is_reschedule' => $is_assigned,
                    'created_by' => get_user_id(),
                    'updated_by' => get_user_id(),
                    'created_on' => date('Y-m-d H:i:s'),
                    'updated_on' => date('Y-m-d H:i:s'),
                ];
                $this->task_assign_m->insert($data_assign);
            }

        }
        if (is_mobile()){
            redirect('app/dashboard/index');
        }else{
            redirect($_SERVER['HTTP_REFERER']);
        }
    }

    public function add_mobile(){
        $this->data['page_title']    = 'Add Task';
        $this->data['page_name']     = 'tasks/add_mobile';
        $this->load->view('app/index', $this->data);
    }

    public function edit($item_id){
        if ($this->input->post()){
            $client_id = $this->projects_m->get(['id' => $this->input->post('project_id')])->row()->client_id;
            $remark_files = array_column($this->upload_file_multiple('tasks', 'remark_files'), 'file');
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'project_id' => $this->input->post('project_id'),
                'client_id' => $client_id,
                'task_priority' => $this->input->post('task_priority'),
                'task_type' => $this->input->post('task_type'),
                'due_date' => $this->input->post('due_date'),
                'required_time' => $this->input->post('required_time'),
                'requested_by' => $this->input->post('requested_by'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            if (count($remark_files)){
                // delete existing files
                $task_files = json_decode($this->tasks_m->get(['id' => $item_id])->row()->remark_files, true);
                if (is_array($task_files)){
                    foreach ($task_files as $file){
                        if (is_file($file)){
                            unlink($file);
                        }
                    }
                }

                $data['remark_files'] = json_encode($remark_files);
            }
            $this->tasks_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Task Updated Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function ajax_save_content(){
        if ($this->input->post()){
            $data = [
                'description' => $this->input->post('content'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->tasks_m->update($data, ['id' => $this->input->post('task_id')]);
            echo true;
        }
        echo false;
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->tasks_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Task Deleted Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    // tasks assign
    public function assign($item_id){
        if ($this->input->post()){
            $data = [
                'user_id' => $this->input->post('user_id'),
                'task_status' => 'assigned',
                'task_priority' => $this->input->post('task_priority'),
                'task_type' => $this->input->post('task_type'),
                'due_date' => $this->input->post('due_date'),
                'required_time' => $this->input->post('required_time'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->tasks_m->update($data, ['id' => $item_id]);

            // check if already assigned
            $is_assigned = $this->task_assign_m->get(['task_id' => $item_id])->num_rows() > 0;

            // assign user
            $data_assign = [
                'task_id' => $item_id,
                'user_id' => $this->input->post('user_id'),
                'assigned_by' => get_user_id(),
                'due_date' => $this->input->post('due_date'),
                'required_time' => $this->input->post('required_time'),
                'task_priority' => $this->input->post('task_priority'),
                'is_reschedule' => $is_assigned,
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->task_assign_m->insert($data_assign);
            log_message('error', $this->db->last_query());
            set_alert('message_success', 'Task Updated Successfully!');
            $user_id = $this->input->post('user_id');
            redirect($this->get_redirect_url($user_id));
        }
    }
    
    private function get_redirect_url($user_id){
        $url = $_SERVER['HTTP_REFERER'];
        
        $parsed_url = parse_url($url);
        $query_params = [];
        parse_str($parsed_url['query'] ?? '', $query_params);
        
        $query_params['user_id'] = $user_id;
        
        // Build the new query string
        $new_query_string = http_build_query($query_params);
        
        // Reconstruct the URL
        $new_url = "{$parsed_url['scheme']}://{$parsed_url['host']}{$parsed_url['path']}?$new_query_string";
        if (isset($parsed_url['fragment'])) {
            $new_url .= "#{$parsed_url['fragment']}";
        }
        $new_url .= "#user_{$user_id}";
        return $new_url;
    }

    public function un_assign($item_id){
        if ($this->input->post()){
            $data = [
                'task_status' => $this->input->post('action_status'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            if ($this->input->post('action_status') == 'pending'){
                $data['user_id'] = null;
            }
            $this->tasks_m->update($data, ['id' => $item_id]);
            
            set_alert('message_success', 'Task Updated Successfully!');
            redirect($_SERVER['HTTP_REFERER']);
        }
    }

    // update task status employee
    public function employee_update_status($item_id){


        if ($this->task_assign_m->is_task_period_overlap(get_user_id(), $this->input->post('job_date'), $this->input->post('start_time'), $this->input->post('end_time'))){
            set_alert('message_error', 'The task time overlaps with an existing task.!');
        }else{
            log_message('error', $this->db->last_query());
            
            $job_date = $this->input->post('job_date');

            //update task status
            $data = [
                'task_status' => $this->input->post('task_status'),
                'time_taken' => $this->input->post('time_taken'),
            ];
            $this->tasks_m->update($data, ['id' => $item_id]);

            // update task status assign
            $this->db->order_by('task_assign.id', 'DESC');
            $this->db->limit(1);
            $task_assign = $this->task_assign_m->get([
                'task_id' => $item_id,
                'user_id' => get_user_id(),
            ]);

            if ($task_assign->num_rows() > 0){
                $task_assign = $task_assign->row();

                if (empty($task_assign->time_taken)){
                    $this->task_assign_m->update(
                        [
                            'start_time' => $this->input->post('start_time'),
                            'end_time' => $this->input->post('end_time'),
                            'time_taken' => $this->input->post('time_taken'),
                            'remarks' => $this->input->post('remarks'),
                            'job_date' => $job_date,
                            'updated_on' => date('Y-m-d H:i:s'),
                            'updated_by' => get_user_id(),
                        ],
                        ['id' => $task_assign->id]
                    );
                }else{
                    $task_assign = [
                        'task_id' => $task_assign->task_id,
                        'user_id' => $task_assign->user_id,
                        'assigned_by' => $task_assign->assigned_by,
                        'due_date' => $task_assign->due_date,
                        'required_time' => $task_assign->required_time,
                        'start_time' => $this->input->post('start_time'),
                        'end_time' => $this->input->post('end_time'),
                        'time_taken' => $this->input->post('time_taken'),
                        'remarks' => $this->input->post('remarks'),
                        'job_date' => $job_date,
                        'task_priority' => $task_assign->task_priority,
                        'is_reschedule' => $task_assign->is_reschedule,
                        'created_by' => $task_assign->created_by,
                        'updated_by' => get_user_id(),
                        'created_on' => $task_assign->created_on,
                        'updated_on' => date('Y-m-d H:i:s'),
                    ];
                    $this->task_assign_m->insert($task_assign);
                }
            }else{
                $task = $this->tasks_m->get(['id' => $item_id])->row();
                $task_assign = [
                    'task_id' => $task->id,
                    'user_id' => $task->user_id,
                    'assigned_by' => $task->created_by,
                    'due_date' => $task->due_date,
                    'required_time' => $task->required_time,
                    'time_taken' => $this->input->post('time_taken'),
                    'remarks' => $this->input->post('remarks'),
                    'job_date' => $job_date,
                    'task_priority' => $task->task_priority,
                    'is_reschedule' => 0,
                    'created_by' => $task->created_by,
                    'updated_by' => get_user_id(),
                    'created_on' => $task->created_on,
                    'updated_on' => date('Y-m-d H:i:s'),
                ];
                $this->task_assign_m->insert($task_assign);
            }
            set_alert('message_success', 'Task Status Updated Successfully!');
        }





        if (is_mobile()) {
            redirect('app/dashboard/index');
        }else{
            redirect($_SERVER['HTTP_REFERER']);
        }
    }

    public function task_details_employee_mobile($item_id){
        $this->data['item_id']    = $item_id;
        $this->data['page_title']    = 'Add Task';
        $this->data['page_name']     = 'tasks/task_details_employee_mobile';
        $this->load->view('app/index', $this->data);
    }
   
    public function task_update_bulk(){
        if(!is_project_manager() && get_user_id() != 10){
            redirect('app/dashboard/index');
        }

        if($this->input->post()){
            $job_date = $this->input->post('job_date');
            
            if($job_date!= date('Y-m-d')){
                $created_at = $job_date.' 23:59:10';
            }else{
                $created_at = date('Y-m-d H:i:s');
            }
            $remarks = $this->input->post('remarks');

            if(empty($remarks)){
                set_alert('message_error', 'Please enter remarks');
                redirect($_SERVER['HTTP_REFERER']);
            }
            
            $tasks = getTaskTimeData($remarks);
            log_message('error', json_encode($tasks));
            
            if(empty($tasks)){
                set_alert('message_error', 'No tasks found');
                redirect($_SERVER['HTTP_REFERER']);
            }

            foreach($tasks as $task){
                // update task status assign
                $this->db->order_by('task_assign.id', 'DESC');
                $this->db->limit(1);
                $task_assign = $this->task_assign_m->get([
                    'task_id' => $task['task_id'],
                    'user_id' => get_user_id(),
                ]);

                if ($task_assign->num_rows() > 0){
                    $task_assign = $task_assign->row();

                    if (empty($task_assign->time_taken)){
                        $this->task_assign_m->update(
                            [
                                'start_time' => $task['start_time'],
                                'end_time' => $task['end_time'],
                                'time_taken' => $task['time_taken'],
                                'remarks' => $task['remarks'],
                                'job_date' => $job_date,
                                'updated_on' => $created_at,
                                'updated_by' => get_user_id(),
                            ],
                            ['id' => $task_assign->id]
                        );
                    }else{
                        $task_assign = [
                            'task_id' => $task_assign->task_id,
                            'user_id' => $task_assign->user_id,
                            'assigned_by' => $task_assign->assigned_by,
                            'due_date' => $task_assign->due_date,
                            'required_time' => $task_assign->required_time,
                            'start_time' => $task['start_time'],
                            'end_time' => $task['end_time'],
                            'time_taken' => $task['time_taken'],
                            'remarks' => $task['remarks'],
                            'job_date' => $job_date,
                            'task_priority' => $task_assign->task_priority,
                            'is_reschedule' => $task_assign->is_reschedule,
                            'created_by' => $task_assign->created_by,
                            'updated_by' => get_user_id(),
                            'created_on' => $task_assign->created_on,
                            'updated_on' => $created_at,
                        ];
                        $this->task_assign_m->insert($task_assign);
                    }
                }else{
                    $task = $this->tasks_m->get(['id' => $item_id])->row();
                    $task_assign = [
                        'task_id' => $task->id,
                        'user_id' => $task->user_id,
                        'assigned_by' => $task->created_by,
                        'due_date' => $task->due_date,
                        'required_time' => $task->required_time,
                        'start_time' => $task['start_time'],
                        'end_time' => $task['end_time'],
                        'time_taken' => $task['time_taken'],
                        'remarks' => $task['remarks'],
                        'job_date' => $job_date,
                        'task_priority' => $task->task_priority,
                        'is_reschedule' => 0,
                        'created_by' => $task->created_by,
                        'updated_by' => get_user_id(),
                        'created_on' => $task->created_on,
                        'updated_on' => $created_at,
                    ];
                    $this->task_assign_m->insert($task_assign);
                }
            }
            set_alert('message_success', 'Task Status Updated Successfully!');
            redirect($_SERVER['HTTP_REFERER']);
        }

        $this->data['page_title']    = 'Bulk Update Task';
        $this->data['page_name']     = 'tasks/task_update_bulk';
        $this->load->view('app/index', $this->data);
    }
    
    public function test_task($task_id){
        if ($task_id > 0){
            // update task
            $this->tasks_m->update(
                [
                    'tested_by' => get_user_id(),
                    'tester_remarks' => $this->input->post('remarks'),
                    'task_status' => $this->input->post('task_status'),
                    'updated_by' => get_user_id(),
                    'updated_on' => date('Y-m-d H:i:s'),
                ],
                ['id' => $task_id]
            );

            // get task details
            $task = $this->tasks_m->get(['id' => $task_id])->row();
            $task_assign = [
                'task_id' => $task->id,
                'user_id' => get_user_id(),
                'assigned_by' => get_user_id(),
                'due_date' => $task->due_date,
                'required_time' => $task->required_time,
                'start_time' => $this->input->post('start_time'),
                'end_time' => $this->input->post('end_time'),
                'time_taken' => $this->input->post('time_taken'),
                'remarks' => $this->input->post('remarks'),
                'job_date' => $this->input->post('job_date'),
                'task_priority' => $task->task_priority,
                'is_reschedule' => 1,
                'is_testing' => 1,
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->task_assign_m->insert($task_assign);

            // assign task
//            $task_assign =
            set_alert('message_success', 'Task Status Updated Successfully!');
            redirect($_SERVER['HTTP_REFERER']);

        }
    }

    public function task_status($task_id){
        if ($task_id > 0){
            // update task
            $this->tasks_m->update(
                [
                    'task_status' => $this->input->post('task_status'),
                    'updated_by' => get_user_id(),
                    'updated_on' => date('Y-m-d H:i:s'),
                ],
                ['id' => $task_id]
            );
            set_alert('message_success', 'Task Status Updated Successfully!');
            redirect($_SERVER['HTTP_REFERER']);
        }
    }

}



