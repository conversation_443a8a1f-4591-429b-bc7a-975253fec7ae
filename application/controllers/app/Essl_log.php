<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Essl_log extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('essl_log_m');
        $this->load->model('users_m');
    }

    public function index() {
        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');
        $user_id = $this->input->get('user_id');

        if (empty($start_date) || empty($end_date)){
            $start_date = date('Y-m-d');
            $end_date = date('Y-m-d');
        }

        $this->data['list_items']   = $this->essl_log_m->get_data($start_date, $end_date, $user_id);
        $this->data['page_title']   = 'Essl Log';
        $this->data['page_name']    = 'essl_log/index';
        $this->load->view('app/index', $this->data);
    }

    public function generate_attendance(){
        $start_date = '2024-04-01';
        $end_date = '2024-04-30';
        $log_data = $this->essl_log_m->generate_attendance($start_date, $end_date);
        echo json_encode($log_data);
    }
    public function push_attendance(){
        $start_date = '2024-04-01';
        $end_date = '2024-04-30';
        $log_data = $this->essl_log_m->push_attendance($start_date, $end_date);
        echo json_encode($log_data);
    }

    public function upload(){
        if ($this->input->post()){
            $csv_file = $this->upload_csv();
            if ($csv_file!=false){
                $this->process_csv($csv_file);
            }
        }
        $this->data['page_title']   = 'Essl Log - Upload CV';
        $this->data['page_name']    = 'essl_log/upload';
        $this->load->view('app/index', $this->data);

    }
    private function process_csv($file_path) {
        $users = $this->users_m->get(null, ['id', 'employee_code'])->result_array();
        $users = array_column($users, 'id', 'employee_code');
        $records_array = [];
        if (($handle = fopen($file_path, "r")) !== false) {
            // Read the header row to skip it
            fgetcsv($handle);

            // Loop through each row of CSV data
            while (($row = fgetcsv($handle)) !== false) {

                $row = json_decode(json_encode($row), true);
                echo '<hr>';

                $punch_records = trim($row[16]);

                // Use a regular expression to match the time patterns
                // preg_match_all('/(\d{2}:\d{2}):/', $punch_records, $matches);
                // The times are in the first element of the matches array
                // $punch_records = $matches[1];

                // Split the string by spaces
                $split_array = explode(' ', $punch_records);

                // Remove the "(TR)" portion from each element
                $formatted_array = array_map(function($item) {
                    // Remove anything within parentheses (and the parentheses themselves)
                    return preg_replace('/\(.*?\)/', '', $item);
                }, $split_array);



                echo json_encode($formatted_array, JSON_PRETTY_PRINT);


                $user_id = $users[$row[1]];
                $log_date = $row[0];
                $ess_log = [
                    'log_date' => $row[0],
                    'user_id' => $user_id,
                    'employee_code' => $row[1],
                    'time_in'=> $row[10],
                    'time_out'=> $row[11],
                    'duration'=> $row[12],
                    'punch_records_data' => $row[16],
                    'punch_records' => json_encode($formatted_array),
                    'attendance' => $row[15] == 'Absent' ? 'A' : 'P',
                    'created_by' => get_user_id(),
                    'updated_by' => get_user_id(),
                    'created_on' => date('Y-m-d H:i:s'),
                    'updated_on' => date('Y-m-d H:i:s')
                ];

                //check if already
                $is_already = $this->essl_log_m->get(['user_id' => $user_id, 'log_date' => $log_date]);
                if ($is_already->num_rows() > 0){
                    unset($ess_log['created_by']);
                    unset($ess_log['created_on']);
                    $this->essl_log_m->update($ess_log, ['user_id' => $user_id, 'log_date' => $log_date]);
                }else{
                    $this->essl_log_m->insert($ess_log);
                }
            }

            // Close the file handle
            fclose($handle);

            // Redirect or show success message
            // return redirect()->to(base_url('csv/uploaded'));
        } else {
            // Error opening the file
            echo "Error opening CSV file.";
        }
        echo json_encode($records_array);
    }

    private function upload_csv(){
        $uploadPath = 'uploads/csv/' . date("Y-m-d");
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0777, TRUE);
        }
        $config['upload_path'] = $uploadPath;
        $config['allowed_types'] = 'csv';
        $config['encrypt_name'] = true;

        $this->load->library('upload', $config);
        if ($this->upload->do_upload('file')) {
            $file_data = $this->upload->data();
            return $uploadPath.'/'.$file_data['file_name'];
        } else {
            return false;
        }
    }



    public function add(){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'phone' => $this->input->post('phone'),
                'email' => $this->input->post('email'),
                'start_date' => empty($this->input->post('start_date')) ? null : $this->input->post('start_date'),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->clients_m->insert($data);
            set_alert('message_success', 'Client Added Successfully!');
        }
        redirect('app/clients/index');
    }

    public function edit($item_id){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'phone' => $this->input->post('phone'),
                'email' => $this->input->post('email'),
                'start_date' => empty($this->input->post('start_date')) ? null : $this->input->post('start_date'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->clients_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Client Updated Successfully!');
        }
        redirect('app/clients/index');
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->clients_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Client Deleted Successfully!');
        }
        redirect('app/clients/index');
    }
}
