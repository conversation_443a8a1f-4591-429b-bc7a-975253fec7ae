<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Todo_category extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('todo_category_m');
    }

    public function index() {
        $this->data['list_items']    = $this->todo_category_m->get()->result_array();
        $this->data['page_title']    = 'Todo Category';
        $this->data['page_name']     = 'todo_category/index';
        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->todo_category_m->insert($data);
            set_alert('message_success', 'Todo Category Added Successfully!');
        }
        redirect('app/todo_category/index');
    }

    public function edit($item_id){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->todo_category_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Todo Category Updated Successfully!');
        }
        redirect('app/todo_category/index');
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->todo_category_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Todo Category Deleted Successfully!');
        }
        redirect('app/todo_category/index');
    }
}
