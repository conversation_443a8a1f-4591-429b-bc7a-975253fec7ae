<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Work_schedule extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('work_schedule_m');
        $this->load->model('users_m');
        $this->load->model('projects_m');
    }


    public function index($date = null){

        if (empty($this->input->get('start_date')) || empty($this->input->get('end_date'))){
            $week_dates = get_week_dates($date);
            redirect('app/work_schedule/index/?start_date='.$week_dates['start_date'].'&end_date='.$week_dates['end_date']);
        }
        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');

        $this->data['work_schedule'] = $this->work_schedule_m->get_work_schedule($start_date, $end_date);

        $this->data['users'] = array_column($this->users_m->get(['work_assign' => 1], ['id', 'name'])->result_array(), 'name', 'id');

        $project_id_list = array_keys($this->data['work_schedule']);
        if (count($project_id_list)){
            $this->db->where_in('id', $project_id_list);
            $this->data['projects'] = array_column($this->projects_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        }else{
            $this->data['projects'] = [];
        }

        $this->data['page_title']   = 'Work Schedule';
        $this->data['page_name']    = 'work_schedule/index';
        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'project_id' => $this->input->post('project_id'),
                'user_id' => json_encode($this->input->post('user_id')),
                'date' => $this->input->post('date'),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->work_schedule_m->insert($data);
            set_alert('message_success', 'Work Schedule Added Successfully!');
            redirect($_SERVER['HTTP_REFERER']);
        }
    }

    public function edit($item_id){
        if ($this->input->post() && $item_id > 0){
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'project_id' => $this->input->post('project_id'),
                'user_id' => json_encode($this->input->post('user_id')),
                'date' => $this->input->post('date'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->work_schedule_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Work Schedule Updated Successfully!');
            redirect($_SERVER['HTTP_REFERER']);
        }
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->work_schedule_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Client Deleted Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }


}
