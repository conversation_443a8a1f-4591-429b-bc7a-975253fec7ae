<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Project_assign extends App_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('project_assign_m');
        $this->load->model('users_m');
        $this->load->model('projects_m');

        // Check if user has permission
        if (!has_permission('project_assign/index')) {
            set_alert('message_error', 'Permission Denied!');
            redirect('app/dashboard/index');
        }
    }

    /**
     * List all project assignments
     */
    public function index() {
        // Get filter parameters
        $start_date = $this->input->get('start_date') ?? date('Y-m-d', strtotime('-7 days'));
        $end_date = $this->input->get('end_date') ?? date('Y-m-d');
        $user_id = $this->input->get('user_id') ?? 'all';
        $project_id = $this->input->get('project_id') ?? 'all';

        // Build where conditions
        $where = [];
        if ($start_date && $end_date) {
            $where['job_date >='] = $start_date;
            $where['job_date <='] = $end_date;
        }

        if ($user_id !== 'all') {
            $where['pa.user_id'] = $user_id;
        }

        if ($project_id !== 'all') {
            $where['pa.project_id'] = $project_id;
        }

        // Get data for view
        $this->data['assignments'] = $this->project_assign_m->get_assignments($where)->result_array();
        $this->data['users'] = array_column($this->users_m->get(['employee_status' => 1])->result_array(), 'name', 'id');
        $this->data['projects'] = array_column($this->projects_m->get()->result_array(), 'title', 'id');

        // Set filter values for the view
        $this->data['start_date'] = $start_date;
        $this->data['end_date'] = $end_date;
        $this->data['selected_user_id'] = $user_id;
        $this->data['selected_project_id'] = $project_id;

        // Set page data
        $this->data['page_title'] = 'Project Assignments';
        $this->data['page_name'] = 'project_assign/index';

        $this->load->view('app/index', $this->data);
    }

    /**
     * Add a new project assignment
     */
    public function add() {
        if ($this->input->post()) {
            $data = [
                'job_date' => $this->input->post('job_date'),
                'user_id' => $this->input->post('user_id'),
                'project_id' => $this->input->post('project_id'),
                'status' => $this->input->post('status'),
                'remarks' => $this->input->post('remarks'),
                'created_by' => $this->session->userdata('user_id'),
            ];

            $result = $this->project_assign_m->insert_assignment($data);

            if ($result) {
                set_alert('message_success', 'Project assignment added successfully!');
            } else {
                set_alert('message_error', 'Failed to add project assignment!');
            }

            redirect('app/project_assign/index');
        }

        // Get data for form
        $this->data['users'] = array_column($this->users_m->get(['employee_status' => 1])->result_array(), 'name', 'id');
        $this->data['projects'] = array_column($this->projects_m->get(['status' => 1])->result_array(), 'title', 'id');

        // Set page data
        $this->data['page_title'] = 'Add Project Assignment';
        $this->data['page_name'] = 'project_assign/add';

        $this->load->view('app/index', $this->data);
    }

    /**
     * Edit a project assignment
     *
     * @param int $id Assignment ID
     */
    public function edit($id) {
        $assignment = $this->project_assign_m->get_assignment($id);

        if (!$assignment) {
            set_alert('message_error', 'Project assignment not found!');
            redirect('app/project_assign/index');
        }

        if ($this->input->post()) {
            $data = [
                'job_date' => $this->input->post('job_date'),
                'user_id' => $this->input->post('user_id'),
                'project_id' => $this->input->post('project_id'),
                'status' => $this->input->post('status'),
                'remarks' => $this->input->post('remarks'),
                'updated_by' => $this->session->userdata('user_id'),
            ];

            $result = $this->project_assign_m->update_assignment($id, $data);

            if ($result) {
                set_alert('message_success', 'Project assignment updated successfully!');
            } else {
                set_alert('message_error', 'Failed to update project assignment!');
            }

            redirect('app/project_assign/index');
        }

        // Get data for form
        $this->data['assignment'] = $assignment;
        $this->data['users'] = array_column($this->users_m->get(['employee_status' => 1])->result_array(), 'name', 'id');
        $this->data['projects'] = array_column($this->projects_m->get(['status' => 1])->result_array(), 'title', 'id');

        // Set page data
        $this->data['page_title'] = 'Edit Project Assignment';
        $this->data['page_name'] = 'project_assign/edit';

        $this->load->view('app/index', $this->data);
    }

    /**
     * View a project assignment
     *
     * @param int $id Assignment ID
     */
    public function view($id) {
        $assignment = $this->project_assign_m->get_assignment($id);

        if (!$assignment) {
            set_alert('message_error', 'Project assignment not found!');
            redirect('app/project_assign/index');
        }

        // Set data for view
        $this->data['assignment'] = $assignment;

        // Set page data
        $this->data['page_title'] = 'View Project Assignment';
        $this->data['page_name'] = 'project_assign/view';

        $this->load->view('app/index', $this->data);
    }

    /**
     * Delete a project assignment
     *
     * @param int $id Assignment ID
     */
    public function delete($id) {
        $assignment = $this->project_assign_m->get_assignment($id);

        if (!$assignment) {
            set_alert('message_error', 'Project assignment not found!');
            redirect('app/project_assign/index');
        }

        $result = $this->project_assign_m->delete_assignment($id);

        if ($result) {
            set_alert('message_success', 'Project assignment deleted successfully!');
        } else {
            set_alert('message_error', 'Failed to delete project assignment!');
        }

        redirect('app/project_assign/index');
    }

    /**
     * Bulk assign projects
     */
    public function bulk_assign() {
        if ($this->input->post()) {
            $job_date = $this->input->post('job_date');
            $user_ids = $this->input->post('user_ids');
            $project_id = $this->input->post('project_id');
            $status = $this->input->post('status');
            $remarks = $this->input->post('remarks');

            $success_count = 0;

            foreach ($user_ids as $user_id) {
                $data = [
                    'job_date' => $job_date,
                    'user_id' => $user_id,
                    'project_id' => $project_id,
                    'status' => $status,
                    'remarks' => $remarks,
                    'created_by' => $this->session->userdata('user_id'),
                ];

                $result = $this->project_assign_m->insert_assignment($data);

                if ($result) {
                    $success_count++;
                }
            }

            if ($success_count > 0) {
                set_alert('message_success', $success_count . ' project assignments added successfully!');
            } else {
                set_alert('message_error', 'Failed to add project assignments!');
            }

            redirect('app/project_assign/index');
        }

        // Get data for form
        $this->data['users'] = $this->users_m->get(['employee_status' => 1])->result_array();
        $this->data['projects'] = array_column($this->projects_m->get(['status' => 1])->result_array(), 'title', 'id');

        // Set page data
        $this->data['page_title'] = 'Bulk Assign Projects';
        $this->data['page_name'] = 'project_assign/bulk_assign';

        $this->load->view('app/index', $this->data);
    }
    /**
     * Daily project assignment page
     */
    public function daily_assign() {
        // Get job date from GET parameter or use current date
        $job_date = $this->input->get('job_date') ?? date('Y-m-d');

        // Get active employees
        $users = $this->users_m->get(['employee_status' => 1, 'work_assign' => 1])->result_array();

        // Get active projects
        $projects = $this->projects_m->get(['status' => 1])->result_array();

        // Get existing project assignments for the selected date
        $where = ['job_date' => $job_date];
        $assignments = $this->project_assign_m->get_assignments($where)->result_array();

        // Organize assignments by user
        $user_assignments = [];
        foreach ($assignments as $assignment) {
            $user_assignments[$assignment['user_id']][] = $assignment;
        }

        // Set data for view
        $this->data['job_date'] = $job_date;
        $this->data['users'] = $users;
        $this->data['projects'] = array_column($projects, 'title', 'id');
        $this->data['user_assignments'] = $user_assignments;

        // Set page data
        $this->data['page_title'] = 'Daily Project Assignment';
        $this->data['page_name'] = 'project_assign/daily_assign';

        $this->load->view('app/index', $this->data);
    }



    /**
     * Save project assignment via AJAX
     */
    public function save_assignment() {
        $response = ['status' => 0, 'message' => 'Invalid request'];

        if ($this->input->post()) {
            $job_date = $this->input->post('job_date');
            $user_id = $this->input->post('user_id');
            $project_ids = $this->input->post('project_ids');
            $remarks = $this->input->post('remarks');

            // First, delete existing assignments for this user on this date
            $this->project_assign_m->delete_assignment(['job_date' => $job_date, 'user_id' => $user_id]);

            // Then add new assignments
            $success_count = 0;

            if (!empty($project_ids)) {
                foreach ($project_ids as $project_id) {
                    $data = [
                        'job_date' => $job_date,
                        'user_id' => $user_id,
                        'project_id' => $project_id,
                        'status' => 'pending',
                        'remarks' => $remarks,
                        'created_by' => $this->session->userdata('user_id'),
                    ];

                    $result = $this->project_assign_m->insert_assignment($data);

                    if ($result) {
                        $success_count++;
                    }
                }
            }

            if ($success_count > 0 || empty($project_ids)) {
                $response = ['status' => 1, 'message' => 'Project assignments saved successfully'];
            } else {
                $response = ['status' => 0, 'message' => 'Failed to save project assignments'];
            }
        }

        echo json_encode($response);
    }
}
