<?php
defined('BASEPATH') OR exit('No direct script access allowed');
date_default_timezone_set('Asia/Kolkata');
class Aptitude extends Frontend_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('aptitude_candidates_m');
        $this->load->model('aptitude_candidate_answers_m');
        $this->load->model('aptitude_questions_m');
        $this->load->helper('date');
    }

    public function index(){
        $this->load->view('public/aptitude/index');
    }

    /**
     * Submit test API endpoint
     * Handles the AJAX submission of the aptitude test
     */
    public function submit_test() {

        $allowed_origin = "https://interview.trogon.info";

        if (isset($_SERVER['HTTP_ORIGIN']) && $_SERVER['HTTP_ORIGIN'] === $allowed_origin) {
            header("Access-Control-Allow-Origin: $allowed_origin");
            header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
            header("Access-Control-Allow-Headers: Content-Type, Authorization");
            header("Access-Control-Allow-Credentials: true");
        }

        // Set headers for JSON response
        header('Content-Type: application/json');

        // Check if this is an AJAX request
        if (!$this->input->is_ajax_request() && !$this->input->post()) {
            echo json_encode(['status' => 'error', 'message' => 'Invalid request method']);
            return;
        }

        // Get POST data
        $fullname = $this->input->post('fullname');
        $email = $this->input->post('email');
        $phone = $this->input->post('phone');
        $district = $this->input->post('district');
        $location = $this->input->post('location');

        // Validate required fields
        if (empty($fullname) || empty($email) || empty($phone) || empty($district) || empty($location)) {
            echo json_encode(['status' => 'error', 'message' => 'All personal information fields are required']);
            return;
        }

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            echo json_encode(['status' => 'error', 'message' => 'Invalid email format']);
            return;
        }

        // Validate phone number (10 digits)
        if (!preg_match('/^[0-9]{10}$/', $phone)) {
            echo json_encode(['status' => 'error', 'message' => 'Phone number must be 10 digits']);
            return;
        }

        // Check if email already exists
        $existing_candidate = $this->aptitude_candidates_m->get(['email' => $email])->row();
        if ($existing_candidate) {
            echo json_encode(['status' => 'error', 'message' => 'This email has already been used for a test submission']);
            return;
        }

        // Start database transaction
        $this->db->trans_start();

        try {
            // Insert candidate data
            $candidate_data = [
                'fullname' => $fullname,
                'email' => $email,
                'phone' => $phone,
                'district' => $district,
                'location' => $location,
                'test_start_time' => $this->input->post('test_start_time') ?: date('Y-m-d H:i:s'),
                'test_end_time' => date('Y-m-d H:i:s'),
                'status' => 'completed',
                'created_at' => date('Y-m-d H:i:s')
            ];

            $candidate_id = $this->aptitude_candidates_m->insert($candidate_data);

            // Process answers from JSON array
            $answers = [];
            $answers_json = $this->input->post('answers');

            if ($answers_json) {
                $answers_data = json_decode($answers_json, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    // JSON parsing error
                    echo json_encode(['status' => 'error', 'message' => 'Invalid answers format']);
                    return;
                }

                if (!empty($answers_data) && is_array($answers_data)) {
                    foreach ($answers_data as $answer_item) {
                        if (isset($answer_item['question_no']) && isset($answer_item['answer'])) {
                            $answers[] = [
                                'candidate_id' => $candidate_id,
                                'question_id' => $answer_item['question_no'],
                                'answer' => $answer_item['answer']
                            ];
                        }
                    }
                }
            }

            // Insert all answers
            if (!empty($answers)) {
                $this->aptitude_candidate_answers_m->insert_batch($answers);
            }

            // Complete transaction
            $this->db->trans_complete();

            if ($this->db->trans_status() === FALSE) {
                // Transaction failed
                echo json_encode(['status' => 'error', 'message' => 'Database error occurred']);
                return;
            }

            // Success response
            echo json_encode(['status' => 'success', 'message' => 'Test submitted successfully']);

        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->trans_rollback();
            echo json_encode(['status' => 'error', 'message' => 'An error occurred: ' . $e->getMessage()]);
        }
    }
}