<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Home extends Frontend_Controller{
    public function __construct () {
        parent::__construct();
        header("Access-Control-Allow-Origin: http://localhost:63343"); // Adjust this if your frontend origin changes
        header("Access-Control-Allow-Headers: Content-Type");
        header("Access-Control-Allow-Methods: GET, POST, OPTIONS"); // Include other methods as needed

    }
    public function get_tasks(){
        
        $this->db->select(['id', 'title', 'description']);
        $this->db->order_by('id', 'desc');
        $this->db->limit(200);
        $tasks = $this->db->get('tasks')->result_array();
        // sleep(5);
        echo json_encode($tasks);
    }
	public function index()
	{
        $whmusername = 'root';
        $token = 'R8DKPA5A7O1NSP36TYW99WYILJA7KKLG';
        $query = "https://************:2087/json-api/listaccts?api.version=1";
        
        $curl = curl_init();                                    // Initialize a cURL session
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);          // Disable SSL verification for localhost
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);          // Disable SSL verification
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);          // Return the response as a string
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "Authorization: whm $whmusername:$token"
        ));
        curl_setopt($curl, CURLOPT_URL, $query);                // Set the URL
        
        $result = curl_exec($curl);                             // Execute the cURL session
        if ($result == false) {
            error_log('Curl error: ' . curl_error($curl));
            curl_close($curl);
            die('An error occurred while fetching the cPanel accounts.');
        }
        
        curl_close($curl);                                      // Close the cURL session
        
        $data = json_decode($result, true);                     // Decode JSON data
        echo json_encode($data);                                         // Print the array to see the structure


	}
	
	public function addon_domains(){
        $whmusername = 'root';
        $token = 'R8DKPA5A7O1NSP36TYW99WYILJA7KKLG';
        $cpusername = 'pmstrogon';  // Username of the cPanel account you want to check
        
        $query = "https://************:2087/json-api/cpanel?cpanel_jsonapi_user=$cpusername&cpanel_jsonapi_apiversion=2&cpanel_jsonapi_module=AddonDomain&cpanel_jsonapi_func=listaddondomains";
        
        $curl = curl_init();                                    // Initialize a cURL session
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);          // Disable SSL verification for localhost
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);          // Disable SSL verification
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);          // Return the response as a string
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "Authorization: whm $whmusername:$token"
        ));
        curl_setopt($curl, CURLOPT_URL, $query);                // Set the URL
        
        $result = curl_exec($curl);                             // Execute the cURL session
        if ($result == false) {
            error_log('Curl error: ' . curl_error($curl));
            curl_close($curl);
            die('An error occurred while fetching addon domains.');
        }
        
        curl_close($curl);                                      // Close the cURL session
        
        $data = json_decode($result, true);                     // Decode JSON data
        echo json_encode($data);
	}
    
}
