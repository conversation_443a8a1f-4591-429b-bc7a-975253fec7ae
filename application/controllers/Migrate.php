<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migrate extends CI_Controller {

    public function __construct() {
        parent::__construct();

        // Load the migration library
        $this->load->library('migration');

        // Check for API key if provided
        $api_key = $this->input->get('key');
        if ($api_key && $api_key === 'migration_secret_key') {
            // Allow access with valid API key regardless of environment
            return;
        }

        // Only allow this to be run in development environment without API key
        if (ENVIRONMENT !== 'development') {
            show_error('Migration is only available in development environment or with a valid API key.');
            exit;
        }
    }

    public function index() {
        // Check if this is an API request
        $is_api = $this->input->get('format') === 'json';

        // Run all migrations up to the current version set in config
        if ($this->migration->current() === FALSE) {
            $error_message = $this->migration->error_string();
            if ($is_api) {
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode([
                        'status' => 'error',
                        'message' => 'Migration failed: ' . $error_message
                    ]));
            } else {
                show_error($error_message);
            }
        } else {
            if ($is_api) {
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode([
                        'status' => 'success',
                        'message' => 'Migration completed successfully'
                    ]));
            } else {
                echo 'Migration completed successfully.';
            }
        }
    }

    public function reset() {
        // Check if this is an API request
        $is_api = $this->input->get('format') === 'json';

        // Reset the database to version 0 (empty)
        if ($this->migration->version(0) === FALSE) {
            $error_message = $this->migration->error_string();
            if ($is_api) {
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode([
                        'status' => 'error',
                        'message' => 'Migration reset failed: ' . $error_message
                    ]));
            } else {
                show_error($error_message);
            }
        } else {
            if ($is_api) {
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode([
                        'status' => 'success',
                        'message' => 'Migration reset successfully'
                    ]));
            } else {
                echo 'Migration reset successfully.';
            }
        }
    }

    public function to($version) {
        // Check if this is an API request
        $is_api = $this->input->get('format') === 'json';

        // Migrate to a specific version
        if ($this->migration->version($version) === FALSE) {
            $error_message = $this->migration->error_string();
            if ($is_api) {
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode([
                        'status' => 'error',
                        'message' => 'Migration to version ' . $version . ' failed: ' . $error_message
                    ]));
            } else {
                show_error($error_message);
            }
        } else {
            if ($is_api) {
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode([
                        'status' => 'success',
                        'message' => 'Migration to version ' . $version . ' completed successfully'
                    ]));
            } else {
                echo 'Migration to version ' . $version . ' completed successfully.';
            }
        }
    }
}
