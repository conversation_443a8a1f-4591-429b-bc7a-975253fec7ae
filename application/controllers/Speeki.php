<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON>eki extends Frontend_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->helper(array('form', 'url'));
        $this->load->library('upload');
    }
	
    public function upload_audio()
    {
        // Set CORS headers
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
        header("Access-Control-Allow-Headers: Content-Type, Authorization");
    
        // Check for OPTIONS method to handle preflight request
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            header("HTTP/1.1 200 OK");
            exit();
        }
        
        
        // Set upload path
        $config['upload_path']   = './uploads/speeki/'; 
        // Set allowed file types
        $config['allowed_types'] = '*'; 
        // Set max file size (in KB)
        $config['max_size']      = 10240; 
        $config['encrypt_name'] = true;

        // Initialize the upload library with the config
        $this->upload->initialize($config);

        if (!$this->upload->do_upload('audio')) {
            // If upload fails, return the error
            $error = array('error' => $this->upload->display_errors());
            echo json_encode(array('status' => 'error', 'message' => $error));
        } else {
            // If upload succeeds, return the file data
            $data = $this->upload->data();
            echo json_encode(array('status' => 'success', 'file_name' => base_url('uploads/speeki/'.$data['file_name'])));
        }
    }
    
}
