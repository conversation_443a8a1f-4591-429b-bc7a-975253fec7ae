<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Video_uploader extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        // Load any required libraries, helpers, etc.
        $this->load->library('curl');
    }

    public function index()
    {
        $videoUrl = $this->input->get('video_url');
        if (!$videoUrl) {
            $videoUrl = "https://player.vimeo.com/progressive_redirect/playback/966431402/rendition/720p/file.mp4?loc=external&signature=27a8e81bfa2f343a62e260ce9f09ebdb9722d0addc8c8868f8d88e7b5ba905e3";
        }

        // Fetch the video content from the URL
        $videoData = $this->fetchVideo($videoUrl);
        if (!$videoData) {
            echo "Failed to fetch video.";
            return;
        }

        // Push video to Cloudflare R2
        $result = $this->pushToCloudflare($videoData);
        echo $result ? "Upload successful." : "Failed to upload.";
    }

    private function fetchVideo($url)
    {
        $this->curl->create($url);
        $this->curl->option(CURLOPT_RETURNTRANSFER, TRUE);
        $this->curl->option(CURLOPT_BINARYTRANSFER, TRUE);
        $response = $this->curl->execute();

        return $response ? $response : false;
    }

    private function pushToCloudflare($videoData)
    {
        // Cloudflare R2 endpoint
        $endpoint = 'https://4d1c8bc90125c73e32267b50f661590f.r2.cloudflarestorage.com/trogon-video';
        $this->curl->create($endpoint);
        $this->curl->http_header('Authorization', 'Bearer 1b9Uz2NieDCXAiia63uU13cb5QpDcICdbSUbDzl3');
        $this->curl->option(CURLOPT_CUSTOMREQUEST, "PUT");
        $this->curl->option(CURLOPT_POSTFIELDS, $videoData);
        $response = $this->curl->execute();

        return $response ? true : false;
    }
}
