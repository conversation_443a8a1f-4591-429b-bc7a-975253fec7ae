<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Attendance_m extends MY_Model
{
    protected string $_table_name = 'attendance';
    function __construct() {
        parent::__construct();
    }

    public function get_attendance_data($date){
        $attendance = parent::get(['date' => $date])->result_array();
        $attendance_data = [];

        foreach ($attendance as $item){
            $attendance_data[$item['user_id']]['attendance'] = $item['attendance'];
            $attendance_data[$item['user_id']]['remarks'] = $item['remarks'];
            $attendance_data[$item['user_id']]['off_date'] = $item['off_date'];
        }
        return $attendance_data;
    }

    public function get_attendance_data_overview($from_date, $to_date){
        $attendance = parent::get(['date>=' => $from_date, 'date<=' => $to_date])->result_array();
        $attendance_data = [];

        foreach ($attendance as $item){
            $attendance_data[$item['user_id']][$item['date']]['attendance'] = $item['attendance'];
            $attendance_data[$item['user_id']][$item['date']]['remarks'] = $item['remarks'];
            $attendance_data[$item['user_id']][$item['date']]['off_date'] = $item['off_date'];
        }
        return $attendance_data;
    }

    // get employee attendance data
    public function get_employee_attendance_data($user_id, $from_date, $to_date){
        $attendance = parent::get(['date>=' => $from_date, 'date<=' => $to_date, 'user_id' => $user_id])->result_array();
        $attendance_data = [];

        foreach ($attendance as $item){
            $attendance_data[$item['date']]['attendance'] = $item['attendance'] ?? '';
            $attendance_data[$item['date']]['remarks'] = $item['remarks'] ?? '';
            $attendance_data[$item['date']]['off_date'] = $item['off_date'] ?? '';
        }
        return $attendance_data;
    }


    public function overview_employee_report($user_id, $start_date, $end_date){
        $attendance = parent::get(['date>=' => $start_date, 'date<=' => $end_date, 'user_id' => $user_id])->result_array();
        $attendance_data = [];

        foreach ($attendance as $item){
            $date = DateTime::createFromFormat('Y-m-d', $item['date']);
            $month = $date->format('Y-m');
            $day = $date->format('d');

            $attendance_data[$month]['data'][$day]['date'] = $item['date']?? '';
            $attendance_data[$month]['data'][$day]['attendance'] = $item['attendance'] ?? '';
            $attendance_data[$month]['data'][$day]['remarks'] = $item['remarks'] ?? '';
            $attendance_data[$month]['data'][$day]['off_date'] = $item['off_date'] ?? '';
        }
        return $attendance_data;
    }

    // Calculates the number of unique working days within a given date range.
    public function get_working_days($start_date, $end_date){
        $this->db->distinct('date');
        $attendance_days = parent::get(
            ['date>=' => $start_date, 'date<=' => $end_date],
            ['date']
        )->result_array();
        return count($attendance_days) ?? 0;
    }

    // get working days within a given date range.
    public function get_office_days($start_date, $end_date){
        $this->db->distinct('date');
        $attendance_days = parent::get(
            ['date>=' => $start_date, 'date<=' => $end_date],
            ['date']
        )->result_array();
        return array_column($attendance_days, 'date');
    }
}
