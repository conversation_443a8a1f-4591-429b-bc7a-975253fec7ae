<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: PMS
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: https://trogonmedia.com
 */

class Project_assign_m extends MY_Model {

    protected string $_table_name = 'project_assign';

    function __construct() {
        parent::__construct();
    }

    /**
     * Get project assignments with related data
     *
     * @param array $where Conditions for filtering
     * @param array $select Fields to select
     * @param array $order_by Order by clause
     * @param int $limit Limit results
     * @param int $offset Offset for pagination
     * @return object Result object
     */
    public function get_assignments($where = null, $select = null, $order_by = null, $limit = null, $offset = null) {
        $this->db->select('pa.*, u.name as user_name, p.title as project_title, c.name as creator_name, up.name as updater_name');
        $this->db->from($this->_table_name . ' as pa');
        $this->db->join('users as u', 'u.id = pa.user_id', 'left');
        $this->db->join('projects as p', 'p.id = pa.project_id', 'left');
        $this->db->join('users as c', 'c.id = pa.created_by', 'left');
        $this->db->join('users as up', 'up.id = pa.updated_by', 'left');

        if ($where) {
            $this->db->where($where);
        }

        if ($select) {
            $this->db->select($select, false);
        }

        if ($order_by) {
            if (is_array($order_by)) {
                $this->db->order_by($order_by['key'], $order_by['direction']);
            } else {
                $this->db->order_by('job_date', 'DESC');
            }
        } else {
            $this->db->order_by('job_date', 'DESC');
        }

        if ($limit) {
            $this->db->limit($limit, $offset);
        }

        return $this->db->get();
    }

    /**
     * Get a single project assignment with related data
     *
     * @param int $id Assignment ID
     * @return array Assignment data
     */
    public function get_assignment($id) {
        $result = $this->get_assignments(['pa.id' => $id]);
        return $result->num_rows() > 0 ? $result->row_array() : null;
    }

    /**
     * Insert a new project assignment
     *
     * @param array $data Assignment data
     * @return int Inserted ID
     */
    public function insert_assignment($data) {
        $data['created_on'] = date('Y-m-d H:i:s');
        return parent::insert($data);
    }

    /**
     * Update a project assignment
     *
     * @param int $id Assignment ID
     * @param array $data Updated data
     * @return bool Success status
     */
    public function update_assignment($id, $data) {
        $data['updated_on'] = date('Y-m-d H:i:s');
        return parent::update($data, ['id' => $id]);
    }

    /**
     * Delete a project assignment
     *
     * @param int|array $id Assignment ID or where conditions
     * @return bool Success status
     */
    public function delete_assignment($id) {
        if (is_array($id)) {
            return parent::delete($id);
        } else {
            return parent::delete(['id' => $id]);
        }
    }

    /**
     * Get assignments by date range
     *
     * @param string $start_date Start date (Y-m-d)
     * @param string $end_date End date (Y-m-d)
     * @param int $user_id Optional user ID filter
     * @param int $project_id Optional project ID filter
     * @return object Result object
     */
    public function get_assignments_by_date_range($start_date, $end_date, $user_id = null, $project_id = null) {
        $where = [
            'job_date >=' => $start_date,
            'job_date <=' => $end_date
        ];

        if ($user_id) {
            $where['pa.user_id'] = $user_id;
        }

        if ($project_id) {
            $where['pa.project_id'] = $project_id;
        }

        return $this->get_assignments($where);
    }
}
