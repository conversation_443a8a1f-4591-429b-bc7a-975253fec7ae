<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: PMS
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: https://trogonmedia.com
 */

class Ticket_history_m extends MY_Model
{
	protected string $_table_name = 'ticket_history';
	
	function __construct() {
		parent::__construct();
	}

	// Get ticket history with user details
	public function get_ticket_history($ticket_id) {
		$this->db->select('
			th.*,
			u.name as user_name,
			u.photo as user_photo
		');
		$this->db->from($this->_table_name . ' th');
		$this->db->join('users u', 'th.user_id = u.id', 'left');
		$this->db->where('th.ticket_id', $ticket_id);
		$this->db->order_by('th.created_at', 'DESC'); // Latest first

		return $this->db->get()->result_array();
	}

	// Get recent ticket activities
	public function get_recent_activities($limit = 10) {
		$this->db->select('
			th.*, 
			u.name as user_name,
			t.title as ticket_title
		');
		$this->db->from($this->_table_name . ' th');
		$this->db->join('users u', 'th.user_id = u.id', 'left');
		$this->db->join('tickets t', 'th.ticket_id = t.id', 'left');
		$this->db->order_by('th.created_at', 'DESC');
		$this->db->limit($limit);
		
		return $this->db->get()->result_array();
	}

	// Get user activity on tickets
	public function get_user_ticket_activities($user_id, $limit = 20) {
		$this->db->select('
			th.*, 
			t.title as ticket_title
		');
		$this->db->from($this->_table_name . ' th');
		$this->db->join('tickets t', 'th.ticket_id = t.id', 'left');
		$this->db->where('th.user_id', $user_id);
		$this->db->order_by('th.created_at', 'DESC');
		$this->db->limit($limit);
		
		return $this->db->get()->result_array();
	}
}