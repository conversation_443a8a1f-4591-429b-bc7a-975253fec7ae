<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: PMS
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: https://trogonmedia.com
 */

class Tickets_m extends MY_Model
{
    protected string $_table_name = 'tickets';
    
    function __construct() {
        parent::__construct();
    }

    /**
     * Get tickets with project and user information (with pagination support)
     */
    public function get_ticket_list($where = null, $limit = null, $offset = null) {
        $this->db->select('tickets.*, projects.title as project_title, users.name as assigned_user_name');
        $this->db->from('tickets');
        $this->db->join('projects', 'projects.id = tickets.project_id', 'left');
        $this->db->join('users', 'users.id = tickets.user_id', 'left');

        if ($where != null) {
            $this->db->where($where);
        }

        $this->db->order_by('tickets.id', 'desc');

        if ($limit !== null) {
            $this->db->limit($limit, $offset);
        }

        return $this->db->get()->result_array();
    }

    /**
     * Get total count of tickets (for pagination)
     */
    public function get_ticket_count($where = null) {
        $this->db->from('tickets');
        $this->db->join('projects', 'projects.id = tickets.project_id', 'left');
        $this->db->join('users', 'users.id = tickets.user_id', 'left');

        if ($where != null) {
            $this->db->where($where);
        }

        return $this->db->count_all_results();
    }

    /**
     * Get tickets for DataTables AJAX (optimized for large datasets)
     */
    public function get_tickets_datatable($request_data) {
        // First, get the total filtered count
        $total_records = $this->_get_filtered_count($request_data);

        // Then get the actual data
        $data = $this->_get_filtered_data($request_data);

        return [
            'data' => $data,
            'recordsTotal' => $this->get_total_tickets(),
            'recordsFiltered' => $total_records
        ];
    }

    /**
     * Get count of filtered tickets
     */
    private function _get_filtered_count($request_data) {
        $this->db->select('COUNT(*) as count');
        $this->db->from('tickets');
        $this->db->join('projects', 'projects.id = tickets.project_id', 'left');
        $this->db->join('users', 'users.id = tickets.user_id', 'left');

        $this->_apply_filters($request_data);
        $this->_apply_search($request_data);

        return $this->db->get()->row()->count;
    }

    /**
     * Get filtered ticket data
     */
    private function _get_filtered_data($request_data) {
        $this->db->select('tickets.id, tickets.title, tickets.description, tickets.type, tickets.priority,
                          tickets.status, tickets.ticket_date, tickets.reported_by, tickets.project_id, tickets.user_id,
                          projects.title as project_title, users.name as assigned_user_name');
        $this->db->from('tickets');
        $this->db->join('projects', 'projects.id = tickets.project_id', 'left');
        $this->db->join('users', 'users.id = tickets.user_id', 'left');

        $this->_apply_filters($request_data);
        $this->_apply_search($request_data);
        $this->_apply_ordering($request_data);

        // Apply pagination
        if (isset($request_data['length']) && $request_data['length'] != -1) {
            $this->db->limit($request_data['length'], $request_data['start']);
        }

        return $this->db->get()->result_array();
    }

    /**
     * Apply filters to query
     */
    private function _apply_filters($request_data) {
        if (!empty($request_data['filter_status'])) {
            $this->db->where('tickets.status', $request_data['filter_status']);
        }

        if (!empty($request_data['filter_priority'])) {
            $this->db->where('tickets.priority', $request_data['filter_priority']);
        }

        if (!empty($request_data['filter_project'])) {
            $this->db->where('tickets.project_id', $request_data['filter_project']);
        }

        if (!empty($request_data['filter_assigned'])) {
            if ($request_data['filter_assigned'] === 'unassigned') {
                $this->db->where('tickets.user_id IS NULL');
            } else {
                $this->db->where('tickets.user_id', $request_data['filter_assigned']);
            }
        }

        if (!empty($request_data['filter_date_from'])) {
            $this->db->where('(tickets.ticket_date IS NOT NULL AND DATE(tickets.ticket_date) >=', $request_data['filter_date_from'] . ')');
        }

        if (!empty($request_data['filter_date_to'])) {
            $this->db->where('(tickets.ticket_date IS NOT NULL AND DATE(tickets.ticket_date) <=', $request_data['filter_date_to'] . ')');
        }
    }

    /**
     * Apply search to query
     */
    private function _apply_search($request_data) {
        if (!empty($request_data['search']['value'])) {
            $search_value = $request_data['search']['value'];
            $this->db->group_start();

            // Check if search is for ticket ID (TKT format)
            if (preg_match('/^TKT(\d+)$/i', $search_value, $matches)) {
                $this->db->where('tickets.id', $matches[1]);
            } else {
                $this->db->like('tickets.title', $search_value);
                $this->db->or_like('tickets.description', $search_value);
                $this->db->or_like('projects.title', $search_value);
                $this->db->or_like('users.name', $search_value);
                $this->db->or_like('tickets.reported_by', $search_value);
                $this->db->or_like('CONCAT("TKT", tickets.id)', $search_value);
            }

            $this->db->group_end();
        }
    }

    /**
     * Apply ordering to query
     */
    private function _apply_ordering($request_data) {
        $columns = ['tickets.id', 'tickets.title', 'projects.title', 'tickets.type',
                   'tickets.priority', 'tickets.status', 'users.name', 'tickets.ticket_date', 'tickets.reported_by'];

        if (isset($request_data['order'][0]['column']) && isset($columns[$request_data['order'][0]['column']])) {
            $order_column = $columns[$request_data['order'][0]['column']];
            $order_dir = $request_data['order'][0]['dir'] ?? 'desc';
            $this->db->order_by($order_column, $order_dir);
        } else {
            // Default: Show latest tickets first (highest ID first)
            $this->db->order_by('tickets.id', 'desc');
        }
    }

    /**
     * Get total tickets count (cached for performance)
     */
    public function get_total_tickets() {
        return $this->db->count_all('tickets');
    }



    /**
     * Get ticket status counts for dashboard (optimized for large datasets)
     */
    public function get_status_count($tickets = null) {
        // If tickets array is provided, use the old method for backward compatibility
        if ($tickets !== null) {
            $status_count = [
                'new_count' => 0,
                'assigned_count' => 0,
                'closed_count' => 0,
                'on_hold_count' => 0,
                're_open_count' => 0,
            ];

            foreach ($tickets as $ticket) {
                switch ($ticket['status']) {
                    case 'new':
                        $status_count['new_count']++;
                        break;
                    case 'assigned':
                        $status_count['assigned_count']++;
                        break;
                    case 'closed':
                        $status_count['closed_count']++;
                        break;
                    case 'on_hold':
                        $status_count['on_hold_count']++;
                        break;
                    case 're_open':
                        $status_count['re_open_count']++;
                        break;
                }
            }
            return $status_count;
        }

        // Optimized version using database aggregation
        $this->db->select('status, COUNT(*) as count');
        $this->db->from('tickets');
        $this->db->group_by('status');
        $results = $this->db->get()->result_array();

        $status_count = [
            'new_count' => 0,
            'assigned_count' => 0,
            'closed_count' => 0,
            'on_hold_count' => 0,
            're_open_count' => 0,
        ];

        foreach ($results as $result) {
            switch ($result['status']) {
                case 'new':
                    $status_count['new_count'] = (int)$result['count'];
                    break;
                case 'assigned':
                    $status_count['assigned_count'] = (int)$result['count'];
                    break;
                case 'closed':
                    $status_count['closed_count'] = (int)$result['count'];
                    break;
                case 'on_hold':
                    $status_count['on_hold_count'] = (int)$result['count'];
                    break;
                case 're_open':
                    $status_count['re_open_count'] = (int)$result['count'];
                    break;
            }
        }

        return $status_count;
    }

    /**
     * Get tickets by project id
     */
    public function get_tickets_by_project($project_id) {
        return $this->get_ticket_list(['tickets.project_id' => $project_id]);
    }

    /**
     * Get tickets assigned to user
     */
    public function get_tickets_by_user($user_id) {
        return $this->get_ticket_list(['tickets.user_id' => $user_id]);
    }

    /**
     * Get priority wise ticket counts (optimized for large datasets)
     */
    public function get_priority_count($tickets = null) {
        // If tickets array is provided, use the old method for backward compatibility
        if ($tickets !== null) {
            $priority_count = [
                'high_count' => 0,
                'medium_count' => 0,
                'low_count' => 0,
            ];

            foreach ($tickets as $ticket) {
                switch ($ticket['priority']) {
                    case 'high':
                        $priority_count['high_count']++;
                        break;
                    case 'medium':
                        $priority_count['medium_count']++;
                        break;
                    case 'low':
                        $priority_count['low_count']++;
                        break;
                }
            }
            return $priority_count;
        }

        // Optimized version using database aggregation
        $this->db->select('priority, COUNT(*) as count');
        $this->db->from('tickets');
        $this->db->group_by('priority');
        $results = $this->db->get()->result_array();

        $priority_count = [
            'high_count' => 0,
            'medium_count' => 0,
            'low_count' => 0,
        ];

        foreach ($results as $result) {
            switch ($result['priority']) {
                case 'high':
                    $priority_count['high_count'] = (int)$result['count'];
                    break;
                case 'medium':
                    $priority_count['medium_count'] = (int)$result['count'];
                    break;
                case 'low':
                    $priority_count['low_count'] = (int)$result['count'];
                    break;
            }
        }

        return $priority_count;
    }

    /**
     * Get tickets overview for different time periods
     */
    public function get_tickets_overview() {
        $overview = [
            'all_time' => $this->get_all_time_overview(),
            'today' => $this->get_period_overview('today'),
            'yesterday' => $this->get_period_overview('yesterday'),
            'last_7_days' => $this->get_period_overview('last_7_days'),
            'last_30_days' => $this->get_period_overview('last_30_days')
        ];

        return $overview;
    }

    /**
     * Get all-time ticket overview
     */
    private function get_all_time_overview() {
        $this->db->select('
            COUNT(*) as total,
            SUM(CASE WHEN status = "closed" THEN 1 ELSE 0 END) as closed,
            SUM(CASE WHEN status = "assigned" THEN 1 ELSE 0 END) as assigned,
            SUM(CASE WHEN user_id IS NULL OR user_id = 0 THEN 1 ELSE 0 END) as not_assigned
        ');
        $this->db->from('tickets');

        $result = $this->db->get()->row_array();

        // Calculate average resolution time for all closed tickets
        $avg_resolution_time = $this->get_average_resolution_time_all_time();

        return [
            'total' => (int)($result['total'] ?? 0),
            'closed' => (int)($result['closed'] ?? 0),
            'assigned' => (int)($result['assigned'] ?? 0),
            'not_assigned' => (int)($result['not_assigned'] ?? 0),
            'avg_resolution_time' => $avg_resolution_time
        ];
    }

    /**
     * Get ticket overview for a specific period
     */
    private function get_period_overview($period) {
        $date_conditions = $this->get_date_conditions($period);

        // Get tickets created in the period
        $this->db->select('
            COUNT(*) as created,
            SUM(CASE WHEN status = "closed" THEN 1 ELSE 0 END) as closed,
            SUM(CASE WHEN status = "assigned" THEN 1 ELSE 0 END) as assigned,
            SUM(CASE WHEN user_id IS NULL OR user_id = 0 THEN 1 ELSE 0 END) as not_assigned
        ');
        $this->db->from('tickets');
        $this->db->where($date_conditions['created']);

        $result = $this->db->get()->row_array();

        // Calculate average resolution time for tickets closed in this period
        $avg_resolution_time = $this->get_average_resolution_time_period($period);

        return [
            'created' => (int)($result['created'] ?? 0),
            'closed' => (int)($result['closed'] ?? 0),
            'assigned' => (int)($result['assigned'] ?? 0),
            'not_assigned' => (int)($result['not_assigned'] ?? 0),
            'avg_resolution_time' => $avg_resolution_time
        ];
    }

    /**
     * Get date conditions for different periods
     */
    private function get_date_conditions($period) {
        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $week_ago = date('Y-m-d', strtotime('-7 days'));
        $month_ago = date('Y-m-d', strtotime('-30 days'));

        switch ($period) {
            case 'today':
                return [
                    'created' => "DATE(created_on) = '$today'"
                ];

            case 'yesterday':
                return [
                    'created' => "DATE(created_on) = '$yesterday'"
                ];

            case 'last_7_days':
                return [
                    'created' => "DATE(created_on) >= '$week_ago' AND DATE(created_on) <= '$today'"
                ];

            case 'last_30_days':
                return [
                    'created' => "DATE(created_on) >= '$month_ago' AND DATE(created_on) <= '$today'"
                ];

            default:
                return [
                    'created' => "DATE(created_on) = '$today'"
                ];
        }
    }

    /**
     * Calculate average resolution time for all closed tickets
     */
    private function get_average_resolution_time_all_time() {
        $this->db->select('
            AVG(TIMESTAMPDIFF(SECOND, created_on, close_date)) as avg_seconds,
            COUNT(*) as closed_count
        ');
        $this->db->from('tickets');
        $this->db->where('status', 'closed');
        $this->db->where('created_on IS NOT NULL');
        $this->db->where('close_date IS NOT NULL');
        $this->db->where('close_date !=', '0000-00-00 00:00:00');

        $result = $this->db->get()->row_array();

        if (!$result || $result['closed_count'] == 0 || !$result['avg_seconds']) {
            return 'N/A';
        }

        return $this->format_resolution_time_from_seconds($result['avg_seconds']);
    }

    /**
     * Calculate average resolution time for tickets created in a specific period
     */
    private function get_average_resolution_time_period($period) {
        $date_conditions = $this->get_date_conditions($period);

        $this->db->select('
            AVG(TIMESTAMPDIFF(SECOND, created_on, close_date)) as avg_seconds,
            COUNT(*) as closed_count
        ');
        $this->db->from('tickets');
        $this->db->where('status', 'closed');
        $this->db->where('created_on IS NOT NULL');
        $this->db->where('close_date IS NOT NULL');
        $this->db->where('close_date !=', '0000-00-00 00:00:00');
        // Filter by creation date (when ticket was created), not close date
        $this->db->where($date_conditions['created']);

        $result = $this->db->get()->row_array();

        if (!$result || $result['closed_count'] == 0 || !$result['avg_seconds']) {
            return 'N/A';
        }

        return $this->format_resolution_time_from_seconds($result['avg_seconds']);
    }

    /**
     * Format resolution time from seconds to human readable format
     */
    private function format_resolution_time_from_seconds($seconds) {
        if (!$seconds || $seconds <= 0) {
            return 'N/A';
        }

        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        if ($days > 0) {
            if ($hours > 0 || $minutes > 0) {
                return $days . 'd ' . $hours . 'h ' . $minutes . 'm';
            }
            return $days . ' day' . ($days > 1 ? 's' : '');
        } else if ($hours > 0) {
            if ($minutes > 0) {
                return $hours . 'h ' . $minutes . 'm';
            }
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        } else if ($minutes > 0) {
            return $minutes . ' min';
        } else {
            return '< 1 min';
        }
    }
}