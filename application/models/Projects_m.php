<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Projects_m extends MY_Model
{
    protected string $_table_name = 'projects';
    function __construct() {
        parent::__construct();
    }

    public function get_project_list() {

        $this->load->model('tasks_m');
        $this->db->order_by('id', 'desc');
        $projects = parent::get()->result_array();
        foreach ($projects as $key => $project){
             // get total number of tasks
            $total_tasks = $this->db->get_where('tasks', ['task_status!=' => 'on_hold', 'project_id' => $project['id']])->num_rows();

            // get total completed or testing
            $this->db->where_in('task_status', ['completed', 'testing']);
            $this->db->where('project_id', $project['id']);
            $completed_tasks = $this->db->get('tasks')->num_rows();

            // pending tasks
            $pending_tasks = $this->db->get_where('tasks', ['task_status' => 'pending', 'project_id' => $project['id']])->num_rows();

            // assigned tasks
            $assigned_tasks = $this->db->get_where('tasks', ['task_status' => 'assigned', 'project_id' => $project['id']])->num_rows();

            if ($total_tasks > 0){
                $progress = ($completed_tasks/$total_tasks)*100;
            }else{
                $progress = 0;
            }

            $projects[$key]['tasks']['total'] = $total_tasks;
            $projects[$key]['tasks']['pending'] = $pending_tasks;
            $projects[$key]['tasks']['assigned'] = $assigned_tasks;
            $projects[$key]['tasks']['completed'] = $completed_tasks;
            $projects[$key]['tasks']['progress'] = number_format($progress);
        }

//        usort($projects, function($a, $b) {
//            return $a['tasks']['progress'] <=> $b['tasks']['progress'];
//        });
        return $projects;
    }

    public function get_status_count($projects){
        $status_count = [
            'pending_count' => 0,
            'assigned_count' => 0,
            'completed_count' => 0,
        ];

        foreach ($projects as $project) {
            if ($project['tasks']['progress'] == 0) {
                $status_count['pending_count']++;
            }elseif ($project['tasks']['progress'] == 100){
                $status_count['completed_count']++;
            }else{
                $status_count['assigned_count']++;
            }
        }

        return $status_count;
    }

    // get project list
    public function get_projects_by_team_id($team_id){
        $this->db->select('id, title, project_type');
        $this->db->where("JSON_CONTAINS(project_teams, '\"$team_id\"')");
        return $this->db->get('projects')->result_array();
    }

}
