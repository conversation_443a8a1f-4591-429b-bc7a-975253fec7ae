<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Users_m extends MY_Model
{
	/*
		| -----------------------------------------------------
		| PRODUCT NAME: 	ECOPEN
		| -----------------------------------------------------
		| AUTHOR:			TROGON MEDIA PVT LTD
		| -----------------------------------------------------
		| EMAIL:			<EMAIL>
		| -----------------------------------------------------
		| COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
		| -----------------------------------------------------
		| WEBSITE:			http://trogonmedia.com
		| -----------------------------------------------------
		*/
	protected string $_table_name = 'users';
	function __construct() {
		parent::__construct();
	}
	
	public function get_task_assign_users(){
	    if (is_app_lead()){
            return $this->get_flutter_developers();
	    }elseif (get_user_id() == 48){
            return $this->get_web_designers();
	    }elseif (!has_permission('tasks/index')){
            return $this->get(['id' => get_user_id()])->result_array();
	    }elseif (is_technical_support()){
//            return $this->get(['id' => get_user_id()])->result_array();
            return $this->get_all_employees();
	    }else{
	        return $this->get_all_employees();
	    }
	}
	
	// get all employees
	public function get_all_employees(){
	    $this->db->where('employee_status', 1);
	    $this->db->where('is_employee', 1);
        $this->db->order_by('name', 'ASC');
	    return $this->get()->result_array();
	}
	
	// get web designers
	public function get_web_designers(){
	    $this->db->where_in('id', ['48', '20']);
	    return $this->get()->result_array();
	}
	
	// get android developers
	public function get_android_developers(){
	    $this->db->where_in('id', ['4', '17', '18']);
	    return $this->get()->result_array();
	}
	
	// get flutter developers
	public function get_flutter_developers(){
	    $this->db->where_in('id', ['5', '15', '50', '18']);
	    return $this->get()->result_array();
	}
	
	// get php developers
	public function get_php_developers(){
	    $this->db->where_in('id', ['7', '10', '16', '19', '21', '22', '24', '29']);
	    return $this->get()->result_array();
	}
	
	// get testers
	public function get_testers(){
	    $this->db->where_in('id', ['28', '30']);
	    return $this->get()->result_array();
	}

}
