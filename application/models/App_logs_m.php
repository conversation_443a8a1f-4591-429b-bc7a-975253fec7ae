<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class App_logs_m extends MY_Model
{
    protected string $_table_name = 'app_logs';
	function __construct() {
		parent::__construct();
	}
	
	public function create_log(){
	    if(!is_mobile()){
	        $data = [
	                'date' => date('Y-m-d'),
	                'log_time' => date('Y-m-d H:i:s'),
	                'user_id' => get_user_id(),
	                'created_by' => get_user_id(),
	                'updated_by' => get_user_id(),
	                'created_on' => date('Y-m-d H:i:s'),
	                'updated_on' => date('Y-m-d H:i:s')
	            ];
	       $this->insert($data);
	    }
	}
	
	public function get_log_in_time($user_id, $date){
	    $this->db->limit(1);
	    $this->db->order_by('log_time', 'ASC');
	    $app_log = $this->get(['user_id' => $user_id, 'date' => $date])->row();
	    
	    if(!empty($app_log)){
	        $app_log_time = DateTime::createFromFormat('Y-m-d H:i:s', $app_log->log_time)->format('H:i:s');
	    
    	    if($app_log_time >= '09:40:00'){
    	        $is_late = true;
    	    }else{
    	        $is_late = false;
    	    }
    	    
    	    // printable time
    	    $print_time = DateTime::createFromFormat('H:i:s', $app_log_time)->format('g:i A');
    	    
    	   return [
    	       'is_late' => $is_late,
    	       'log_in_time' => $app_log_time,
    	       'print_time' => $print_time
    	       ];
	    }else{
	        return false;
	    }
	    
	}

}