<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Work_assign_m extends MY_Model
{
    protected string $_table_name = 'work_assign';
    function __construct() {
        parent::__construct();
    }

    public function get_jobs_by_date($job_date, $job_date_arr = []) {
        //users
        $this->db->order_by('name', 'ASC');
        $users = $this->users_m->get(
            ['employee_status' => 1, 'work_assign' => 1],
            ['id', 'name', 'phone', 'employee_code'],
        )->result_array();

        // tasks
        if(!empty($job_date_arr)){
            $this->db->where_in('due_date', $job_date_arr);
        }else{
            $this->db->where('due_date', $job_date);
        }
        $tasks = $this->tasks_m->get(
            ['task_status' => 'assigned'],
            ['id', 'title', 'user_id', 'project_id', 'due_date']
        )->result_array();

        // work assign status
        $this->db->where('job_date', $job_date);
        $work_assign = $this->get()->result_array();
        $work_assign_status = [];
        $work_assign_remarks = [];
        $is_support = [];
        foreach ($work_assign as $item){
            $work_assign_status[$item['user_id']] = $item['work_status'];
            $work_assign_remarks[$item['user_id']] = $item['remarks'];
            $is_support[$item['user_id']] = $item['is_support'];
        }

        $tasks_data = [];
        foreach ($tasks as $task) {
            $tasks_data[$task['user_id']][$task['project_id']][] = $task;
        }

        foreach ($users as $key => $user){
            $users[$key]['tasks'] =  $tasks_data[$user['id']];
            $users[$key]['work_status'] =  $work_assign_status[$user['id']];
            $users[$key]['remarks'] =  $work_assign_remarks[$user['id']];
            $users[$key]['is_support'] =  $is_support[$user['id']];
        }

        return $users;
    }

    public function get_job_attendance_data($start_date, $end_date){
        $attendance = $this->get(['job_date >=' => $start_date, 'job_date <=' => $end_date])->result_array();
        $attendance_data = [];
        foreach ($attendance as $item){
            $attendance_data[$item['user_id']][$item['job_date']] = $item['work_status'];
        }
        return $attendance_data;
    }

}
