<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Work_report_m extends MY_Model
{
    function __construct() {
        $this->load->model('tasks_m');
        $this->load->model('task_assign_m');
        $this->load->model('projects_m');

        parent::__construct();
    }

    public function overview_report($start_date, $end_date){
        $task_history = $this->task_assign_m->get(['job_date >=' => $start_date, 'job_date <=' => $end_date])->result_array();
        $task_history_report = [];
        $task_array = [];
        foreach ($task_history as $history){
            if (!isset($task_array[$history['task_id']])){
                $task_array[$history['task_id']] = $this->tasks_m->get(['id' => $history['task_id']])->row_array();
            }
            $task_history_report[$history['user_id']]['jobs'][$history['task_id']]['details'] = $task_array[$history['task_id']];
            $task_history_report[$history['user_id']]['jobs'][$history['task_id']]['history'][] = $history;
        }
        return $task_history_report;
    }

    // get time sheet
    public function get_time_sheet_overview($start_date, $end_date){
        $task_history = $this->task_assign_m->get(
            ['job_date >=' => $start_date, 'job_date <=' => $end_date],
            [],
            ['key' => 'start_time', 'direction' => 'ASC']
        )->result_array();

        $time_sheet = [];
        $task_array = [];

        foreach ($task_history as $history){
            if(empty($history['start_time']) || empty($history['end_time'])){
                continue;
            }
            if (!isset($task_array[$history['task_id']])){
                $task_array[$history['task_id']] = $this->tasks_m->get(['id' => $history['task_id']])->row_array();
            }
            $history['task_details'] = $task_array[$history['task_id']];
            $time_sheet[$history['user_id']]['jobs'][] = $history;
        }
        return $time_sheet;
    }

    public function overview_report_data($start_date, $end_date){
        $task_history = $this->task_assign_m->get(['job_date >=' => $start_date, 'job_date <=' => $end_date])->result_array();
        $task_history_report = [];
        $task_array = [];
        foreach ($task_history as $history){
            if(empty($history['start_time']) || empty($history['end_time'])){
                continue;
            }
            
            if (!isset($task_array[$history['task_id']])){
                $task_array[$history['task_id']] = $this->tasks_m->get(['id' => $history['task_id']])->row_array();
            }
            $task_history_report[$history['user_id']]['jobs'][$history['job_date']][$history['task_id']]['details'] = $task_array[$history['task_id']];
            $task_history_report[$history['user_id']]['jobs'][$history['job_date']][$history['task_id']]['history'][] = $history;
        }
        return $task_history_report;
    }

    public function employee_report($user_id, $start_date, $end_date, $project_id = 0){

        if ($project_id > 0){
            $task_ids = array_column($this->db->select('id')->get_where('tasks', ['project_id' => $project_id])->result_array(), 'id');
            if (count($task_ids)){
                $this->db->where_in('task_assign.task_id', $task_ids);
            }
        }

        $task_history = $this->task_assign_m->get(
            ['job_date >=' => $start_date,
            'job_date <=' => $end_date,
            'user_id' => $user_id]
        )->result_array();
        $task_history_report = [];
        $task_array = [];
        foreach ($task_history as $history){
            if (!isset($task_array[$job_history['task_id']])){
                $task_array[$history['task_id']] = $this->tasks_m->get(['id' => $history['task_id']])->row_array();
            }
            $task_history_report[$history['job_date']]['jobs'][$history['task_id']]['details'] = $task_array[$history['task_id']];
            $task_history_report[$history['job_date']]['jobs'][$history['task_id']]['history'][] = $history;
        }
        return $task_history_report;
    }

    public function get_time_sheet($user_id, $job_date){
        $time_sheet_data = [];
        $task_history = $this->task_assign_m->get(
            ['job_date' => $job_date, 'user_id' => $user_id], [], ['key' => 'start_time', 'direction' => 'ASC']
        )->result_array();
        foreach ($task_history as $item){
            if (empty($item['start_time'])){
                continue;
            }
            $task = $this->db->get_where('tasks', ['id' => $item['task_id']])->row();
            $project = $this->db->get_where('projects', ['id' => $task->project_id])->row();
            $time_sheet_data[] = [
                'item_id' => $item['id'],
                'start' => DateTime::createFromFormat('H:i:s', $item['start_time'])->format('g:i A'),
                'end' => DateTime::createFromFormat('H:i:s', $item['end_time'])->format('g:i A'),
                'type' => 'work',
                'jobId' => '#'.$item['task_id'],
                'project' => "[$project->title] - [{$item['task_id']}] - $task->title",
            ];
        }
        return $time_sheet_data;
    }


    // get monthly overview calendar
    public function get_monthly_overview_calendar($user_id, $month_year) {
        $month_year = DateTime::createFromFormat('m-Y', $month_year)->format('Y-m');

        // get task report
        $this->db->select('time_taken, job_date');
        $this->db->from('task_assign');
        $this->db->where('user_id', $user_id);
        $this->db->where('time_taken!=', null);
        $this->db->where('time_taken!=', '00:00:00');
        $this->db->like('job_date', $month_year);
        $task_report = $this->db->get()->result_array();
        $task_report_array = [];

        foreach ($task_report as $task_report_item){
            $day = (int) DateTime::createFromFormat('Y-m-d', $task_report_item['job_date'])->format('d');
            $task_report_array[$day][] = $task_report_item['time_taken'];
        }

        $task_duration = [];
        foreach ($task_report_array as $day => $item){
            if (count($item)){
                $task_duration[$day] = add_duration($item, 'seconds');
            }
        }

        // get work attendance
        $this->db->select('work_status, job_date');
        $this->db->from('work_assign');
        $this->db->where('user_id', $user_id);
        $this->db->like('job_date', $month_year);
        $attendance = $this->db->get()->result_array();
        $attendance_array = [];
        foreach ($attendance as $attendance_item){
            $day = (int) DateTime::createFromFormat('Y-m-d', $attendance_item['job_date'])->format('d');
            $attendance_array[$day] = $attendance_item['work_status'];
        }

        // get month day array
        list($year, $month) = explode('-', $month_year);
        $numDays = cal_days_in_month(CAL_GREGORIAN, $month, $year);

        $month_days = [];
        for ($day = 1; $day <= $numDays; $day++) {
            $month_days[] = (int) $day;
        }

        // calculate monthly calendar
        $work_calendar = [
            'updated' => [],
            'partially' => [],
            'not_updated' => [],
            'not_applicable' => [],
        ];
        
        $current_day = (int) date('d');
        $current_month = date('Y-m');
        
        foreach ($month_days as $day){
            // check if attendance
            if($current_month == $month_year && $day > $current_day){
                $work_calendar['not_applicable'][] = $day;
            }else{
                if (isset($attendance_array[$day]) && $attendance_array[$day] == 'working'){
                    if (isset($task_duration[$day])){
                        if ($task_duration[$day] < 25200){
                            $work_calendar['partially'][] = $day;
                        }else{
                            $work_calendar['updated'][] = $day;
                        }
                    }else{
                        $work_calendar['not_updated'][] = $day;
                    }
                }else{
                    $work_calendar['not_applicable'][] = $day;
                }
            }
            
        }

        return $work_calendar;
    }

    // Get employee status report for date range
    public function get_employee_status_report($start_date, $end_date) {
        // Get all users with employee_status = 1 and work_report = 1
        $users = $this->users_m->get([
            'employee_status' => 1, 'work_report' => 1
        ], ['id', 'name'], ['key' => 'name', 'direction' => 'ASC'])->result_array();

        // Get work assign data for the date range
        $work_assign_data = $this->work_assign_m->get([
            'job_date >=' => $start_date,
            'job_date <=' => $end_date
        ])->result_array();

        // Get task assign data for the date range
        $this->db->select('user_id, job_date, time_taken');
        $this->db->where('job_date >=', $start_date);
        $this->db->where('job_date <=', $end_date);
        $this->db->where('time_taken IS NOT NULL');
        $this->db->where('time_taken !=', '00:00:00');
        $task_assign_data = $this->db->get('task_assign')->result_array();

        // Process work assign data
        $work_status_array = [];
        foreach ($work_assign_data as $work_assign) {
            $work_status_array[$work_assign['user_id']][$work_assign['job_date']] = $work_assign['work_status'];
        }

        // Process task assign data to calculate daily work hours
        $daily_work_hours = [];
        foreach ($task_assign_data as $task_assign) {
            if (!isset($daily_work_hours[$task_assign['user_id']][$task_assign['job_date']])) {
                $daily_work_hours[$task_assign['user_id']][$task_assign['job_date']] = 0;
            }
            $daily_work_hours[$task_assign['user_id']][$task_assign['job_date']] += time_to_seconds($task_assign['time_taken']);
        }

        // Generate date array
        $date_array = get_date_array($start_date, $end_date);

        $status_report = [];

        foreach ($users as $user) {
            $user_id = $user['id'];
            $status_report[$user_id] = [
                'user_info' => $user,
                'daily_status' => [],
                'summary' => [
                    'total_hours' => 0,
                    'days_updated' => 0,
                    'days_partially_updated' => 0,
                    'days_not_updated' => 0,
                    'days_not_applicable' => 0
                ]
            ];

            foreach ($date_array as $date) {
                $work_status = $work_status_array[$user_id][$date] ?? null;
                $work_hours = $daily_work_hours[$user_id][$date] ?? 0;

                $status = 'not_applicable'; // Default status

                if ($work_status == 'working') {
                    if ($work_hours >= 25200) { // 7 hours = 25200 seconds
                        $status = 'updated';
                        $status_report[$user_id]['summary']['days_updated']++;
                    } elseif ($work_hours > 0) {
                        $status = 'partially_updated';
                        $status_report[$user_id]['summary']['days_partially_updated']++;
                    } else {
                        $status = 'not_updated';
                        $status_report[$user_id]['summary']['days_not_updated']++;
                    }
                } else {
                    // work_status is 'absent', 'off', or null
                    $status_report[$user_id]['summary']['days_not_applicable']++;
                }

                $status_report[$user_id]['daily_status'][$date] = [
                    'status' => $status,
                    'work_status' => $work_status,
                    'work_hours' => $work_hours,
                    'work_hours_formatted' => format_time_from_seconds($work_hours)
                ];

                $status_report[$user_id]['summary']['total_hours'] += $work_hours;
            }

            // Format total hours
            $status_report[$user_id]['summary']['total_hours_formatted'] = format_time_from_seconds($status_report[$user_id]['summary']['total_hours']);
        }

        return $status_report;
    }

}
