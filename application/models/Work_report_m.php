<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Work_report_m extends MY_Model
{
    function __construct() {
        $this->load->model('tasks_m');
        $this->load->model('task_assign_m');
        $this->load->model('projects_m');

        parent::__construct();
    }

    public function overview_report($start_date, $end_date){
        $task_history = $this->task_assign_m->get(['job_date >=' => $start_date, 'job_date <=' => $end_date])->result_array();
        $task_history_report = [];
        $task_array = [];
        foreach ($task_history as $history){
            if (!isset($task_array[$history['task_id']])){
                $task_array[$history['task_id']] = $this->tasks_m->get(['id' => $history['task_id']])->row_array();
            }
            $task_history_report[$history['user_id']]['jobs'][$history['task_id']]['details'] = $task_array[$history['task_id']];
            $task_history_report[$history['user_id']]['jobs'][$history['task_id']]['history'][] = $history;
        }
        return $task_history_report;
    }

    // get time sheet
    public function get_time_sheet_overview($start_date, $end_date){
        $task_history = $this->task_assign_m->get(
            ['job_date >=' => $start_date, 'job_date <=' => $end_date],
            [],
            ['key' => 'start_time', 'direction' => 'ASC']
        )->result_array();

        $time_sheet = [];
        $task_array = [];

        foreach ($task_history as $history){
            if(empty($history['start_time']) || empty($history['end_time'])){
                continue;
            }
            if (!isset($task_array[$history['task_id']])){
                $task_array[$history['task_id']] = $this->tasks_m->get(['id' => $history['task_id']])->row_array();
            }
            $history['task_details'] = $task_array[$history['task_id']];
            $time_sheet[$history['user_id']]['jobs'][] = $history;
        }
        return $time_sheet;
    }

    public function overview_report_data($start_date, $end_date){
        $task_history = $this->task_assign_m->get(['job_date >=' => $start_date, 'job_date <=' => $end_date])->result_array();
        $task_history_report = [];
        $task_array = [];
        foreach ($task_history as $history){
            if(empty($history['start_time']) || empty($history['end_time'])){
                continue;
            }
            
            if (!isset($task_array[$history['task_id']])){
                $task_array[$history['task_id']] = $this->tasks_m->get(['id' => $history['task_id']])->row_array();
            }
            $task_history_report[$history['user_id']]['jobs'][$history['job_date']][$history['task_id']]['details'] = $task_array[$history['task_id']];
            $task_history_report[$history['user_id']]['jobs'][$history['job_date']][$history['task_id']]['history'][] = $history;
        }
        return $task_history_report;
    }

    public function employee_report($user_id, $start_date, $end_date, $project_id = 0){

        if ($project_id > 0){
            $task_ids = array_column($this->db->select('id')->get_where('tasks', ['project_id' => $project_id])->result_array(), 'id');
            if (count($task_ids)){
                $this->db->where_in('task_assign.task_id', $task_ids);
            }
        }

        $task_history = $this->task_assign_m->get(
            ['job_date >=' => $start_date,
            'job_date <=' => $end_date,
            'user_id' => $user_id]
        )->result_array();
        $task_history_report = [];
        $task_array = [];
        foreach ($task_history as $history){
            if (!isset($task_array[$job_history['task_id']])){
                $task_array[$history['task_id']] = $this->tasks_m->get(['id' => $history['task_id']])->row_array();
            }
            $task_history_report[$history['job_date']]['jobs'][$history['task_id']]['details'] = $task_array[$history['task_id']];
            $task_history_report[$history['job_date']]['jobs'][$history['task_id']]['history'][] = $history;
        }
        return $task_history_report;
    }

    public function get_time_sheet($user_id, $job_date){
        $time_sheet_data = [];
        $task_history = $this->task_assign_m->get(
            ['job_date' => $job_date, 'user_id' => $user_id], [], ['key' => 'start_time', 'direction' => 'ASC']
        )->result_array();
        foreach ($task_history as $item){
            if (empty($item['start_time'])){
                continue;
            }
            $task = $this->db->get_where('tasks', ['id' => $item['task_id']])->row();
            $project = $this->db->get_where('projects', ['id' => $task->project_id])->row();
            $time_sheet_data[] = [
                'item_id' => $item['id'],
                'start' => DateTime::createFromFormat('H:i:s', $item['start_time'])->format('g:i A'),
                'end' => DateTime::createFromFormat('H:i:s', $item['end_time'])->format('g:i A'),
                'type' => 'work',
                'jobId' => '#'.$item['task_id'],
                'project' => "[$project->title] - [{$item['task_id']}] - $task->title",
            ];
        }
        return $time_sheet_data;
    }


    // get monthly overview calendar
    public function get_monthly_overview_calendar($user_id, $month_year) {
        $month_year = DateTime::createFromFormat('m-Y', $month_year)->format('Y-m');

        // get task report
        $this->db->select('time_taken, job_date');
        $this->db->from('task_assign');
        $this->db->where('user_id', $user_id);
        $this->db->where('time_taken!=', null);
        $this->db->where('time_taken!=', '00:00:00');
        $this->db->like('job_date', $month_year);
        $task_report = $this->db->get()->result_array();
        $task_report_array = [];

        foreach ($task_report as $task_report_item){
            $day = (int) DateTime::createFromFormat('Y-m-d', $task_report_item['job_date'])->format('d');
            $task_report_array[$day][] = $task_report_item['time_taken'];
        }

        $task_duration = [];
        foreach ($task_report_array as $day => $item){
            if (count($item)){
                $task_duration[$day] = add_duration($item, 'seconds');
            }
        }

        // get work attendance
        $this->db->select('work_status, job_date');
        $this->db->from('work_assign');
        $this->db->where('user_id', $user_id);
        $this->db->like('job_date', $month_year);
        $attendance = $this->db->get()->result_array();
        $attendance_array = [];
        foreach ($attendance as $attendance_item){
            $day = (int) DateTime::createFromFormat('Y-m-d', $attendance_item['job_date'])->format('d');
            $attendance_array[$day] = $attendance_item['work_status'];
        }

        // get month day array
        list($year, $month) = explode('-', $month_year);
        $numDays = cal_days_in_month(CAL_GREGORIAN, $month, $year);

        $month_days = [];
        for ($day = 1; $day <= $numDays; $day++) {
            $month_days[] = (int) $day;
        }

        // calculate monthly calendar
        $work_calendar = [
            'updated' => [],
            'partially' => [],
            'not_updated' => [],
            'not_applicable' => [],
        ];
        
        $current_day = (int) date('d');
        $current_month = date('Y-m');
        
        foreach ($month_days as $day){
            // check if attendance
            if($current_month == $month_year && $day > $current_day){
                $work_calendar['not_applicable'][] = $day;
            }else{
                if (isset($attendance_array[$day]) && $attendance_array[$day] == 'working'){
                    if (isset($task_duration[$day])){
                        if ($task_duration[$day] < 25200){
                            $work_calendar['partially'][] = $day;
                        }else{
                            $work_calendar['updated'][] = $day;
                        }
                    }else{
                        $work_calendar['not_updated'][] = $day;
                    }
                }else{
                    $work_calendar['not_applicable'][] = $day;
                }
            }
            
        }

        return $work_calendar;
    }

}
