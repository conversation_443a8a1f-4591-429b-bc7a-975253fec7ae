<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Project_schedule_m extends MY_Model
{
    protected string $_table_name = 'project_schedule';
    function __construct() {
        parent::__construct();
    }

    public function get_project_schedule($start_date, $end_date) {
        $project_schedule_data = [];
        $project_schedule = $this->get(['date >=' => $start_date, 'date <=' => $end_date])->result_array();

        foreach ($project_schedule as $item){
            $project_schedule_data[$item['project_id']][] = $item;
        }
        return $project_schedule_data;
    }



}
