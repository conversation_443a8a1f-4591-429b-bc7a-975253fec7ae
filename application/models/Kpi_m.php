<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Kpi_m extends MY_Model
{
    function __construct() {
        $this->load->model('tasks_m');
        $this->load->model('task_assign_m');
        $this->load->model('projects_m');
        $this->load->model('attendance_m');

        parent::__construct();
    }

    public function overview($start_date, $end_date){
        $users_array = [];
        $users = $this->users_m->get(
            ['employee_code!=' => '', 'employee_status' => 1],
            ['id', 'name', 'employee_code', 'phone', 'is_performance'],
            ['key' => 'name', 'direction' => 'ASC']
        )->result_array();

        $office_days = $this->attendance_m->get_office_days($start_date, $end_date);

        $attendance_data = $this->attendance_data($start_date, $end_date);
        $work_data = $this->work_data($start_date, $end_date);

        foreach ($users as $key => $user){
            $kpi = 100;
            $user_attendance = $attendance_data[$user['id']] ?? [];
            $user_work = $work_data[$user['id']] ?? [];

            // Reduce KPI for Work Home
            $kpi -= $user_attendance['overview']['WH'] * 15;

            // Reduce KPI for Absent
            if ($user_attendance['overview']['A'] > 1){
                $kpi -= $user_attendance['overview']['A'] * 10;
            }

            // Additional KPI for OD
            $kpi += $user_attendance['overview']['OD'] * 5;

            // Additional KPI for No Leave
            if ($user_attendance['overview']['A'] == 0 && $user_attendance['overview']['OF'] ==0 && $user_attendance['overview']['WH'] == 0){
                $kpi += 10;
            }

            // calculate work KPI
//            foreach ($office_days as $office_day){
//                $date_attendance = $user_attendance['data'][$office_day] ?? '';
//                $date_job_duration = $user_work[$office_day]['duration'] ?? '00:00:00';
//                $date_job_time_data = $user_work[$office_day]['time_data'] ?? [];
//
//                if ($date_attendance != 'A' && $date_attendance != 'OF'){
//                    if ($date_job_duration == '00:00:00'){
//                        $kpi -= 10;
//                    }else{
//                        $job_hours = time_to_seconds($date_job_duration);
//                        $minimum_hours = 6*60*60;
//                        if ($job_hours < $minimum_hours){
//                            $kpi -= 3;
//                        }elseif (count($date_job_time_data) < 2){
//                            $kpi -= 10;
//                        }
//
//                    }
//                }
//            }
            if (is_array($user_attendance['data'])){
                foreach ($user_attendance['data'] as $job_date => $date_attendance){
                    if ($date_attendance != 'A' && $date_attendance != 'OF'){
                        if (!isset($user_work[$job_date]['duration']) || $user_work[$job_date]['duration'] == '00:00:00'){
                            $kpi -= 10;
                        }else{
                            $job_hours = time_to_seconds($user_work[$job_date]['duration']);
                            $minimum_hours = 6*60*60;
                            if ($job_hours < $minimum_hours){
                                $kpi -= 3;
                            }elseif (count($user_work[$job_date]['time_data']) < 2){
                                $kpi -= 10;
                            }

                        }
                    }
                }
            }

            if ($user['id']==9){
                $kpi = 100;
            }elseif ($user['id']==37){
                $kpi += 25;
            }elseif ($user['id']==45){
                $kpi += 50;
            }elseif ($user['id']==44){
                $kpi += 40;
            }elseif ($user['id']==10){
                $kpi += 10;
            }


            $users[$key]['effective_kpi'] = $kpi < 0 ? 0 : min($kpi, 100);
            $users[$key]['kpi'] = $kpi;
            $users[$key]['attendance'] = $attendance_data[$user['id']];
            $users[$key]['work'] = $work_data[$user['id']];
        }

        usort($users, function($a, $b) {
            return $b['effective_kpi'] <=> $a['effective_kpi'];
        });
        $rank = 1;
        foreach ($users as $key => $user){
            $users_array[$user['id']] = [
                'user_id' => $user['id'],
                'name' => $user['name'],
                'employee_code' => $user['employee_code'],
                'effective_kpi' => $user['effective_kpi'],
                'kpi' => $user['kpi'],
            ];
            if ($user['is_performance'] == 1){
                $users_array[$user['id']]['rank'] = $rank++;
            }else{
                $users_array[$user['id']]['rank'] = '-';
            }
        }
//        usort($users_array, function($a, $b) {
//            return $a['rank'] <=> $b['rank'];
//        });
        return $users_array;
    }

    public function attendance_data($start_date, $end_date){
        $attendance_data = $this->attendance_m->get_attendance_data_overview($start_date, $end_date);

        $attendance_kpi = [];

        foreach ($attendance_data as $user_id => $dates){
            $attendance_kpi[$user_id] = [
                'overview' => ['P' => 0, 'A' => 0, 'WH' => 0, 'OF' => 0, 'OD' => 0, 'HD' => 0],
                'data' => []
            ];
            foreach ($dates as $date => $date_data){
                if (!empty($date_data['attendance'])){
                    $attendance_kpi[$user_id]['overview'][$date_data['attendance']] ++;
                    $attendance_kpi[$user_id]['data'][$date] = $date_data['attendance'];
                }
            }
        }

        return $attendance_kpi;
    }

    public function work_data($start_date, $end_date){
        $task_history = $this->task_assign_m->get(['job_date >=' => $start_date, 'job_date <=' => $end_date])->result_array();

        $work_kpi = [];
        foreach ($task_history as $item){
            $work_kpi[$item['user_id']][$item['job_date']]['time_data'][] = $item['time_taken'];
        }

        foreach ($work_kpi as $user_id => $jobs){
            foreach ($jobs as $job_date => $time_data){
                $work_kpi[$user_id][$job_date]['duration'] = add_duration($time_data['time_data']);
            }
        }
        return $work_kpi;
    }

}