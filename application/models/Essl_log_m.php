<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Essl_log_m extends MY_Model
{
    protected string $_table_name = 'essl_log';
    function __construct() {
        parent::__construct();
        $this->load->model('users_m');
        $this->load->model('attendance_m');
        $this->load->model('time_log_m');
    }

    public function get_data($start_date, $end_date, $user_id = 0){
        $employees = $this->get_active_employees();
        if ($user_id > 0){
            $this->db->where('user_id', $user_id);
        }


    }

    public function get_active_employees(){
        $users = $this->users_m->get(
            ['employee_status' => 1, 'is_employee' => 1],
            ['id as user_id', 'employee_code', 'name']
        )->result_array();

        $employees = [];
        foreach ($users as $user){
            $employees[$user['user_id']] = $user;
        }
        return $employees;
    }

    public function generate_attendance($start_date, $end_date){
        $punching_log = $this->get(
            ['log_date >=' => $start_date, 'log_date <=' => $end_date]
        )->result_array();

        $employees = $this->get_logged_employees($start_date, $end_date);
        $logged_days = $this->get_logged_dates($start_date, $end_date);

        $attendance_data = [];
        foreach($punching_log as $log){
            if (in_array($log['user_id'],$employees) && in_array($log['log_date'], $logged_days)){
                $attendance_data[$log['log_date']][$log['user_id']]['attendance'] = $log['attendance'];
                $attendance_data[$log['log_date']][$log['user_id']]['time_in'] = $log['time_in'];
                $attendance_data[$log['log_date']][$log['user_id']]['time_out'] = $log['time_out'];
                $punch_records = json_decode($log['punch_records'], true);
                $log_records = [];

                if (count($punch_records)){
                    foreach ($punch_records as $record){
                        if (!empty($record)){
                            $record = explode(':', $record);
                            $log_records[] = [
                                'time' => $record[0].':'.$record[1].':00',
                                'type' => $record[2]
                            ];
                        }
                    }
                }
                $attendance_data[$log['log_date']][$log['user_id']]['punch_records'] = $log_records;
            }

        }
        return $attendance_data;
    }

    public function push_attendance($start_date, $end_date){
        $attendance_data = $this->generate_attendance($start_date, $end_date);
        $push_data = [];

        foreach ($attendance_data as $date => $users){
            foreach ($users as $user_id => $user_attendance){
                $attendance = $this->attendance_m->get(['date' => $date, 'user_id' => $user_id]);
                if (!($attendance->num_rows() > 0)){
                    $attendance = [
                        'user_id' => $user_id,
                        'date' => $date,
                        'attendance' => $user_attendance['attendance'],
                        'time_in' => $user_attendance['time_in'],
                        'time_out' => $user_attendance['time_out'],
                        'publish_status' => 1,
                        'created_by' => get_user_id(),
                        'updated_by' => get_user_id(),
                        'created_on' => date('Y-m-d H:i:s'),
                        'updated_on' => date('Y-m-d H:i:s'),
                    ];
                    $this->attendance_m->insert($attendance);

                    $push_data[] = $attendance;
                }
                // push time log
                $this->push_time_log($date, $user_id, $user_attendance);
            }
        }
        return $push_data;
    }

    public function push_time_log($log_date, $user_id, $user_attendance){
        $time_logs = $user_attendance['punch_records'];
        $this->time_log_m->delete(['user_id' => $user_id, 'log_date' => $log_date]);

        $punch_type = 1;
        $last_punch_time = [];

        foreach($time_logs as $key => $time_log){
            if (in_array($time_log['time'], $last_punch_time)){
                continue;
            }
            $time_log_data = [
                'user_id' => $user_id,
                'log_date' => $log_date,
                'log_time' => $time_log['time'],
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            if ($time_log['time'] == $user_attendance['time_in']){ //time in checking
                $time_log_data['log_type_id'] = 1;
                $time_log_data['type'] = 1;
            }elseif ($time_log['time'] == $user_attendance['time_out']){ //time out checking
                $time_log_data['log_type_id'] = 8;
                $time_log_data['type'] = 0;
            }else{

                $time_log_data['log_type_id'] = $time_log['type'] == 'in' ? 11 : 12; //other punch in and punch out
                $time_log_data['type'] = $punch_type;
            }

            if ($user_id == 7 && $log_date =='2024-04-13'){
                log_message('error', json_encode($time_log, JSON_PRETTY_PRINT));
                log_message('error', json_encode($time_log_data, JSON_PRETTY_PRINT));
            }
            $this->time_log_m->insert($time_log_data);
            $punch_type = !$punch_type;

            $last_punch_time[] = $time_log['time'];
        }
    }

    public function get_logged_employees($start_date, $end_date){
        $employees = $this->db->select('user_id')
            ->from('essl_log')
            ->where('log_date >=', $start_date)
            ->where('log_date <=', $end_date)
            ->where('attendance', 'P')
            ->group_by('user_id')
            ->get()->result_array();
        return array_column($employees, 'user_id');
    }

    public function get_logged_dates($start_date, $end_date){
        $logged_dates = $this->db->select('log_date')
            ->from('essl_log')
            ->where('log_date >=', $start_date)
            ->where('log_date <=', $end_date)
            ->where('attendance', 'P')
            ->group_by('log_date')
            ->get()->result_array();
        return array_column($logged_dates, 'log_date');
    }

}
