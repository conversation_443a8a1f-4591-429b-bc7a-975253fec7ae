<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Employee_report_m extends MY_Model
{
    function __construct() {
        parent::__construct();
    }

    // over all report
    public function get_report($start_date, $end_date, $user_id, $is_detailed = 0){
        $attendance_report = $this->time_log_m->generate_overview_report($start_date, $end_date, $user_id);
        $project_report = $this->task_assign_m->get_employee_project_report($start_date, $end_date, $user_id, $is_detailed);
        return [
            'employee_report' => $attendance_report[0],
            'project_wise_report' => $project_report['project_wise_report'],
            'monthly_report' => $project_report['monthly_report']
        ];
    }


    // get employee work report
    public function get_employee_work_report($start_date, $end_date, $user_id){
        $project_report = $this->get_employee_work_report_monthly($start_date, $end_date, $user_id);
        return $project_report;
    }

    // get employee work report monthly
    public function get_employee_work_report_monthly($start_date, $end_date, $user_id){
        $work_report = $this->task_assign_m->get_work_report($start_date, $end_date, $user_id);
        $work_report_data = [];
        $daily_durations = [];

        foreach($work_report as $work) {
            $job_month = date('Y-m', strtotime($work['job_date']));
            $job_date = $work['job_date'];
            
            // Initialize arrays if not set
            if (!isset($work_report_data[$job_month][$job_date])) {
                $work_report_data[$job_month][$job_date] = [
                    'time_taken' => '0 minutes',
                    'job_duration' => '00:00:00',
                    'tasks' => []
                ];
            }
            if (!isset($daily_durations[$job_date])) {
                $daily_durations[$job_date] = 0;
            }
            $work_report_data[$job_month]['job_duration'] = '';
            $work_report_data[$job_month]['time_taken'] = '';
            $work_report_data[$job_month]['days'][$job_date]['tasks'][$work['task_id']]['title'] = $work['task_title'];

            // Format time range
            $time_range = $this->format_time_range($work['start_time'], $work['end_time']);
            
            // Add to daily total minutes
            if (!empty($work['time_taken'])) {
                list($hours, $minutes) = explode(':', $work['time_taken']);
                $daily_durations[$job_date] += ((int)$hours * 60) + (int)$minutes;
            }

            $history = [    
                'start_time' => $time_range['start_time'],
                'end_time' => $time_range['end_time'],
                'time_taken' => $this->format_time_to_readable($work['time_taken']),
                'duration' => $work['time_taken'],
                'remarks' => $work['remarks']
            ];
            $work_report_data[$job_month]['days'][$job_date]['tasks'][$work['task_id']]['history'][] = $history;
        }

        // Calculate final job durations after processing all tasks
        foreach ($daily_durations as $date => $total_minutes) {
            $job_month = date('Y-m', strtotime($date));
            $durations = $this->format_duration_from_minutes($total_minutes);
            
            $work_report_data[$job_month][$date] = array_merge(
                $work_report_data[$job_month][$date],
                $durations
            );
        }

        // Calculate monthly totals
        foreach ($work_report_data as $month => $month_data) {
            $monthly_total_minutes = 0;
            foreach ($month_data as $date => $day_data) {
                if (isset($day_data['job_duration'])) {
                    list($hours, $minutes) = explode(':', $day_data['job_duration']);
                    $monthly_total_minutes += ((int)$hours * 60) + (int)$minutes;
                }
            }
            $monthly_durations = $this->format_duration_from_minutes($monthly_total_minutes);
            $work_report_data[$month]['job_duration'] = $monthly_durations['job_duration'];
            $work_report_data[$month]['time_taken'] = $monthly_durations['time_taken'];
        }

        return $work_report_data;
    }

    private function format_time_to_readable($time_taken) {
        if (empty($time_taken)) {
            return '0 minutes';
        }

        list($hours, $minutes) = explode(':', $time_taken);
        $hours = (int)$hours;
        $minutes = (int)$minutes;
        
        if ($hours > 0) {
            $readable = $hours . ' hour' . ($hours > 1 ? 's' : '');
            if ($minutes > 0) {
                $readable .= ' ' . $minutes . ' minute' . ($minutes > 1 ? 's' : '');
            }
            return $readable;
        }
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '');
    }

    private function calculate_total_duration($time_taken_array) {
        $total_minutes = 0;
        foreach ($time_taken_array as $time_taken) {
            if (!empty($time_taken)) {
                list($hours, $minutes) = explode(':', $time_taken);
                $total_minutes += ((int)$hours * 60) + (int)$minutes;
            }
        }
        return $total_minutes;
    }

    private function format_duration_from_minutes($total_minutes) {
        $hours = floor($total_minutes / 60);
        $minutes = $total_minutes % 60;
        
        return [
            'job_duration' => sprintf('%02d:%02d:00', $hours, $minutes),
            'time_taken' => $hours > 0 
                ? $hours . ' hour' . ($hours > 1 ? 's' : '') . ($minutes > 0 ? ' ' . $minutes . ' minute' . ($minutes > 1 ? 's' : '') : '')
                : $minutes . ' minute' . ($minutes > 1 ? 's' : '')
        ];
    }

    private function format_time_range($start_time, $end_time) {
        if (!empty($start_time) && !empty($end_time)) {
            return [
                'start_time' => DateTime::createFromFormat('H:i:s', $start_time)->format('g:i A'),
                'end_time' => DateTime::createFromFormat('H:i:s', $end_time)->format('g:i A')
            ];
        }
        return ['start_time' => '', 'end_time' => ''];
    }
}