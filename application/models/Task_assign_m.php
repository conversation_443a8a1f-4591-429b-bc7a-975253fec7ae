<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Task_assign_m extends MY_Model
{
    protected string $_table_name = 'task_assign';
    function __construct() {
        parent::__construct();
    }

    public function get_employee_total_job_duration($start_date, $end_date, $user_id) {
        $this->db->select('time_taken');
        $this->db->where('job_date >=', $start_date);
        $this->db->where('job_date <=', $end_date);
        $this->db->where('user_id', $user_id);
        $task_status = $this->get()->result_array();
        $time_taken_array = array_column($task_status, 'time_taken');
        $time_taken_seconds = add_duration($time_taken_array, 'seconds');
        return $time_taken_seconds;
    }

    public function get_employee_project_report($start_date, $end_date, $user_id, $is_detailed = 0){
        $this->db->select('task_assign.time_taken, task_assign.job_date, task_assign.remarks, tasks.project_id, tasks.title, task_assign.start_time, task_assign.end_time');
        $this->db->from('task_assign');
        $this->db->join('tasks', 'tasks.id = task_assign.task_id');
        $this->db->where('task_assign.job_date >=', $start_date);
        $this->db->where('task_assign.job_date <=', $end_date);
        $this->db->where('task_assign.user_id', $user_id);
        $this->db->where('task_assign.time_taken <', '06:00:00');
        $task_status = $this->db->get()->result_array();
        $time_taken_array = [];
        $total_time_taken = [];

        $monthly_array = [];

        foreach ($task_status as $task) {
            $time_taken_array[$task['project_id']][] = $task['time_taken'];
            $total_time_taken[] = $task['time_taken'];
            $month = DateTime::createFromFormat('Y-m-d', $task['job_date'])->format('m-Y');
            $monthly_array[$month][$task['project_id']][] = $task;
        }

        $total_time_taken = add_duration($total_time_taken, 'seconds');
        $project_time_taken = [];
        $projects = $this->projects_m->get(null, ['id', 'title', 'project_type'])->result_array();
        $projects = array_column($projects, 'title', 'id');
        foreach ($time_taken_array as $project_id => $time_taken_arr) {
            if(!empty($start_time) && !empty($end_time)){
                $start_time = DateTime::createFromFormat('H:i:s', $task['start_time'])->format('g:i a');
                $end_time = DateTime::createFromFormat('H:i:s', $task['end_time'])->format('g:i a');
            }

            if (count($time_taken_arr)){
                $project_time_taken[] = [
                    'project_id' => $project_id,
                    'project_name' => $projects[$project_id],
                    'duration_seconds' => add_duration($time_taken_arr, 'seconds'),
                    'percentage' => add_duration($time_taken_arr, 'seconds')/$total_time_taken*100,
                    'duration' => get_time_from_seconds(add_duration($time_taken_arr, 'seconds')),
                    'start_time' => $start_time ?? '',
                    'end_time' => $end_time ?? ''
                ];
            }else{
                $project_time_taken[] = [
                    'project_id' => $project_id,
                    'project_name' => $projects[$project_id],
                    'duration_seconds' => 0,
                    'percentage' => 0,
                    'duration' => '',
                    'start_time' => '',
                    'end_time' => ''
                ];
            }
        }
        usort($project_time_taken, function($a, $b) {
            return $b['duration_seconds'] <=> $a['duration_seconds'];
        });
        return ['monthly_report' => $monthly_array, 'project_wise_report' => $project_time_taken];
    }
    
    public function get_employee_total_job_duration_performance($start_date, $end_date, $user_id) {
        $this->db->select('time_taken');
        $this->db->where('job_date >=', $start_date);
        $this->db->where('job_date <=', $end_date);
        $this->db->where('user_id', $user_id);
        $this->db->where('time_taken <', '05:00:00');
        $task_status = $this->get()->result_array();

        $time_taken_array = array();

        foreach ($task_status as $task) {
            $job_date_8_05_pm = strtotime($task['job_date'] . ' 21:05:00');
            $updated_on_time = strtotime($task['updated_on']);

            if ($updated_on_time <= $job_date_8_05_pm) {
                $time_taken_array[] = $task['time_taken'];
            }else{
                $time_taken_array[] = $task['time_taken'];
            }
        }

        $time_taken_seconds = add_duration($time_taken_array, 'seconds');
        return $time_taken_seconds;
    }

    public function get_employee_total_job_duration_performance_ot($start_date, $end_date, $user_id) {
        $this->db->select('time_taken');
        $this->db->where('job_date >=', $start_date);
        $this->db->where('job_date <=', $end_date);
        $this->db->where('user_id', $user_id);
        $this->db->where('end_time >=', '18:30:00');
        $this->db->where('time_taken <', '12:00:00');
        $task_status = $this->get()->result_array();
        $time_taken_array = array_column($task_status, 'time_taken');
        $time_taken_seconds = add_duration($time_taken_array, 'seconds');
        return $time_taken_seconds;
    }

    public function is_task_period_overlap($user_id, $job_date, $start_time, $end_time) {
        $this->db->select('COUNT(*) as overlap_count');
        $this->db->from('task_assign');
        $this->db->where('user_id', $user_id);
        $this->db->where('job_date', $job_date);
        $this->db->group_start();
        $this->db->where("('$start_time' BETWEEN start_time AND end_time)", NULL, FALSE);
        $this->db->or_where("('$end_time' BETWEEN start_time AND end_time)", NULL, FALSE);
        $this->db->or_where("(start_time BETWEEN '$start_time' AND '$end_time')", NULL, FALSE);
        $this->db->or_where("(end_time BETWEEN '$start_time' AND '$end_time')", NULL, FALSE);
        $this->db->group_end();

        $query = $this->db->get();
        $result = $query->row();

        return $result->overlap_count > 0;
    }

    // get task report
    public function get_work_report($start_date, $end_date, $user_id){
        $this->db->select('task_assign.task_id, task_assign.time_taken, task_assign.job_date, task_assign.remarks, tasks.project_id, tasks.title as task_title, task_assign.start_time, task_assign.end_time');
        $this->db->from('task_assign');
        $this->db->join('tasks', 'tasks.id = task_assign.task_id');
        $this->db->where('task_assign.job_date >=', $start_date);
        $this->db->where('task_assign.job_date <=', $end_date);
        $this->db->where('task_assign.user_id', $user_id);
        $this->db->where('task_assign.time_taken <', '06:00:00');
        $task_status = $this->db->get()->result_array();
        return $task_status;
    }


}
