<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Tasks_m extends MY_Model
{
    protected string $_table_name = 'tasks';
    function __construct() {
        parent::__construct();
    }

    // get status wise count
    public function get_count($start_date, $end_date, $where = [], $project_id = 0, $user_id = 0){
        if($where['task_status'] != 'pending' && $where['task_status'] != 'assigned' && $where['task_status'] != 'on_hold'){
            $where['date(tasks.due_date) >='] = $start_date;
            $where['date(tasks.due_date) <='] = $end_date;
        }
        
        if ($project_id > 0){
            $where['tasks.project_id'] = $project_id;
        }
        if ($user_id > 0){
            $where['tasks.user_id'] = $user_id;
        }
        $count = parent::get($where, ['count(tasks.id) as item_count'])->row()->item_count ?? 0;
        // echo $this->db->last_query();
        return $count;
    }


    // get pending tasks
    public function get_user_pending_count(){
        $this->db->where('user_id', get_user_id());
        if (is_technical_support()){
            $this->db->where_in('task_status', ['assigned', 'testing']);
        }else{
            $this->db->where_in('task_status', ['assigned']);
        }
        return parent::get(null, ['count(tasks.id) as item_count'])->row()->item_count ?? 0;
    }

}
