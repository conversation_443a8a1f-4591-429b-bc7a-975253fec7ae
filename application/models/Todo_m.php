<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Todo_m extends MY_Model
{
    protected string $_table_name = 'todo';
    function __construct() {
        parent::__construct();
    }

    // get status wise count
    public function get_count($start_date, $end_date, $where = []){
        if (!is_super_admin() && !is_project_manager()) {
            $where['created_by'] = get_user_id();
        }
        $where['date(todo.due_date) >='] = $start_date;
        $where['date(todo.due_date) <='] = $end_date;
        return parent::get($where, ['count(todo.id) as item_count'])->row()->item_count ?? 0;
    }

    // get pending todo
    public function get_user_pending_count(){
        return parent::get(
            ['user_id' => get_user_id(), 'todo_status' => 'assigned'],
            ['count(todo.id) as item_count']
        )->row()->item_count ?? 0;
    }

}
