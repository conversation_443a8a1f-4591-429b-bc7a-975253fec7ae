<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Work_schedule_m extends MY_Model
{
    protected string $_table_name = 'work_schedule';
    function __construct() {
        parent::__construct();
    }

    public function get_work_schedule($start_date, $end_date) {
        $work_schedule_data = [];
        $work_schedule = $this->get(['date >=' => $start_date, 'date <=' => $end_date])->result_array();

        foreach ($work_schedule as $item){
            $work_schedule_data[$item['project_id']][] = $item;
        }
        return $work_schedule_data;
    }



}
