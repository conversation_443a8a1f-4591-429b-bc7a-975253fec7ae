<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Add_rating_columns_to_aptitude_candidates extends CI_Migration {

    public function up() {
        // Add rating_total column
        $this->dbforge->add_column('aptitude_candidates', [
            'rating_total' => [
                'type' => 'INT',
                'null' => TRUE,
                'comment' => 'Total rating score'
            ]
        ]);

        // Add rating_ai column
        $this->dbforge->add_column('aptitude_candidates', [
            'rating_ai' => [
                'type' => 'INT',
                'null' => TRUE,
                'comment' => 'AI-based rating score'
            ]
        ]);

        // Add rating_communication column
        $this->dbforge->add_column('aptitude_candidates', [
            'rating_communication' => [
                'type' => 'INT',
                'null' => TRUE,
                'comment' => 'Communication skills rating'
            ]
        ]);

        // Add remarks column
        $this->dbforge->add_column('aptitude_candidates', [
            'remarks' => [
                'type' => 'TEXT',
                'null' => TRUE,
                'comment' => 'General remarks about the candidate'
            ]
        ]);

        // Add suggested_questions column
        $this->dbforge->add_column('aptitude_candidates', [
            'suggested_questions' => [
                'type' => 'TEXT',
                'null' => TRUE,
                'comment' => 'Suggested interview questions'
            ]
        ]);

        // Add rating_interviewer column
        $this->dbforge->add_column('aptitude_candidates', [
            'rating_interviewer' => [
                'type' => 'INT',
                'null' => TRUE,
                'comment' => 'Rating given by interviewer'
            ]
        ]);

        // Add remarks_interviewer column
        $this->dbforge->add_column('aptitude_candidates', [
            'remarks_interviewer' => [
                'type' => 'TEXT',
                'null' => TRUE,
                'comment' => 'Remarks from the interviewer'
            ]
        ]);
    }

    public function down() {
        // Remove columns if migration is rolled back
        $this->dbforge->drop_column('aptitude_candidates', 'rating_total');
        $this->dbforge->drop_column('aptitude_candidates', 'rating_ai');
        $this->dbforge->drop_column('aptitude_candidates', 'rating_communication');
        $this->dbforge->drop_column('aptitude_candidates', 'remarks');
        $this->dbforge->drop_column('aptitude_candidates', 'suggested_questions');
        $this->dbforge->drop_column('aptitude_candidates', 'rating_interviewer');
        $this->dbforge->drop_column('aptitude_candidates', 'remarks_interviewer');
    }
}
