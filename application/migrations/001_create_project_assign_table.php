<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Create_project_assign_table extends CI_Migration {

    public function up() {
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => TRUE,
                'auto_increment' => TRUE
            ],
            'job_date' => [
                'type' => 'DATE',
                'null' => FALSE,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => FALSE,
            ],
            'project_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => FALSE,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'in_progress', 'completed', 'cancelled'],
                'default' => 'pending',
                'null' => FALSE,
            ],
            'remarks' => [
                'type' => 'TEXT',
                'null' => TRUE,
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => FALSE,
            ],
            'updated_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => TRUE,
            ],
            'created_on' => [
                'type' => 'DATETIME',
                'null' => FALSE,
            ],
            'updated_on' => [
                'type' => 'DATETIME',
                'null' => TRUE,
            ]
        ]);
        
        $this->dbforge->add_key('id', TRUE);
        $this->dbforge->add_key(['user_id', 'project_id', 'job_date']);
        
        $this->dbforge->create_table('project_assign');
    }

    public function down() {
        $this->dbforge->drop_table('project_assign');
    }
}
