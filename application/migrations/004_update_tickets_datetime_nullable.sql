-- Migration: Update tickets table datetime fields to allow NULL values
-- Date: <?= date('Y-m-d H:i:s') ?>

-- Change ticket_date and close_date to DATETIME NULL DEFAULT NULL
ALTER TABLE `tickets` CHANGE `ticket_date` `ticket_date` DATETIME NULL DEFAULT NULL;
ALTER TABLE `tickets` CHANGE `close_date` `close_date` DATETIME NULL DEFAULT NULL;

-- Update any existing '0000-00-00 00:00:00' values to NULL for better data integrity
UPDATE `tickets` SET `ticket_date` = NULL WHERE `ticket_date` = '0000-00-00 00:00:00';
UPDATE `tickets` SET `close_date` = NULL WHERE `close_date` = '0000-00-00 00:00:00';

-- Show table structure after changes
DESCRIBE `tickets`;
