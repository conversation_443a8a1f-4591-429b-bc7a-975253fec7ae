<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Add_rating_technical_column extends CI_Migration {

    public function up() {
        // Add rating_technical column
        $this->dbforge->add_column('aptitude_candidates', [
            'rating_technical' => [
                'type' => 'INT',
                'null' => TRUE,
                'comment' => 'Technical skills rating',
                'after' => 'rating_ai'  // Specify position after rating_ai column
            ]
        ]);
    }

    public function down() {
        // Remove column if migration is rolled back
        $this->dbforge->drop_column('aptitude_candidates', 'rating_technical');
    }
}
