-- Performance optimization for tickets table
-- Run this SQL to optimize performance for large datasets

-- Add indexes for better query performance
ALTER TABLE `tickets` ADD INDEX `idx_status` (`status`);
ALTER TABLE `tickets` ADD INDEX `idx_priority` (`priority`);
ALTER TABLE `tickets` ADD INDEX `idx_project_id` (`project_id`);
ALTER TABLE `tickets` ADD INDEX `idx_user_id` (`user_id`);
ALTER TABLE `tickets` ADD INDEX `idx_ticket_date` (`ticket_date`);
ALTER TABLE `tickets` ADD INDEX `idx_created_on` (`created_on`);

-- Composite indexes for common query patterns
ALTER TABLE `tickets` ADD INDEX `idx_status_priority` (`status`, `priority`);
ALTER TABLE `tickets` ADD INDEX `idx_project_status` (`project_id`, `status`);
ALTER TABLE `tickets` ADD INDEX `idx_user_status` (`user_id`, `status`);

-- Full-text search index for title and description (if using MyISAM or InnoDB with MySQL 5.6+)
-- Uncomment the following line if you want to enable full-text search
-- <PERSON><PERSON>R TABLE `tickets` ADD FULLTEXT(`title`, `description`);

-- Optimize table structure
OPTIMIZE TABLE `tickets`;

-- Show table status after optimization
SHOW TABLE STATUS LIKE 'tickets';
