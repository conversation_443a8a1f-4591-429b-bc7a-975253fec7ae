<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Add_estimated_time_to_tickets extends CI_Migration {

    public function up() {
        // Add estimated_time column to tickets table
        $this->dbforge->add_column('tickets', [
            'estimated_time' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => TRUE,
                'comment' => 'Estimated time in minutes',
                'after' => 'priority'
            ]
        ]);
    }

    public function down() {
        // Remove estimated_time column if migration is rolled back
        $this->dbforge->drop_column('tickets', 'estimated_time');
    }
}
